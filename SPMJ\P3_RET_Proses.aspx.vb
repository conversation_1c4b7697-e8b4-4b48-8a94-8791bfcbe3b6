﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm21
    Inherits System.Web.UI.Page

    Public x As String

    Public Sub Surat_RET(ByVal i As Int16)
        Dim <PERSON>, <PERSON><PERSON>ar, Alama<PERSON>, Poskod, Bandar, <PERSON><PERSON><PERSON>, <PERSON>_<PERSON>, <PERSON><PERSON>_<PERSON>, <PERSON><PERSON><PERSON>, Amaun As String
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select nama, jp.j_daftar, sm_alamat, isnull(sm_poskod,'') as 'sm_poskod', isnull(sm_bandar,'') as 'sm_bandar', isnull(dc_negeri,'') as 'dc_negeri', jp.j_daftar as 'no_pd', jp.nokp, jpa.apc_tkhresit, jpa.apc_noresit, jpa.apc_amaun from jt_penuh jp inner join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.apc_no=0 left outer join pn_negeri pn on jp.sm_negeri=pn.id_negeri where jp.nokp = '" & Tx_NoKP.Text & "' and jpa.apc_tahun= '" & Tx_Tahun.Text & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Nama = Rdr("Nama")
            Daftar = Rdr("j_daftar")
            NoPd = Rdr("No_Pd")
            Alamat = Rdr("sm_alamat")
            Poskod = Rdr("sm_poskod")
            Bandar = Rdr("sm_bandar")
            Negeri = Rdr("dc_negeri")
            No_Resit = Rdr("apc_noresit")
            Tkh_Resit = Rdr("apc_tkhresit")
            Amaun = Rdr("apc_amaun")
        Else
            Nama = ""
            Daftar = ""
            NoPd = ""
            Alamat = ""
            Poskod = ""
            Bandar = ""
            Negeri = ""
            No_Resit = ""
            Tkh_Resit = ""
            Amaun = ""
        End If
        Rdr.Close()
        Cn.Close()

        x = ""
        x += Header_Surat(1, 1)

        'Rujukan
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 11px;'><tr>"
        x += "<td style='width:68%;border: none; margin-left: 0px; font-family: Arial; font-size: 11px;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 11px; color:#151B8D;'> Your Ref : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 11px; color:#000000;'> </td>"
        x += "</tr><tr>"
        x += "<td style='width:68%;border: none; margin-left: 0px; font-family: Arial; font-size: 11px;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 11px; color:#151B8D;'> Our Ref &nbsp; : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 11px; color:#000000;'> N.B.M NO : " + NoPd + " </td>"
        x += "</tr><tr>"
        x += "<td style='width:68%;border: none; margin-left: 0px; font-family: Arial; font-size: 11px;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 11px; color:#151B8D;'> Date &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; :</td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 11px; color:#000000;'> " + Now.ToLongDateString + "</td>"
        x += "</tr></table>"

        'Penerima
        x += "<div style='font-family: Arial; font-size: 11pt;'>"
        x += "<br/>" & Nama & ""
        If Bandar = "" Then
            x += "<br/>" & Alamat & ""
            x += "<br/>" & Poskod & " " & Negeri
        Else
            x += "<br/>" & Alamat & ""
            x += "<br/>" & Poskod & " " & Bandar
            x += "<br/>" & Negeri & ""
        End If
        x += "</div>"

        x += "<br/>"
        x += "<div  style='font-family: Arial; font-size: 11pt;'>Sir/Madam,"
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 11pt;'><b>RETENTION OF NAME"
        x += "</b></div>"

        x += "<br/><div style='font-family: Arial; font-size: 11pt;'>"
        x += "This is to certify that your name has been retained in the Nurses Registration Regulation 1985 Part II 5.(1)"
        'Comment Original 04082020  - OSH 
        'If Daftar = 1 Then x += "(a)"
        'If Daftar = 2 Then x += "(c)"
        'If Daftar = 3 Then x += "(b)"

        'Fix 04082020 - OSH 
        If Daftar.Trim = "1" Then x += "(a)"
        If Daftar.Trim = "2" Then x += "(c)"
        If Daftar.Trim = "3" Then x += "(b)"
        x += " of the Malaysian Nurses Act 1950, for year ............................... as requested.</b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 11pt;'>"
        x += "2. &nbsp; &nbsp; &nbsp; &nbsp; Enclosed is the receipt no. " & No_Resit & " RM" & Amaun & " being the retention fee."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 11pt;'>"
        x += "Thank You."
        x += "</div>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 11pt;'>"
        x += "Yours Sincerely,"
        x += "</div>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "</br>"
        x += "<br/><div style='font-family: Arial; font-size: 11pt;'>"
        If i = 0 Then
            x += "<b> (DATO' HJH. FATHILAH BINTI HJ.ABD. WAHAB) </b>"
            x += "<br>Registar,"
            x += "<br>Nursing Board Malaysia"
            x += "<br>Ministry of Health Malaysia"
        Else
            x += "<b> (ZAINOORIAH DATO' HJ. ZAKARIA) </b>"
            x += "<br>Secretary,"
            x += "<br>Nursing Board Malaysia"
            x += "<br>Ministry of Health Malaysia"
        End If
        x += "</div>"

        x += Footer_Surat()

        'Response.Write(x)
        'Exit Sub

        Dim fileName As String = "Surat.doc"
        Response.Clear()
        Response.Buffer = True
        ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)
    End Sub

    Public Sub Reset()
        Session("PN_PINDA") = False
        Cb_Tempoh.Enabled = True : Tx_Tahun.Enabled = True : Tx_TkhResit.Enabled = True : Tx_Amaun.Enabled = True
        Tx_Tahun.Text = ""
        Tx_Tahun.Text = ""
        Tx_NoResit.Text = ""
        Tx_TkhResit.Text = ""
        Tx_Amaun.Text = ""
        Cb_Negara.SelectedIndex = 0
        Cb_Sebab.SelectedIndex = 0
        Tx_Catatan.Text = ""
        cmd_Simpan.Visible = False
        cmd_CetakDftr.Visible = False
        cmd_CetakSU.Visible = False
    End Sub

    Public Sub Isi_APC()
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)

        'Comment Original 13042020 - OSH 
        'Dim SQL As String = "select apc_tahun as 'TAHUN', case ret when 1 then 'RET' else stuff('0000',6-len(apc_no),len(apc_no),apc_no) end as 'NO. APC', isnull(CONVERT(varchar(12), apc_tkh, 103),'') as 'TARIKH APC', isnull(apc_amaun,0) 'AMAUN', dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR', isnull(ret_negara,0) 'NEGARA', isnull(ret_sebab,0) 'SEBAB', isnull(ret_catatan,' ') 'CATATAN', pta.alamat, pta.poskod, pta.bandar, png.dc_negeri, pta.tel, pta.fax, isnull(Apc_NoResit,'') as resit  from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & Session("NOKP") & "' order by apc_tahun desc, apc_tkh desc"

        'Add RET STATUS 13042020 - OSH 
        Dim SQL As String = "select apc_tahun as 'TAHUN', case ret when 1 then 'RET' else stuff('0000',6-len(apc_no),len(apc_no),apc_no) end as 'NO. APC', isnull(CONVERT(varchar(12), apc_tkh, 103),'') as 'TARIKH APC', isnull(apc_amaun,0) 'AMAUN', dc_amalan as 'TEMPAT AMALAN', case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' end as 'SEKTOR', isnull(ret_negara,0) 'NEGARA', isnull(ret_sebab,0) 'SEBAB', isnull(ret_catatan,' ') 'CATATAN', pta.alamat, pta.poskod, pta.bandar, png.dc_negeri, pta.tel, pta.fax, isnull(Apc_NoResit,'') as resit,  case jpa.apc_batal  when  '1' then 'BATAL' when '0' then 'AKTIF'end as 'STATUS'    from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on jpa.id_amalan = pta.id_amalan left outer join pn_negeri png on png.id_negeri = pta.negeri where jpa.nokp = '" & Session("NOKP") & "' order by apc_tahun desc, apc_tkh desc"

        Dim List_Adp As New SqlDataAdapter(Sql, Cn)

        List_Adp.Fill(List_Data, "jt_penuh_apc")
        Gd_APC.DataSource = List_Data.Tables("jt_penuh_apc")
        Gd_APC.DataBind()
        Session("PN_PINDA") = False
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        'Add verify login 06122019 -OSH 
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        Dim dt1 As DateTime

        Tx_Nama.Text = Session("NAMA")
        Tx_NoKP.Text = Session("NOKP")
        Tx_NoPd.Text = Session("NOPD")

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Add display registration number (ICT SUPPORT - #558249) 1302017 -OSH
        Cmd.CommandText = "select tkh_daftar from jt_penuh where nokp = '" & Session("NOKP") & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            'If Not IsDBNull(Rdr("tkh_daftar")) Then tkh_daftar.Text = Rdr("tkh_daftar") Else Tx_Tkh_Daftar.Text = ""

            'fixing date issue 20062018 - OSH
            If Not IsDBNull(Rdr("tkh_daftar")) Then dt1 = Rdr("tkh_daftar") : Tx_Tkh_Daftar.Text = dt1.ToString("dd'/'MM'/'yyyy") Else Tx_Tkh_Daftar.Text = ""
            Rdr.Close()
        End If

        'NEGARA
        Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara ORDER BY dc_NEGARA"
        Rdr = Cmd.ExecuteReader()
        Cb_Negara.Items.Clear()
        Cb_Negara.Items.Add("")
        While Rdr.Read
            Cb_Negara.Items.Add(Rdr(0))
            Cb_Negara.Items.Item(Cb_Negara.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'PENGEKALAN
        Cmd.CommandText = "SELECT Dc_Kekal, Id_Kekal FROM pn_pengekalan ORDER BY dc_kekal"
        Rdr = Cmd.ExecuteReader()
        Cb_Sebab.Items.Clear()
        Cb_Sebab.Items.Add("")
        While Rdr.Read
            Cb_Sebab.Items.Add(Rdr(0))
            Cb_Sebab.Items.Item(Cb_Sebab.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        Cn.Close()
        Isi_APC()
    End Sub

    Private Sub Gd_APC_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd_APC.RowCreated
        e.Row.Cells(0).Width = Unit.Pixel(50)
        e.Row.Cells(1).Width = Unit.Pixel(50)
        e.Row.Cells(5).Visible = False
        e.Row.Cells(8).Visible = False
        e.Row.Cells(9).Visible = False
        e.Row.Cells(10).Visible = False
        e.Row.Cells(11).Visible = False
        e.Row.Cells(12).Visible = False
        e.Row.Cells(13).Visible = False
        e.Row.Cells(14).Visible = False
        e.Row.Cells(15).Visible = False
        e.Row.Cells(16).Visible = False
        e.Row.Cells(17).Visible = False
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Height = Unit.Pixel(15)
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_APC_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd_APC.SelectedIndexChanged
        'Comment Original 04082020 - OSH
        Reset()
        cmd_Simpan.Visible = True
        Session("PN_PINDA") = True
        Cb_Tempoh.Enabled = False : Tx_Tahun.Enabled = False : Tx_TkhResit.Enabled = False : Tx_Amaun.Enabled = False        
        Tx_Tahun.Text = Gd_APC.SelectedRow.Cells(2).Text.Trim
        Tx_NoResit.Text = Replace(Gd_APC.SelectedRow.Cells(17).Text.Trim,"&nbsp;","")
        Tx_TkhResit.Text = Gd_APC.SelectedRow.Cells(4).Text.Trim
        Tx_Amaun.Text = Gd_APC.SelectedRow.Cells(5).Text.Trim
        If Gd_APC.SelectedRow.Cells(8).Text > 0 Then Cb_Negara.SelectedValue = Gd_APC.SelectedRow.Cells(8).Text.Trim
        If Gd_APC.SelectedRow.Cells(9).Text > 0 Then Cb_Sebab.SelectedValue = Gd_APC.SelectedRow.Cells(9).Text.Trim
        Tx_Catatan.Text = Replace(Gd_APC.SelectedRow.Cells(10).Text.Trim,"&nbsp;","")

    End Sub

    Protected Sub cmd_Simpan_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Simpan.Click
        If Tx_Tahun.Text = "" Then Tx_Tahun.Focus() : Exit Sub
        If Not IsNumeric(Tx_Tahun.Text) Then Tx_Tahun.Focus() : Exit Sub
        If Tx_NoResit.Text = "" Then Tx_NoResit.Focus() : Exit Sub
        If Not IsNumeric(Tx_Amaun.Text) Then Tx_Amaun.Focus() : Exit Sub
        'Add Check Field RON Process 20022017- OSH 
        If Tx_TkhResit.Text = "" Then Tx_TkhResit.Focus() : Exit Sub
        If Cb_Negara.SelectedIndex = 0 Then Cb_Negara.Focus() : Exit Sub
        If Cb_Sebab.SelectedIndex = 0 Then Cb_Sebab.Focus() : Exit Sub


        Dim i As Int16, SQL As String = ""
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Add Check Field RON Process 20022017- OSH 
        'If Tx_Tahun.Text.Trim = "" Then x += "Tahun RON,"
        'If Tx_NoResit.Text.Trim = "" Then x += "Nombor Resit RON, "
        'If Tx_TkhResit.Text.Trim = "" Then x += "Tarikh Resit RON, "
        'If Tx_Amaun.Text.Trim = "" Then x += "Jumlah Bayaran, "
        'If Cb_Negara.SelectedIndex = 0 Then x += "Negara, "
        'If Cb_Sebab.SelectedIndex = 0 Then x += "Sebab Pengekalan, "
        'If x.Trim = "" Then  Else x = Mid(x, 1, Len(x) - 2) : Msg(Me, "Sila isikan maklumat berikut:" + x) : Exit Sub


        If Session("PN_PINDA") = True Then
            SQL = "update jt_penuh_apc set apc_noresit='" & Tx_NoResit.Text & "', ret_negara = '" & Cb_Negara.SelectedItem.Value & "', ret_sebab= '" & Cb_Sebab.SelectedItem.Value & "', ret_catatan='" & Tx_Catatan.Text & "' where apc_tahun = '" & Tx_Tahun.Text & "' and nokp='" & Tx_NoKP.Text & "'"
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Isi_APC()
            Msg(Me, "Rekod Telah Dikemaskini...")
            Exit Sub
        End If

        'Comment Original 27042020 - OSH
        'SQL += "select apc_tahun from jt_penuh_apc where nokp='" & Tx_NoKP.Text.Trim & "'"

        'Improve query exclude cancel APC records 27042020 - OSH 
        SQL += "select apc_tahun from jt_penuh_apc where nokp='" & Tx_NoKP.Text.Trim & "' and apc_batal not in ('1')"
        Cmd.CommandText = SQL
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If Rdr(0) = Tx_Tahun.Text Then
                Msg(Me, "Rekod Pengekalan Nama Telah Ada!")
                Rdr.Close()
                Exit Sub
            End If
        End While
        Rdr.Close()

        For i = 0 To Cb_Tempoh.SelectedIndex
            SQL += "insert jt_penuh_apc (j_daftar, nokp, apc_tahun, apc_no, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_noresit, apc_lwt_tkhresit, apc_lwt_amaun, id_amalan, ret_negara, ret_sebab, ret_catatan, ret, log_id, log_tkh) select " & _
                    "(select j_daftar from jt_penuh where nokp='" & Tx_NoKP.Text.Trim & "')," & _
                    "'" & Tx_NoKP.Text.Trim & "'," & _
                    "" & CInt(Tx_Tahun.Text) + i & "," & _
                    "0," & _
                    "getdate()," & _
                    "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
                    "'" & Tx_NoResit.Text & "'," & _
                    "" & CInt(Tx_Amaun.Text) & "," & _
                    "null," & _
                    "null," & _
                    "null," & _
                    "null," & _
                    "'" & Cb_Negara.SelectedItem.Value & "'," & _
                    "'" & Cb_Sebab.SelectedItem.Value & "', " & _
                    "'" & Tx_Catatan.Text & "'," & _
                    "1," & _
                    "'" & Session("Id_PG") & "', " & _
                    "getdate(); "
        Next
        If SQL = "" Then Exit Sub
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Cn.Close()
            cmd_Simpan.Visible = False
            Isi_APC()
            Msg(Me, "Rekod Telah Disimpan...")
        Catch ex As Exception
            Cn.Close()
            Msg(Me, ex.Message)
        End Try
    End Sub

    Protected Sub cmd_CetakDftr_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_CetakDftr.Click
        Surat_RET(0)
    End Sub

    Protected Sub cmd_CetakSU_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_CetakSU.Click
        Surat_RET(1)
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As EventArgs)
        Session("PN_PINDA") = True
    End Sub    
End Class