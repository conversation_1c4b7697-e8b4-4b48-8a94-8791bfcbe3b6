﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm20
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', cast(jpa2.apc_no as varchar(6)) + '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN HADAPAN', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.ret = 0 and jpa.apc_tahun = year(getdate()) left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_tahun = year(getdate())+1 left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_tahun = year(getdate()) where " & X & " order by jp.nama"
        Tb = "jt_penuh"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        Gd.DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        'Add verify login 06122019 -OSH 
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        Gd.Height = Unit.Pixel(1)
        e.Row.Cells(0).Width = Unit.Pixel(30)
        e.Row.Cells(4).Width = Unit.Pixel(60)
        e.Row.Cells(5).Width = Unit.Pixel(60)
        e.Row.Cells(6).Width = Unit.Pixel(60)
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub cmd_Cari0_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari0.Click
        Cari("jp.nokp like '" & Tx_NoKP.Text & "%'")
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Cari("nama like '" & Tx_Nama.Text & "%'")
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Cari("nopd like '" & Tx_NoPd.Text & "%'")
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Session("NAMA") = Gd.SelectedRow.Cells(2).Text
        Session("NOKP") = Gd.SelectedRow.Cells(3).Text
        Session("NOPD") = Gd.SelectedRow.Cells(4).Text
        'Comment Original 06082020 - OSH 
        'Response.Redirect("P3_RET_Proses.aspx")

        'Add Cancel Process 06082020 - OSH 
        Response.Redirect("P3_RET_Proses2.aspx")

    End Sub
End Class