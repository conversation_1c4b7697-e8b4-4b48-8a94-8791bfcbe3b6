<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:tns="http://tempuri.org/" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" name="Service1" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsp:Policy wsu:Id="BasicHttpsBinding_IService1_policy">
    <wsp:ExactlyOne>
      <wsp:All>
        <sp:TransportBinding xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
          <wsp:Policy>
            <sp:TransportToken>
              <wsp:Policy>
                <sp:HttpsToken RequireClientCertificate="false" />
              </wsp:Policy>
            </sp:TransportToken>
            <sp:AlgorithmSuite>
              <wsp:Policy>
                <sp:Basic256 />
              </wsp:Policy>
            </sp:AlgorithmSuite>
            <sp:Layout>
              <wsp:Policy>
                <sp:Strict />
              </wsp:Policy>
            </sp:Layout>
          </wsp:Policy>
        </sp:TransportBinding>
      </wsp:All>
    </wsp:ExactlyOne>
  </wsp:Policy>
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://www.mycpd2.moh.gov.my/ws/Service1.svc?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://www.mycpd2.moh.gov.my/ws/Service1.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://www.mycpd2.moh.gov.my/ws/Service1.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/WcfServiceMyCPD" />
      <xsd:import schemaLocation="http://www.mycpd2.moh.gov.my/ws/Service1.svc?xsd=xsd3" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IService1_UserInfo_InputMessage">
    <wsdl:part name="parameters" element="tns:UserInfo" />
  </wsdl:message>
  <wsdl:message name="IService1_UserInfo_OutputMessage">
    <wsdl:part name="parameters" element="tns:UserInfoResponse" />
  </wsdl:message>
  <wsdl:message name="IService1_PointByCategory_InputMessage">
    <wsdl:part name="parameters" element="tns:PointByCategory" />
  </wsdl:message>
  <wsdl:message name="IService1_PointByCategory_OutputMessage">
    <wsdl:part name="parameters" element="tns:PointByCategoryResponse" />
  </wsdl:message>
  <wsdl:message name="IService1_CumulativePoint_InputMessage">
    <wsdl:part name="parameters" element="tns:CumulativePoint" />
  </wsdl:message>
  <wsdl:message name="IService1_CumulativePoint_OutputMessage">
    <wsdl:part name="parameters" element="tns:CumulativePointResponse" />
  </wsdl:message>
  <wsdl:message name="IService1_ActualPoint_InputMessage">
    <wsdl:part name="parameters" element="tns:ActualPoint" />
  </wsdl:message>
  <wsdl:message name="IService1_ActualPoint_OutputMessage">
    <wsdl:part name="parameters" element="tns:ActualPointResponse" />
  </wsdl:message>
  <wsdl:message name="IService1_HcpList_InputMessage">
    <wsdl:part name="parameters" element="tns:HcpList" />
  </wsdl:message>
  <wsdl:message name="IService1_HcpList_OutputMessage">
    <wsdl:part name="parameters" element="tns:HcpListResponse" />
  </wsdl:message>
  <wsdl:message name="IService1_PointByYear_InputMessage">
    <wsdl:part name="parameters" element="tns:PointByYear" />
  </wsdl:message>
  <wsdl:message name="IService1_PointByYear_OutputMessage">
    <wsdl:part name="parameters" element="tns:PointByYearResponse" />
  </wsdl:message>
  <wsdl:portType name="IService1">
    <wsdl:operation name="UserInfo">
      <wsdl:input wsaw:Action="http://tempuri.org/IService1/UserInfo" message="tns:IService1_UserInfo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IService1/UserInfoResponse" message="tns:IService1_UserInfo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="PointByCategory">
      <wsdl:input wsaw:Action="http://tempuri.org/IService1/PointByCategory" message="tns:IService1_PointByCategory_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IService1/PointByCategoryResponse" message="tns:IService1_PointByCategory_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CumulativePoint">
      <wsdl:input wsaw:Action="http://tempuri.org/IService1/CumulativePoint" message="tns:IService1_CumulativePoint_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IService1/CumulativePointResponse" message="tns:IService1_CumulativePoint_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ActualPoint">
      <wsdl:input wsaw:Action="http://tempuri.org/IService1/ActualPoint" message="tns:IService1_ActualPoint_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IService1/ActualPointResponse" message="tns:IService1_ActualPoint_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="HcpList">
      <wsdl:input wsaw:Action="http://tempuri.org/IService1/HcpList" message="tns:IService1_HcpList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IService1/HcpListResponse" message="tns:IService1_HcpList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="PointByYear">
      <wsdl:input wsaw:Action="http://tempuri.org/IService1/PointByYear" message="tns:IService1_PointByYear_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IService1/PointByYearResponse" message="tns:IService1_PointByYear_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IService1" type="tns:IService1">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="UserInfo">
      <soap:operation soapAction="http://tempuri.org/IService1/UserInfo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PointByCategory">
      <soap:operation soapAction="http://tempuri.org/IService1/PointByCategory" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CumulativePoint">
      <soap:operation soapAction="http://tempuri.org/IService1/CumulativePoint" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ActualPoint">
      <soap:operation soapAction="http://tempuri.org/IService1/ActualPoint" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="HcpList">
      <soap:operation soapAction="http://tempuri.org/IService1/HcpList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PointByYear">
      <soap:operation soapAction="http://tempuri.org/IService1/PointByYear" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="BasicHttpsBinding_IService1" type="tns:IService1">
    <wsp:PolicyReference URI="#BasicHttpsBinding_IService1_policy" />
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="UserInfo">
      <soap:operation soapAction="http://tempuri.org/IService1/UserInfo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PointByCategory">
      <soap:operation soapAction="http://tempuri.org/IService1/PointByCategory" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CumulativePoint">
      <soap:operation soapAction="http://tempuri.org/IService1/CumulativePoint" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ActualPoint">
      <soap:operation soapAction="http://tempuri.org/IService1/ActualPoint" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="HcpList">
      <soap:operation soapAction="http://tempuri.org/IService1/HcpList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PointByYear">
      <soap:operation soapAction="http://tempuri.org/IService1/PointByYear" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="Service1">
    <wsdl:port name="BasicHttpBinding_IService1" binding="tns:BasicHttpBinding_IService1">
      <soap:address location="http://www.mycpd2.moh.gov.my/ws/Service1.svc" />
    </wsdl:port>
    <wsdl:port name="BasicHttpsBinding_IService1" binding="tns:BasicHttpsBinding_IService1">
      <soap:address location="https://www.mycpd2.moh.gov.my/ws/Service1.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>