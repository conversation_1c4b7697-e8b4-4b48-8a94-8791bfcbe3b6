<?xml version="1.0" encoding="UTF-8"?><configuration>
	<configSections>
		<sectionGroup name="system.web.extensions" type="System.Web.Configuration.SystemWebExtensionsSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
			<sectionGroup name="scripting" type="System.Web.Configuration.ScriptingSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
				<section name="scriptResourceHandler" type="System.Web.Configuration.ScriptingScriptResourceHandlerSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="MachineToApplication" />
				<sectionGroup name="webServices" type="System.Web.Configuration.ScriptingWebServicesSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
					<section name="jsonSerialization" type="System.Web.Configuration.ScriptingJsonSerializationSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="Everywhere" />
					<section name="profileService" type="System.Web.Configuration.ScriptingProfileServiceSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="MachineToApplication" />
					<section name="authenticationService" type="System.Web.Configuration.ScriptingAuthenticationServiceSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="MachineToApplication" />
					 <section name="roleService" type="System.Web.Configuration.ScriptingRoleServiceSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" allowDefinition="MachineToApplication" />
			</sectionGroup>
			</sectionGroup>
		</sectionGroup>
 </configSections>
	<appSettings>
    <!--add key="IP_App" value="Fz" />
    <add key="IP_App2" value="Fz:1667" /-->
    <!--<add key="IP_App" value="************" />
    <add key="IP_App2" value="************" />-->
    <add key="IP_App" value="************" />
    <add key="IP_App2" value="************" />
    <!--<add key="dB" value="SPMJ_PDSA" />-->
    <add key="dB" value="SPMJ_TEST_RON" />
    <!--<add key="IP_App" value="************" />
    <add key="IP_App2" value="************" />
    <add key="dB" value="SPMJ_TRAIN_2013" />-->
    <!--<add key="IP_App" value="(LOCAL)\SQLEXPRESS" />
    <add key="IP_App2" value="(LOCAL)\SQLEXPRESS" />
    <add key="dB" value="SPMJ_BC_28082015" />-->
    <!--<add key="CrystalImageCleaner-AutoStart" value="true " />
    <add key="CrystalImageCleaner-Sleep" value="60000" />
    <add key="CrystalImageCleaner-Age" value="120000" />-->
    <add key="ChartImageHandler" value="storage=file;timeout=20;dir=c:\TempImageFiles\;" />
    <add key="aspnet:MaxHttpCollectionKeys" value="2001" />
 </appSettings>
	<connectionStrings>
 </connectionStrings>
	<system.web>
    <customErrors mode="Off"/>
    <!-- 
            Set compilation debug="true" to insert debugging 
            symbols into the compiled page. Because this 
            affects performance, set this value to true only 
            during development.

            Visual Basic options:
            Set strict="true" to disallow all data type conversions 
            where data loss can occur. 
            Set explicit="true" to force declaration of all variables.
        -->
		<compilation debug="true" strict="false" explicit="true">
   <assemblies>
    <add assembly="System.Core, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
    <add assembly="System.Data.DataSetExtensions, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
    <add assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
    <add assembly="System.Xml.Linq, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
    <add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
    <add assembly="System.Web.Extensions.Design, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
    <add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
    <!--<add assembly="Microsoft.ReportViewer.WebForms, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
    <add assembly="Microsoft.ReportViewer.Common, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />-->
   
				<!--<add assembly="CrystalDecisions.Web, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692FBEA5521E1304" />
				<add assembly="CrystalDecisions.Shared, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692FBEA5521E1304" />
				<add assembly="CrystalDecisions.ReportSource, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692FBEA5521E1304" />
				<add assembly="CrystalDecisions.CrystalReports.Engine, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692FBEA5521E1304" />
        <add assembly="CrystalDecisions.ReportAppServer.ClientDoc, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" /><add assembly="CrystalDecisions.Enterprise.Framework, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" /><add assembly="CrystalDecisions.Enterprise.InfoStore, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" />-->
   </assemblies>
   <buildProviders>
    <add extension=".rdlc" type="Microsoft.Reporting.RdlBuildProvider, Microsoft.ReportViewer.Common, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
   </buildProviders>
  </compilation>

    <pages>
       <namespaces>
          <clear />
          <add namespace="System" />
          <add namespace="System.Collections" />
          <add namespace="System.Collections.Generic" />
          <add namespace="System.Collections.Specialized" />
          <add namespace="System.Configuration" />
          <add namespace="System.Text" />
          <add namespace="System.Text.RegularExpressions" />
          <add namespace="System.Linq" />
          <add namespace="System.Xml.Linq" />
          <add namespace="System.Web" />
          <add namespace="System.Web.Caching" />
          <add namespace="System.Web.SessionState" />
          <add namespace="System.Web.Security" />
          <add namespace="System.Web.Profile" />
          <add namespace="System.Web.UI" />
          <add namespace="System.Web.UI.WebControls" />
          <add namespace="System.Web.UI.WebControls.WebParts" />
          <add namespace="System.Web.UI.HtmlControls" />
       </namespaces>
       <controls>
          <add tagPrefix="asp" namespace="System.Web.UI" assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
          <add tagPrefix="asp" namespace="System.Web.UI.WebControls" assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
       </controls>
    </pages>
		<!--
            The <authentication> section enables configuration 
            of the security authentication mode used by 
            ASP.NET to identify an incoming user. 
        -->
		<authentication mode="Windows" />
		<!--
            The <customErrors> section enables configuration 
            of what to do if/when an unhandled error occurs 
            during the execution of a request. Specifically, 
            it enables developers to configure html error pages 
            to be displayed in place of a error stack trace.

        <customErrors mode="RemoteOnly" defaultRedirect="GenericErrorPage.htm">
            <error statusCode="403" redirect="NoAccess.htm" />
            <error statusCode="404" redirect="FileNotFound.htm" />
        </customErrors>
        -->
		<httpHandlers>
   <remove path="*.asmx" verb="*" />
   <add path="*.asmx" verb="*" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" validate="false" />
   <add path="*_AppService.axd" verb="*" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" validate="false" />
   <add path="ScriptResource.axd" verb="GET,HEAD" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" validate="false" />
   <add path="Reserved.ReportViewerWebControl.axd" verb="*" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" validate="false" />
   <!--<add path="CrystalImageHandler.aspx" verb="GET" type="CrystalDecisions.Web.CrystalImageHandler, CrystalDecisions.Web, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" />-->
   <add path="ChartImg.axd" verb="GET,HEAD,POST" type="System.Web.UI.DataVisualization.Charting.ChartHttpHandler, System.Web.DataVisualization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="false" />      
    </httpHandlers>
		<httpModules>
			<add name="ScriptModule" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
    </httpModules>
	</system.web>
	<system.codedom>
		<compilers>
			<compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" warningLevel="4" type="Microsoft.VisualBasic.VBCodeProvider, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
				<providerOption name="CompilerVersion" value="v3.5" />
				<providerOption name="OptionInfer" value="true" />
				<providerOption name="WarnAsError" value="false" />
			</compiler>
		</compilers>
	</system.codedom>
	<!-- 
        The system.webServer section is required for running ASP.NET AJAX under Internet
        Information Services 7.0.  It is not necessary for previous version of IIS.
    -->
	<system.webServer>
     <validation validateIntegratedModeConfiguration="false" />
     <modules>
         <remove name="ScriptModule" />
         <add name="ScriptModule" preCondition="managedHandler" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
     </modules>
     <handlers>
         <remove name="WebServiceHandlerFactory-Integrated" />
         <remove name="ScriptHandlerFactory" />
         <remove name="ScriptHandlerFactoryAppServices" />
         <remove name="ScriptResource" />
         <remove name="ChartImageHandler" />
         <add name="ScriptHandlerFactory" verb="*" path="*.asmx" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
         <add name="ScriptHandlerFactoryAppServices" verb="*" path="*_AppService.axd" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
         <add name="ScriptResource" preCondition="integratedMode" verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
         <!--<add name="CrystalImageHandler.aspx_GET" verb="GET" path="CrystalImageHandler.aspx" type="CrystalDecisions.Web.CrystalImageHandler, CrystalDecisions.Web, Version=10.5.3700.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" preCondition="integratedMode" />-->
         <add name="ChartImageHandler" preCondition="integratedMode" verb="GET,HEAD" path="ChartImg.axd" type="System.Web.UI.DataVisualization.Charting.ChartHttpHandler, System.Web.DataVisualization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
     </handlers>
        <directoryBrowse enabled="true" />
        <defaultDocument enabled="false">
            <files>
                <remove value="index.htm" />
                <remove value="iisstart.htm" />
                <remove value="Default.asp" />
                <remove value="index.html" />
                <remove value="Default.htm" />
                <add value="p0_login.aspx" />
            </files>
        </defaultDocument>
 </system.webServer>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <add name="ChartImageHandler" preCondition="integratedMode" verb="GET,HEAD" path="ChartImg.axd" type="System.Web.UI.DataVisualization.Charting.ChartHttpHandler, System.Web.DataVisualization, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
        <assemblyIdentity name="System.Web.Extensions" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="*******" />
      </dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Extensions.Design" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="*******" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>

                                          <system.serviceModel>
                                              <bindings>
                                                  <basicHttpBinding>
                                                      <!--<binding name="BasicHttpBinding_IService1" closeTimeout="00:01:00"
                                                          openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:01:00"
                                                          allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard"
                                                          maxBufferSize="65536" maxBufferPoolSize="524288" maxReceivedMessageSize="65536"
                                                          messageEncoding="Text" textEncoding="utf-8" transferMode="Buffered"
                                                          useDefaultWebProxy="true">
                                                          <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384"
                                                              maxBytesPerRead="4096" maxNameTableCharCount="16384" />
                                                          <security mode="None">
                                                              <transport clientCredentialType="None" proxyCredentialType="None"
                                                                  realm="" />
                                                              <message clientCredentialType="UserName" algorithmSuite="Default" />
                                                          </security>
                                                      </binding>-->
                                                      <binding name="BasicHttpsBinding_IService1" closeTimeout="00:01:00"
                                                          openTimeout="00:01:00" receiveTimeout="00:10:00" sendTimeout="00:01:00"
                                                          allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard"
                                                          maxBufferSize="65536" maxBufferPoolSize="524288" maxReceivedMessageSize="65536"
                                                          messageEncoding="Text" textEncoding="utf-8" transferMode="Buffered"
                                                          useDefaultWebProxy="true">
                                                          <readerQuotas maxDepth="32" maxStringContentLength="8192" maxArrayLength="16384"
                                                              maxBytesPerRead="4096" maxNameTableCharCount="16384" />
                                                          <security mode="Transport">
                                                              <transport clientCredentialType="None" proxyCredentialType="None"
                                                                  realm="" />
                                                              <message clientCredentialType="UserName" algorithmSuite="Default" />
                                                          </security>
                                                      </binding>
                                                  </basicHttpBinding>
                                              </bindings>
                                              <client>
                                                  <!--<endpoint address="http://www.mycpd2.moh.gov.my/ws/Service1.svc"
                                                      binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IService1"
                                                      contract="MyCPD2_SVC.IService1" name="BasicHttpBinding_IService1" />-->
                                                  <endpoint address="https://www.mycpd2.moh.gov.my/ws/Service1.svc"
                                                      binding="basicHttpBinding" bindingConfiguration="BasicHttpsBinding_IService1"
                                                      contract="MyCPD2_SVC.IService1" name="BasicHttpsBinding_IService1" />
                                              </client>
                                          </system.serviceModel>
</configuration>