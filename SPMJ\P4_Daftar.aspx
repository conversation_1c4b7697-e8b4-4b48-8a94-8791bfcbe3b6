﻿<%@ Page Language="vb" MaintainScrollPositionOnPostback="true" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P4_Daftar.aspx.vb" Inherits="SPMJ.WebForm22" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">

    <script type="text/javascript">
<!--
window.history.forward(1);
// -->
</script>
    <style type="text/css">
        .std_hd
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             color: #ffffff;
             font-variant :small-caps ;
             background-color: #6699cc;
        }
        .std_hd2
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             font-variant :small-caps ;             
             color: #ffffff;
        }
        .std
        {
             font-family: Arial;
             font-size: 8pt; 
             color: #000000;
        }
        .style9
        {
            height: 23px;
        }
        .style12
        {
            height: 21px;
        }
        .style33
    {
            height: 20px;
        }
        p.<PERSON><PERSON><PERSON><PERSON>
	{margin-bottom:.0001pt;
	font-size:12.0pt;
	font-family:"Times New Roman";
	        margin-left: 0in;
            margin-right: 0in;
            margin-top: 0in;
        }
        .style62
        {
            height: 24px;
            width: 186px;
        }
        .style81
        {
            width: 650px;
            height: 436px;
            left: 0px;
            top: 70px;
            position: static;
        }
        .style83
        {
            height: 20px;
            }
        .style87
        {
            width: 186px;
            height: 21px;
        }
        .style90
        {
            height: 19px;
            margin-left: 40px;
            }
        .style92
        {
            height: 22px;
            width: 186px;
        }
        .style96
        {
        }
        .style102
        {
            width: 74px;
        }
        .style103
        {
            width: 59px;
        }
        .style108
    {
        height: 19px;
        width: 186px;
            color: #CC0000;
        }
        .style111
    {
        height: 24px;
        }
    .style115
    {
        height: 10px;
        }
        .style121
        {
            width: 186px;
            height: 23px;
        }
        .style127
        {
            width: 186px;
            height: 103px;
        }
        .style128
        {
            height: 103px;
        }
        .style129
        {
            width: 7px;
        }
        .style130
        {
            width: 28px;
        }
        .style131
        {
            height: 22px;
            width: 186px;
            color: #CC0000;
        }
        .style132
        {
            color: #CC0000;
        }
        .style133
        {
            background-color: #FFFFFF;
            color: #CC0000;
        }
        .style134
        {
            color: #000000;
            background-color: #FFFFFF;
        }
        .style135
        {
            color: #000000;
        }
        .style136
        {
            width: 774px;
        }
        </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:Localize ID="Localize1" runat="server"></asp:Localize>
    <div style="width:100%; ">
    <table id="Table1" width="100%" style="font-variant: small-caps" bgcolor="White">
    <br>
    <tr><td>
    
    <table id="Table2" align="center" border="0" cellpadding="-1" cellspacing="0" 
        
            style="border: 1px solid black; margin-left: 0px; font-family: Arial; font-size: 8pt; text-transform: none; line-height: 21px; width: 85%;" 
            bgcolor="White" class="style81">
        <tr>
            <td align="center" bgcolor="#8BB900" 
                valign="top" colspan="4" 
                
                
                
                
                style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #000000; vertical-align: middle; text-align: center; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bolder;" >
                pendaftaran tpc - pendaftaran baru</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style103" rowspan="51">
                            &nbsp;
                        </td>
            <td align="center" bgcolor="#ffffff" 
                valign="top" class="style83" colspan="2">
                            <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                            </asp:ScriptManagerProxy>
                <asp:TextBox ID="Tx_Msj" runat="server" CssClass="std" Width="100%" 
                    Wrap="False" BorderStyle="None" Height="53px" TextMode="MultiLine" 
                    Visible="False"></asp:TextBox>
                                        </td>
            <td align="left" bgcolor="#ffffff" valign="top" 
                class="style102" rowspan="51" width="10%">
                &nbsp;</td>
        </tr>
        <tr style="line-height: 21px; background-color: #999966; vertical-align: middle;" 
            bgcolor="#999966">
								<TD valign="middle" align="left" bgcolor="#8BB900" 
                class="style62" 
                
                
                
                style="font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;">&nbsp; Jenis Pendaftaran</TD>
 
            <td align="left" bgcolor="#8BB900" valign="middle" class="style111">
                <asp:DropDownList ID="Cb_Jenis" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" Width="200px" CssClass="std" 
                    AutoPostBack="True">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="1">JURURAWAT TERLATIH</asp:ListItem>
                    <asp:ListItem Value="2">LATIHAN ELEKTIF</asp:ListItem>
                    <asp:ListItem Value="3">PENGAJAR JURURAWAT</asp:ListItem>
                    <asp:ListItem Value="4">INSTRUKTOR KLINIKAL</asp:ListItem>
                </asp:DropDownList>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:CheckBox ID="chk_Jenis" runat="server" 
                    ForeColor="White" Height="22px" Text="Pinda" Visible="False" 
                    AutoPostBack="True" />
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                </td>
        </tr>
        <tr style="line-height: 10px">
								<TD valign="top" align="left" bgcolor="white" 
                class="style115" colspan="2">
                </td>
        </tr>
        <tr style="line-height: 10px">
								<TD valign="top" align="left" bgcolor="white" 
                class="style115" colspan="2">
                                    <span class="style133">*</span> <span class="style132">Medan Mandatori</span></td>
        </tr>
        <tr style="line-height: 10px">
								<TD valign="top" align="center" bgcolor="white" 
                class="style115" colspan="2">
                                    &nbsp;</td>
        </tr>
        <tr style="line-height: 21px">
            <td valign="top" align="left" bgcolor="white" 
                class="style108">* <span class="style134">NO. PASPORT
                </span></td><td bgcolor="White" class="style90">
                                         <asp:UpdatePanel ID="UpdatePanel5" runat="server">
                                             <ContentTemplate>
                                                 <asp:TextBox ID="Tx_NoKP" runat="server" CssClass="std" Width="190px" 
                                                     Wrap="False" AutoPostBack="True"></asp:TextBox>
                                                 <asp:CheckBox ID="chk_Tukar" runat="server" Height="22px" Text="Pinda" 
                                                     Visible="False" AutoPostBack="True" />
                                             </ContentTemplate>
                                         </asp:UpdatePanel>
                                         <asp:Button ID="cmd_Semak" runat="server" Font-Names="Arial" Font-Size="8pt" Height="20px" tabIndex="3" Text="SEMAK" Width="60px" />                                                 
                                     </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style131">* <span class="style135">NAMA</span></td>
            <td bgcolor="White">
                <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="282px" 
                    Wrap="False"></asp:TextBox>
            </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style92"><span class="style132">*</span> <span class="style135">WARGANEGARA</span></TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:UpdatePanel ID="UpdatePanel13" runat="server">
                    <ContentTemplate>
                        <asp:DropDownList ID="Cb_Warga" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="11" Width="190px" 
    CssClass="std">
                        </asp:DropDownList>
                        <asp:CheckBox ID="chk_PR" runat="server" Height="24px" Text="Penduduk Tetap" />
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp; TARIKH LAHIR</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                            <asp:UpdatePanel ID="UpdatePanel12" runat="server">
                                <ContentTemplate>
                                    <asp:TextBox ID="Tx_Tkh_Lahir" runat="server" CssClass="std" 
                    Width="95px" AutoPostBack="True"></asp:TextBox>
                                    <cc1:MaskedEditExtender ID="Tx_Tkh_Lahir_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" 
    CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Lahir" 
                                CultureName="en-GB" 
    UserDateFormat="DayMonthYear">
                                    </cc1:MaskedEditExtender>
                                    <cc1:CalendarExtender ID="Tx_Tkh_Lahir_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_Tkh_Lahir" 
                                Format="dd/MM/yyyy">
                                    </cc1:CalendarExtender>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                                     </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp; TEMPAT LAHIR</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:TextBox ID="Tx_Tpt_Lahir" runat="server" CssClass="std" Width="282px" 
                    Wrap="False"></asp:TextBox>
                                     </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83"><span class="style133">*</span> <span class="style135">JANTINA</span></TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:DropDownList ID="Cb_Jantina" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="11" Width="190px" CssClass="std">
                    <asp:ListItem Value=""></asp:ListItem>
                    <asp:ListItem Value="1">LELAKI</asp:ListItem>
                    <asp:ListItem Value="2">PEREMPUAN</asp:ListItem>
                </asp:DropDownList>
            </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style92">&nbsp; UMUR&nbsp;</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:UpdatePanel ID="UpdatePanel11" runat="server">
                    <ContentTemplate>
                        <asp:TextBox ID="Tx_Umur" runat="server" CssClass="std" Width="95px" 
                    Wrap="False" ReadOnly="True"></asp:TextBox>
                    </ContentTemplate>
                </asp:UpdatePanel>
                                     </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp;
									TARAF PERKAHWINAN</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:DropDownList ID="Cb_Kahwin" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="22" Width="190px">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="1">BUJANG</asp:ListItem>
                    <asp:ListItem Value="2">BERKAHWIN</asp:ListItem>
                    <asp:ListItem Value="3">DUDA</asp:ListItem>
                    <asp:ListItem Value="4">JANDA</asp:ListItem>
                    <asp:ListItem Value="5">BERKAHWIN (MALAYSIA)</asp:ListItem>
                </asp:DropDownList>
            </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp; ALAMAT TETAP</TD>
            <td align="left" bgcolor="white" valign="top" 
                id="Tx_TP_Alamat" class="style33">
                <asp:TextBox ID="Tx_TP_Alamat" runat="server" CssClass="std" Height="60px" 
                    TextMode="MultiLine" Width="282px"></asp:TextBox>
            </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp;
									POSKOD</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_TP_Poskod" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style92">&nbsp;
									BANDAR</TD>
            <td align="left" bgcolor="white" valign="top" class="style9">
                <asp:TextBox ID="Tx_TP_Bandar" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style87">&nbsp; NEGARA</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:DropDownList ID="Cb_TP_Negeri" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="193px">
                    <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
            </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83"><span class="style133">*</span> <span class="style135">ALAMAT SURAT-MENYURAT</span><br />
&nbsp;<asp:CheckBox ID="CheckBox2" runat="server" Text="sama alamat tetap" Visible="False" />
                                </TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_SM_Alamat" runat="server" CssClass="std" Height="60px" 
                    TextMode="MultiLine" Width="282px"></asp:TextBox>
            </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83"><span class="style133">*</span> <span class="style135">POSKOD</span></TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_SM_Poskod" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83"><span class="style133">*</span>&nbsp;<span class="style135">BANDAR</span></TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_SM_Bandar" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83"><span class="style133">*</span> <span class="style135">NEGERI</span></TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:DropDownList ID="Cb_SM_Negeri" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="193px">
                    <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
            </td>
        </tr>
        <tr>
								<TD valign="top" align="left" bgcolor="white" 
                class="style83">&nbsp;
									E-MEL</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_Emel" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
        </tr>
                    <tr>
								<TD valign="top" bgcolor="#ffffff" class="style96">&nbsp;</TD>
                        <td bgcolor="#ffffff" valign="top">
                            &nbsp;</td>
                    </tr>
                    <tr>
								<TD valign="top" bgcolor="#ffffff" class="style96">&nbsp;&nbsp;INSTITUSI LATIHAN</TD>
                        <td bgcolor="#ffffff" valign="top">
                            <asp:DropDownList ID="Cb_Kolej" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="500px">
                            </asp:DropDownList>
                        </td>
                    </tr>
                    <tr>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; TARIKH TAMAT 
                                    LATIHAN</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                            <asp:TextBox ID="Tx_T_Latihan" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_T_Latihan_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_T_Latihan" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_T_Latihan_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_T_Latihan" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                    </tr>
                    <tr>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; LEMBAGA PENDAFTAR&nbsp;</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                <asp:TextBox ID="Tx_LP" runat="server" CssClass="std" Width="400px"></asp:TextBox>
                            </td>
                    </tr>
                    <tr>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; NO. PENDAFTARAN LP</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                <asp:TextBox ID="Tx_LP_NoPd" runat="server" CssClass="std" Width="282px"></asp:TextBox>
                            </td>
                    </tr>
                    <tr>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; TARIKH PENGESAHAN 
                                    SIJIL LP</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                            <asp:TextBox ID="Tx_LP_TkhSah" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_LP_TkhSah_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_LP_TkhSah" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_LP_TkhSah_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_LP_TkhSah" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                    </tr>
                    <tr>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp;</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                            &nbsp;</td>
                    </tr>
                    <tr>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">
                                    <span class="style133">*</span>&nbsp; <span class="style135">NO. SIRI 
                                    BORANG PERMOHONAN</span>&nbsp;</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                <asp:TextBox ID="Tx_NoSiri" runat="server" CssClass="std" Width="95px" 
                    Wrap="False"></asp:TextBox>
                            </td>
                    </tr>
                    <tr>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">
                                    <span class="style133">*</span>&nbsp; <span class="style135">TARIKH 
                                    PERMOHONAN&nbsp;</span></TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                            <asp:TextBox ID="Tx_TkhMohon" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_TkhMohon_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_TkhMohon" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_TkhMohon_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_TkhMohon" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                    </tr>
                    <tr>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; TARIKH RESIT&nbsp;</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                            <asp:TextBox ID="Tx_TkhResit" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_TkhResit_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_TkhResit" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_TkhResit_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_TkhResit" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                    </tr>
                    <tr>
								<TD valign="top" align="left" bgcolor="#ffffff" class="style83">&nbsp; NO. RESIT&nbsp;</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                <asp:TextBox ID="Tx_NoResit" runat="server" CssClass="std" Width="95px" 
                    Wrap="False"></asp:TextBox>
                            </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style96">
                        </td>
                        <td bgcolor="#ffffff">
                            <asp:DropDownList ID="Cb_K" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="50px" Visible="False">
                            </asp:DropDownList>
                            <asp:DropDownList ID="Cb_I" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="50px" Visible="False">
                            </asp:DropDownList>
                            <asp:DropDownList ID="Cb_M" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="50px" Visible="False">
                            </asp:DropDownList>
                            <asp:DropDownList ID="Cb_D" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="50px" Visible="False">
                            </asp:DropDownList>
                            <br />
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style96" valign="top" colspan="2">
                        <table align="left"><tr>
                                <td align="left" width="100%"><asp:UpdatePanel ID="UpdatePanel1" runat="server">
                                        <ContentTemplate>
                                            <asp:Button ID="cmdHantar1" runat="server" Font-Bold="False" Font-Names="Arial" 
                                                Font-Size="8pt" Height="21px" tabIndex="3" Text="+" Width="24px" 
                                                Visible="False" />
                                            <asp:TextBox ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                                                BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                Font-Size="8pt" Height="16px" tabIndex="36" Width="130px">KELAYAKAN IKHTISAS</asp:TextBox>
                                            
                                            <asp:GridView ID="Gd" runat="server" AutoGenerateColumns="False" 
                                                BorderColor="#5D7B9D" BorderStyle="Solid" BorderWidth="1px" CellPadding="0" 
                                                Font-Names="Arial" Font-Size="8pt" ForeColor="Black" GridLines="Horizontal" 
                                                Height="106px" HorizontalAlign="Left">
                                                <FooterStyle BackColor="#A6B7CA" Font-Bold="True" ForeColor="White" />
                                                <RowStyle BackColor="White" VerticalAlign="Middle" />
                                                <Columns>
                                                    <asp:TemplateField HeaderText="#">
                                                        <HeaderStyle HorizontalAlign="Center" />
                                                        <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="KELAYAKAN">
                                                        <ItemTemplate>
                                                            <asp:DropDownList ID="Cb_Kelayakan" runat="server" Font-Names="Arial" 
                                                                Font-Size="8pt" Height="19px" tabIndex="33" Width="160px">
                                                            </asp:DropDownList>
                                                            &nbsp;
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="BIDANG">
                                                        <ItemTemplate>
                                                            <asp:TextBox ID="Tx_Bidang" runat="server" CssClass="std" Width="350px"></asp:TextBox>
                                                            &nbsp;
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="TARIKH KELAYAKAN">
                                                        <ItemTemplate>
                                                            <asp:TextBox ID="Tx_Tkh_Layak" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                            <cc1:MaskedEditExtender ID="Tx_Tkh_Layak_MaskedEditExtender" runat="server" 
                                                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Layak" 
                                                                UserDateFormat="DayMonthYear">
                                                            </cc1:MaskedEditExtender>
                                                            <cc1:CalendarExtender ID="Tx_Tkh_Layak_CalendarExtender" runat="server" 
                                                                Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                TargetControlID="Tx_Tkh_Layak">
                                                            </cc1:CalendarExtender>
                                                            &nbsp;
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField ShowHeader="False">
                                                        <ItemTemplate>
                                                            <asp:Button ID="Button1" runat="server" CausesValidation="False" 
                                                                CommandName="Select" Font-Bold="True" Font-Names="Arial" Font-Size="8pt" 
                                                                Height="20px" onclick="Button1_Click" Text="+" Width="21px" />
                                                        </ItemTemplate>
                                                        <ControlStyle Font-Names="Arial" Font-Size="8pt" />
                                                        <HeaderStyle Width="65px" />
                                                    </asp:TemplateField>
                                                </Columns>
                                                <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                                                <SelectedRowStyle Font-Bold="False" />
                                                <HeaderStyle BackColor="#5D7B9D" CssClass="menu_small" Font-Bold="True" 
                                                    ForeColor="White" Height="21px" HorizontalAlign="Left" />
                                                <AlternatingRowStyle BackColor="White" />
                                            </asp:GridView>
                                            
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </td></tr></table>
                            
                
                            
                                     
                            
                
                            
                                     </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style96" valign="top" colspan="2">
                            <table align="left"><tr>
                                <td align="left">
                                    <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                                        <ContentTemplate>
                                            <asp:Button ID="cmdHantar0" runat="server" Font-Bold="False" Font-Names="Arial" 
                                                Font-Size="8pt" Height="21px" tabIndex="3" Text="+" Width="24px" 
                                                Visible="False" />
                                            <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" 
                                                BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                Font-Size="8pt" Height="16px" tabIndex="36" Width="130px">KELAYAKAN AKADEMIK</asp:TextBox>
                                            <asp:GridView ID="GdA" 
                            runat="server" CellPadding="0" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" GridLines="Horizontal" HorizontalAlign="Left" BorderColor="#5D7B9D" 
    AutoGenerateColumns="False" Height="106px" BorderStyle="Solid" BorderWidth="1px">
                                                <FooterStyle BackColor="#A6B7CA" Font-Bold="True" ForeColor="White" />
                                                <RowStyle BackColor="White" />
                                                <Columns>
                                                    <asp:TemplateField HeaderText="#">
                                                        <HeaderStyle HorizontalAlign="Center" />
                                                        <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="KELAYAKAN">
                                                        <ItemTemplate>
                                                            <asp:DropDownList ID="Cb_Kelayakan" runat="server" Font-Names="Arial" 
                                                                Font-Size="8pt" Height="19px" tabIndex="33" Width="200px">
                                                            </asp:DropDownList>&nbsp;
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="TARIKH KELAYAKAN">
                                                        <ItemTemplate>
                                                            <asp:TextBox ID="Tx_Tkh_Layak" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                            <cc1:MaskedEditExtender ID="Tx_Tkh_Layak_MaskedEditExtender" runat="server" 
                                    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                    CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                    CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                    Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Layak" 
                                    UserDateFormat="DayMonthYear">
                                                            </cc1:MaskedEditExtender>
                                                            <cc1:CalendarExtender ID="Tx_Tkh_Layak_CalendarExtender" runat="server" 
                                    Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                    TargetControlID="Tx_Tkh_Layak">
                                                            </cc1:CalendarExtender>&nbsp;
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField ShowHeader="False">
                                                        <ItemTemplate>
                                                            <asp:Button ID="Button2" runat="server" CausesValidation="False" 
                                    CommandName="Select" Font-Names="Arial" Font-Size="8pt" Height="20px" 
                                    Text="+" Width="21px" Font-Bold="True" onclick="Button1_Click" />
                                                        </ItemTemplate>
                                                        <ControlStyle Font-Names="Arial" Font-Size="8pt" />
                                                        <HeaderStyle Width="65px" />
                                                    </asp:TemplateField>
                                                </Columns>
                                                <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                                                <SelectedRowStyle Font-Bold="False" />
                                                <HeaderStyle BackColor="#738EAC" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                                                <AlternatingRowStyle BackColor="White" />
                                            </asp:GridView>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </td></tr></table>
                            
                                     </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style96" valign="top" colspan="2"><table align="left"><tr>
                                <td align="left">
                                    <asp:UpdatePanel ID="UpdatePanel7" runat="server">
                                        <ContentTemplate>
                                            <asp:Button ID="Button5" runat="server" Font-Bold="False" Font-Names="Arial" 
                                                Font-Size="8pt" Height="21px" tabIndex="3" Text="+" Width="24px" 
                                                Visible="False" />
                                            <asp:TextBox ID="Cb_Sbj6" runat="server" BackColor="Transparent" 
                                                BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                Font-Size="8pt" Height="16px" tabIndex="36" Width="130px">PENGALAMAN BEKERJA</asp:TextBox>
                                            <asp:GridView ID="GdP" 
                            runat="server" CellPadding="0" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" GridLines="Horizontal" HorizontalAlign="Left" 
                    Width="100%" BorderColor="#5D7B9D" 
    AutoGenerateColumns="False" BorderStyle="Solid" BorderWidth="1px">
                                                <FooterStyle BackColor="#A6B7CA" Font-Bold="True" ForeColor="White" />
                                                <RowStyle BackColor="White" />
                                                <Columns>
                                                    <asp:TemplateField HeaderText="#">
                                                        <HeaderStyle HorizontalAlign="Center" />
                                                        <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="DARI">
                                                        <ItemTemplate>
                                                            <asp:TextBox ID="Tx_Dari" runat="server" CssClass="std" Width="95px"></asp:TextBox>&nbsp;
                                                            <cc1:MaskedEditExtender ID="Tx_Dari_MaskedEditExtender" runat="server" 
                                                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Dari" 
                                                                UserDateFormat="DayMonthYear">
                                                            </cc1:MaskedEditExtender>
                                                            <cc1:CalendarExtender ID="Tx_Dari_CalendarExtender" runat="server" 
                                                                Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                TargetControlID="Tx_Dari">
                                                            </cc1:CalendarExtender>
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="HINGGA">
                                                        <ItemTemplate>
                                                            <asp:TextBox ID="Tx_Hingga" runat="server" CssClass="std" Width="95px"></asp:TextBox>&nbsp;
                                                            <cc1:MaskedEditExtender ID="Tx_Hingga_MaskedEditExtender" runat="server" 
                                                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Hingga" 
                                                                UserDateFormat="DayMonthYear">
                                                            </cc1:MaskedEditExtender>
                                                            <cc1:CalendarExtender ID="Tx_Hingga_CalendarExtender" runat="server" 
                                                                Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                TargetControlID="Tx_Hingga">
                                                            </cc1:CalendarExtender>
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="BIDANG">
                                                        <ItemTemplate>
                                                            <asp:DropDownList ID="Cb_Bidang" runat="server" Font-Names="Arial" 
                                                                Font-Size="8pt" Height="19px" tabIndex="33" Width="300px">
                                                                <asp:ListItem></asp:ListItem>
                                                            </asp:DropDownList>&nbsp;
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="SEBAGAI">
                                                        <ItemTemplate>
                                                            <asp:DropDownList ID="Cb_Sebagai" runat="server" Font-Names="Arial" 
                                                                Font-Size="8pt" Height="19px" tabIndex="33" Width="150px">
                                                                <asp:ListItem></asp:ListItem>
                                                                <asp:ListItem Value="1">JURURAWAT</asp:ListItem>
                                                                <asp:ListItem Value="2">INSTRUKTOR KLINIKAL</asp:ListItem>
                                                                <asp:ListItem Value="3">PENGAJAR</asp:ListItem>
                                                                <asp:ListItem Value="4">PAKAR RUJUK</asp:ListItem>
                                                                <asp:ListItem>VOLUNTEER NURSE</asp:ListItem>
                                                                <asp:ListItem>LAIN-LAIN</asp:ListItem>
                                                            </asp:DropDownList>&nbsp;
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField ShowHeader="False">
                                                        <ItemTemplate>
                                                            <asp:Button ID="Button3" runat="server" CausesValidation="False" 
                                    CommandName="Select" Font-Names="Arial" Font-Size="8pt" Height="20px" 
                                    Text="+" Width="21px" Font-Bold="True" onclick="Button1_Click" />
                                                        </ItemTemplate>
                                                        <ControlStyle Font-Names="Arial" Font-Size="8pt" />
                                                        <HeaderStyle Width="65px" />
                                                    </asp:TemplateField>
                                                </Columns>
                                                <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                                                <SelectedRowStyle Font-Bold="False" />
                                                <HeaderStyle BackColor="#5D7B9D" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                                                <AlternatingRowStyle BackColor="White" />
                                            </asp:GridView>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </td></tr></table>
                            
                                     </td>
                                 </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style96" valign="top" colspan="2"><table align="left"><tr>
                                <td align="left" class="style136">
                                    <asp:UpdatePanel ID="UpdatePanel9" runat="server">
                                        <ContentTemplate>
                                            <asp:Button ID="Button7" runat="server" Font-Bold="False" Font-Names="Arial" 
                                                Font-Size="8pt" Height="21px" tabIndex="3" Text="+" Width="24px" 
                                                Visible="False" />
                                            <asp:TextBox ID="Cb_Sbj8" runat="server" BackColor="Transparent" 
                                                BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                Font-Size="8pt" Height="16px" tabIndex="36" Width="130px">LATIHAN TRANSKRIP</asp:TextBox>
                                            <asp:GridView ID="GdL" runat="server" BorderColor="#5D7B9D" BorderStyle="Solid" 
                                                BorderWidth="1px" CellPadding="0" Font-Names="Arial" Font-Size="8pt" 
                                                ForeColor="Black" GridLines="Horizontal" HorizontalAlign="Left" Width="100%">
                                                <FooterStyle BackColor="#A6B7CA" Font-Bold="True" ForeColor="White" />
                                                <RowStyle BackColor="White" />
                                                <Columns>
                                                    <asp:TemplateField HeaderText="#">
                                                        <HeaderStyle HorizontalAlign="Center" />
                                                        <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="LATIHAN">
                                                        <ItemTemplate>
                                                            &nbsp;
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="MASA LJM">
                                                        <ItemTemplate>
                                                            &nbsp;
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="MASA CATIT  ">
                                                        <ItemTemplate>
                                                            <asp:TextBox ID="Tx_Masa" runat="server" CssClass="std" Width="30px"></asp:TextBox>
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                </Columns>
                                                <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                                                <SelectedRowStyle Font-Bold="False" />
                                                <HeaderStyle BackColor="#5D7B9D" CssClass="menu_small" Font-Bold="True" 
                                                    ForeColor="White" Height="21px" HorizontalAlign="Left" />
                                                <AlternatingRowStyle BackColor="White" />
                                            </asp:GridView>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </td></tr><tr>
                                <td align="left" class="style136">
                                    <asp:UpdatePanel ID="UpdatePanel10" runat="server">
                                        <ContentTemplate>
                                            <table cellpadding="-1" cellspacing="-1" style="width:100%;">
                                                <tr>
                                                    <td align="left" class="style129">
                                                        <asp:Button ID="cmdHantar2" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                                            Height="20px" tabIndex="3" Text="KIRA" Width="60px" />
                                                    </td>
                                                    <td align="right" width="20%">
                                                        Jumlah Teori&nbsp;&nbsp;
                                                    </td>
                                                    <td class="style130">
                                                        <asp:TextBox ID="Tx_J_Teori" runat="server" CssClass="std" Width="50px" 
                                                            Wrap="False" ReadOnly="True"></asp:TextBox>
                                                    </td>
                                                    <td align="right" width="30%">
                                                        NURSING SCIENCES&nbsp;&nbsp;
                                                    </td>
                                                    <td width="50">
                                                        <asp:TextBox ID="Tx_J_NS" runat="server" CssClass="std" Width="50px" 
                                                            Wrap="False" ReadOnly="True"></asp:TextBox>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="right" class="style129">
                                                        &nbsp;</td>
                                                    <td align="right" width="20%">
                                                        Jumlah Amali&nbsp;&nbsp;
                                                    </td>
                                                    <td class="style130">
                                                        <asp:TextBox ID="Tx_J_Amali" runat="server" CssClass="std" Width="50px" 
                                                            Wrap="False" ReadOnly="True"></asp:TextBox>
                                                    </td>
                                                    <td align="right" width="20%">
                                                        &nbsp;</td>
                                                    <td width="50">
                                                        &nbsp;</td>
                                                </tr>
                                            </table>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                    &nbsp;</td></tr></table>
                            
                                     </td>
                                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style96" valign="top" colspan="2"><table align="left"><tr>
                                <td align="left">
                                    <asp:UpdatePanel ID="UpdatePanel8" runat="server">
                                        <ContentTemplate>
                                            <asp:Button ID="Button6" runat="server" Font-Bold="False" Font-Names="Arial" 
                                                Font-Size="8pt" Height="21px" tabIndex="3" Text="+" Width="24px" 
                                                Visible="False" />
                                            <asp:TextBox ID="Cb_Sbj7" runat="server" BackColor="Transparent" 
                                                BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                                                Font-Size="8pt" Height="16px" tabIndex="36" Width="130px">MAJIKAN</asp:TextBox>
                                            <asp:GridView ID="GdM" 
                            runat="server" CellPadding="0" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" GridLines="Horizontal" HorizontalAlign="Left" 
                    Width="100%" BorderColor="#5D7B9D" 
    AutoGenerateColumns="False" BorderStyle="Solid" BorderWidth="1px">
                                                <FooterStyle BackColor="#A6B7CA" Font-Bold="True" ForeColor="White" />
                                                <RowStyle BackColor="White" />
                                                <Columns>
                                                    <asp:TemplateField HeaderText="#">
                                                        <HeaderStyle HorizontalAlign="Center" />
                                                        <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="MAJIKAN">
                                                        <ItemTemplate>
                                                            <span class="style132">*</span><asp:DropDownList ID="Cb_Majikan" runat="server" Font-Names="Arial" 
                                                                Font-Size="8pt" Height="19px" tabIndex="33" Width="450px">
                                                            </asp:DropDownList>&nbsp;
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="TEMPOH SAH LAKU">
                                                        <ItemTemplate>
                                                            <asp:TextBox ID="Tx_Tempoh" runat="server" CssClass="std" Width="30px" 
                                                                Wrap="False"></asp:TextBox><asp:DropDownList ID="Cb_Tempoh" runat="server" Font-Names="Arial" 
                                                                Font-Size="8pt" Height="19px" tabIndex="33" Width="80px">
                                                                <asp:ListItem></asp:ListItem>
                                                                <asp:ListItem>TAHUN</asp:ListItem>
                                                                <asp:ListItem>HARI</asp:ListItem>
                                                                <asp:ListItem>BULAN</asp:ListItem>
                                                            </asp:DropDownList>&nbsp;
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="TARIKH TAMAT">
                                                        <ItemTemplate>
                                                            <asp:TextBox ID="Tx_TkhTamat" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                            <cc1:MaskedEditExtender ID="Tx_TkhTamat_MaskedEditExtender" runat="server" 
                                                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                                CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                                Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_TkhTamat" 
                                                                UserDateFormat="DayMonthYear">
                                                            </cc1:MaskedEditExtender>
                                                            <cc1:CalendarExtender ID="Tx_TkhTamat_CalendarExtender" runat="server" 
                                                                Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                                TargetControlID="Tx_TkhTamat">
                                                            </cc1:CalendarExtender>
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField ShowHeader="False">
                                                        <ItemTemplate>
                                                            <asp:Button ID="Button4" runat="server" CausesValidation="False" 
                                    CommandName="Select" Font-Names="Arial" Font-Size="8pt" Height="20px" 
                                    Text="BATAL" Width="50px" Font-Bold="False" onclick="Button1_Click" />
                                                        </ItemTemplate>
                                                        <ControlStyle Font-Names="Arial" Font-Size="8pt" />
                                                        <HeaderStyle Width="65px" />
                                                    </asp:TemplateField>
                                                </Columns>
                                                <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                                                <SelectedRowStyle Font-Bold="False" />
                                                <HeaderStyle BackColor="#738EAC" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                                                <AlternatingRowStyle BackColor="White" />
                                            </asp:GridView>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </td></tr></table>
                            
                                     </td>
                                 </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style96">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                    </tr>
                    <tr id="tr1">
                        <td bgcolor="#999966" colspan="2" 
                            style="border: 1px solid #999966; color: #FFFFFF; font-weight: bold;">
                                                        &nbsp; Senarai Semak </td>
                        </td>

                    </tr>
        <tr>
                        <td bgcolor="#F5F5F1" colspan="2" valign="top" 
                            style="border: 1px solid #999966; line-height: normal" align="left"><table align="center"><tr>
                                <td align="left" colspan="1">
                                    <asp:CheckBoxList ID="chk_Semak" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" RepeatColumns="2" 
                                Width="100%" Height="31px">
                                <asp:ListItem>Salinan Sijil Perkahwinan</asp:ListItem>
                                <asp:ListItem>Salinan Pasport</asp:ListItem>
                                <asp:ListItem>Salinan Sijil Lahir</asp:ListItem>
                                <asp:ListItem>Salinan Sijil Akademik</asp:ListItem>
                                <asp:ListItem>Foto</asp:ListItem>
                                <asp:ListItem>Salinan Sijil Ikhtisas</asp:ListItem>
                                <asp:ListItem>Salinan Sijil Pendaftaran dengan Lembaga Jururawat</asp:ListItem>
                                <asp:ListItem>Salinan Sijil Amalan Kejururawatan</asp:ListItem>
                                <asp:ListItem>Letter of Good Standing</asp:ListItem>
                                <asp:ListItem>Salinan VOR</asp:ListItem>
                                <asp:ListItem>Salinan VOT</asp:ListItem>
                                <asp:ListItem>Transkrip Latihan</asp:ListItem>
                                <asp:ListItem>Surat Perisytiharan dari majikan terkini</asp:ListItem>
                                <asp:ListItem>Bayaran Pendaftaran</asp:ListItem>
                                        <asp:ListItem>Resume</asp:ListItem>
                                        <asp:ListItem>Sijil Pengajar (Kolej)/ Teaching Methodology</asp:ListItem>
                                    </asp:CheckBoxList></td></tr></table>
                            
                        </td>
        <tr>
                        <td bgcolor="#ffffff" class="style96">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                    </tr>
        <tr valign="bottom">
                        <td bgcolor="#999966" class="style62" 
                                         
                            style="font-weight: bold; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-variant: small-caps;" 
                            valign="bottom">
                            &nbsp; Status&nbsp;</td>
                        <td bgcolor="#999966" class="style111">
                            <asp:UpdatePanel ID="UpdatePanel6" runat="server">
                                <ContentTemplate>
                                    <asp:DropDownList ID="Cb_Status" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="150px" AutoPostBack="True">
                                        <asp:ListItem></asp:ListItem>
                                        <asp:ListItem Value="1">LENGKAP (MESYUARAT)</asp:ListItem>
                                        <asp:ListItem Value="2">LENGKAP (TPC)</asp:ListItem>
                                        <asp:ListItem Value="3">TIDAK LENGKAP</asp:ListItem>
                                    </asp:DropDownList>
                                </ContentTemplate>
                            </asp:UpdatePanel>
            </td>
                    </tr>
        <tr>
                        <td bgcolor="#999966" class="style127" valign="top" 
                            
                            style="font-weight: bold; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-variant: small-caps;">
                            &nbsp; Catatan</td>
                        <td bgcolor="#999966" valign="top" class="style128">
                <asp:TextBox ID="Tx_Catatan" runat="server" CssClass="std" Height="89px" 
                    TextMode="MultiLine" Width="325px"></asp:TextBox>
            </td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style96" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" valign="top">
                            &nbsp;</td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style96" valign="top">
                            &nbsp; KEPUTUSAN MESYUARAT<br />
                            &nbsp; JAWATANKUASA KELULUSAN
                            <br />
&nbsp; PENGAMBILAN JURURAWAT ASING<br />
&nbsp; &nbsp;</td>
                        <td bgcolor="#ffffff" valign="top">
                <asp:TextBox ID="Tx_Mesyuarat" runat="server" CssClass="std" Height="129px" 
                    TextMode="MultiLine" Width="331px" MaxLength="255"></asp:TextBox>
                                        </td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style121" valign="top">
                            &nbsp;TARIKH KELULUSAN&nbsp;</td>
                        <td bgcolor="#ffffff" class="style9" valign="top">
                            <asp:TextBox ID="Tx_Tkh_Lulus" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_Tkh_Lulus_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Lulus" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_Tkh_Lulus_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_Tkh_Lulus" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            &nbsp;-
                            <asp:TextBox ID="Tx_Tkh_Lulus0" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_Tkh_Lulus0_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Lulus0" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_Tkh_Lulus0_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_Tkh_Lulus0" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style121" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style9" valign="top">
                            &nbsp;</td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style121" valign="top">
                            </td>
                        <td bgcolor="#ffffff" class="style9" valign="top">
                <asp:Button ID="cmdHantar" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SIMPAN" Width="96px" />
            </td>
                    </tr>
        <tr>
            <td bgcolor="#ffffff" align="center" colspan="2" valign="middle">
                &nbsp;</td>
        </tr>
    </table></td></tr></table>
    
    </br></br>
    </div>

</asp:Content>

