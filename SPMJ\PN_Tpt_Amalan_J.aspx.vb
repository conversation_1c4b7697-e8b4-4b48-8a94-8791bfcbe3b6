﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient
Public Class PN_Tpt_Amalan_J
    Inherits System.Web.UI.Page
    Public Sub Reset()
        Session("PN_Pinda") = False
        Tx_Id.Text = ""
        Tx_Nama.Text = ""
        Tx_Alamat.Text = ""
        Tx_Alamat1.Text = ""
        Tx_Alamat2.Text = ""
        Tx_Bandar.Text = ""
        Tx_Poskod.Text = ""
        Tx_Fax.Text = ""
        Tx_Tel.Text = ""
        Cb_Sektor.SelectedIndex = 0
        cb_baru.Visible = False
    End Sub

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select id_amalan, dc_amalan as 'TEMPAT AMALAN', alamat, alamat1, alamat2, poskod, BANDAR, negeri, case sektor when 1 then 'K' when 2 then 'S' end as 'SEKTOR',sektor, tel, fax from pn_tpt_amalan " & X & " order by dc_amalan"
        Tb = "pn_tpt_amalan"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("PN", "Majikan", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PENYELENGGARAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'NEGERI
        Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()
        Cb_Negeri.Items.Clear()
        Cb_Negeri.Items.Add("")
        While Rdr.Read
            Cb_Negeri.Items.Add(Rdr(0))
            Cb_Negeri.Items.Item(Cb_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
        cb_baru.Visible = False
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        'e.Row.Cells(1).Visible = False
        e.Row.Cells(0).Wrap = False
        e.Row.Cells(2).Visible = False
        e.Row.Cells(4).Visible = False
        e.Row.Cells(5).Visible = False
        e.Row.Cells(6).Visible = False
        e.Row.Cells(7).Visible = False
        e.Row.Cells(9).Visible = False
        'e.Row.Cells(10).Visible = False
        e.Row.Cells(11).Visible = False
        e.Row.Cells(12).Visible = False
        e.Row.Cells(13).Visible = False
        e.Row.Cells(10).HorizontalAlign = HorizontalAlign.Center
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        If Session("PN_Padam") Then
            Session("PN_Padam") = False
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
            Dim SQL As String
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            'Comment Ori -08012013 -OSH 
            'SQL = "delete from pn_tpt_amalan where id_amalan=" & Gd.SelectedRow.Cells(2).Text

            'Improvement delete query replace with update status_record flags value 0 mean delete query -08012013 -OSH 
            SQL = "update pn_tpt_amalan set  Mod_Id = '" & Session("Id_PG") & "', Mod_Tkh = getdate(), Status_Rekod = '0' where id_amalan=" & Gd.SelectedRow.Cells(2).Text
            Try
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Reset()
                'Comment Ori 08012013 -OSH 
                'Cari("where negeri = '" & Cb_Negeri.SelectedValue & "'")

                'Improvement sort by active records 08012013 -OSH 
                Cari("where status_rekod='1' and negeri = '" & Cb_Negeri.SelectedValue & "'")
                Msg(Me, "Rekod Telah Dipadam...")
            Catch ex As Exception
                Msg(Me, "Error...")
            End Try
            Cn.Close()
            Exit Sub
        End If

        Session("PN_Pinda") = True
        Reset()
        cb_baru.Visible = True
        Select Case Gd.SelectedRow.Cells(10).Text
            Case "K"
                Cb_Sektor.SelectedIndex = 1
            Case "S"
                Cb_Sektor.SelectedIndex = 2
        End Select
        Tx_Id.Text = Gd.SelectedRow.Cells(2).Text
        Tx_Nama.Text = Gd.SelectedRow.Cells(3).Text
        If Gd.SelectedRow.Cells(4).Text <> "&nbsp;" Then Tx_Alamat.Text = Gd.SelectedRow.Cells(4).Text
        If Gd.SelectedRow.Cells(5).Text <> "&nbsp;" Then Tx_Alamat1.Text = Gd.SelectedRow.Cells(5).Text
        If Gd.SelectedRow.Cells(6).Text <> "&nbsp;" Then Tx_Alamat2.Text = Gd.SelectedRow.Cells(6).Text
        If Gd.SelectedRow.Cells(7).Text <> "&nbsp;" Then Tx_Poskod.Text = Gd.SelectedRow.Cells(7).Text
        If Gd.SelectedRow.Cells(8).Text <> "&nbsp;" Then Tx_Bandar.Text = Gd.SelectedRow.Cells(8).Text
        If Gd.SelectedRow.Cells(12).Text <> "&nbsp;" Then Tx_Tel.Text = Gd.SelectedRow.Cells(12).Text
        If Gd.SelectedRow.Cells(13).Text <> "&nbsp;" Then Tx_Fax.Text = Gd.SelectedRow.Cells(13).Text
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim X As String = ""
        'If Cb_Negeri.SelectedIndex < 1 Then Exit Sub
        'If Cb_Sektor.SelectedIndex < 1 Then Exit Sub
        'If Tx_Nama.Text = "" Then Exit Sub
        If Cb_Negeri.SelectedIndex < 1 Then X += "Negeri, "
        If Cb_Sektor.SelectedIndex < 1 Then X += "Sektor, "
        If Tx_Nama.Text = "" Then X += "Tempat Amalan, "
        If Tx_Alamat.Text = "" Then X += "Alamat, "
        If Tx_Poskod.Text = "" Then X += "Poskod, "
        If Tx_Bandar.Text = "" Then X += "Bandar, "
        If X.Trim = "" Then Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut: " + X) : Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim SQL As String

        If Tx_Id.Text = "" Then
        Else
            Try
                'Comment Ori- 08012013-OSH
                'SQL = "update pn_tpt_amalan set "
                'SQL += "dc_amalan = '" & Tx_Nama.Text & "', "
                'SQL += "alamat = '" & Tx_Alamat.Text & "', "
                'SQL += "alamat1 = '" & Tx_Alamat1.Text & "', "
                'SQL += "alamat2 = '" & Tx_Alamat2.Text & "', "
                'SQL += "poskod = '" & Tx_Poskod.Text & "', "
                'SQL += "bandar = '" & Tx_Bandar.Text & "', "
                'SQL += "negeri = '" & Cb_Negeri.Text & "', "
                'SQL += "sektor = " & Cb_Sektor.Text & ", "
                'SQL += "tel = '" & Tx_Tel.Text & "', "
                'SQL += "fax = '" & Tx_Fax.Text & "' "
                'SQL += "where id_amalan = " & Tx_Id.Text

                'Improvement update records which starus records is value 1 means active insert record - 08012013-OSH
                SQL = "update pn_tpt_amalan set "
                SQL += "dc_amalan = '" & Tx_Nama.Text & "', "
                SQL += "alamat = '" & Tx_Alamat.Text & "', "
                SQL += "alamat1 = '" & Tx_Alamat1.Text & "', "
                SQL += "alamat2 = '" & Tx_Alamat2.Text & "', "
                SQL += "poskod = '" & Tx_Poskod.Text & "', "
                SQL += "bandar = '" & Tx_Bandar.Text & "', "
                SQL += "negeri = '" & Cb_Negeri.Text & "', "
                SQL += "sektor = " & Cb_Sektor.Text & ", "
                SQL += "tel = '" & Tx_Tel.Text & "', "
                SQL += "fax = '" & Tx_Fax.Text & "', "
                SQL += "up_id = '" & Session("Id_PG") & "', "
                SQL += "up_tkh = getdate() , "
                SQL += "status_up = '1' "
                SQL += "where status_rekod ='1' and id_amalan = " & Tx_Id.Text
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Cn.Close()
                Reset()

                'Comment Ori 08012013 -OSH 
                'Cari("where negeri = '" & Cb_Negeri.SelectedValue & "'")

                'Improvement sort by active records 08012013 -OSH 
                Cari("where status_rekod='1' and negeri = '" & Cb_Negeri.SelectedValue & "'")
                Msg(Me, "Rekod Telah Dikemaskini...")
                Session("PN_Pinda") = False
                Exit Sub
            Catch ex As Exception
                Msg(Me, "Error...")
            End Try
        End If
        'Comment Ori 08012012 -OSH 
        'SQL = "select * from pn_tpt_amalan where dc_amalan='" & Tx_Nama.Text.Trim & "'"

        'Improvement sort by active records without duplicate  08012012 -OSH 
        'SQL = "select * from pn_tpt_amalan where dc_amalan='" & Tx_Nama.Text.Trim & "' and Status_Rekod = '1'"

        'Fix Apostrophe1 issue 12072023 - OSH
        SQL = "select * from pn_tpt_amalan where dc_amalan='" & Apo(Tx_Nama.Text.Trim) & "' and Status_Rekod = '1'"
        Cmd.CommandText = SQL
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Telah Ada!!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()

        'Comment Ori Query 08012013 -OSH 
        'SQL = "insert pn_tpt_amalan (dc_amalan, alamat, alamat1, alamat2, poskod, bandar, negeri, sektor, tel, fax) "
        'SQL += "select '" & Tx_Nama.Text & "',  '" & Tx_Alamat.Text & "',  '" & Tx_Alamat1.Text & "',  '" & Tx_Alamat2.Text & "',  '" & Tx_Poskod.Text & "',  '" & Tx_Bandar.Text & "',  " & Cb_Negeri.SelectedValue & ",  " & Cb_Sektor.SelectedValue & ",  '" & Tx_Tel.Text & "',  '" & Tx_Fax.Text & "'"

        ''Improvement Query 08012013 -add logging -OSH 
        'SQL = "insert pn_tpt_amalan (dc_amalan, alamat, alamat1, alamat2, poskod, bandar, negeri, sektor, tel, fax, Log_Id, Log_Tkh, Status_Rekod) "
        'SQL += "select '" & Tx_Nama.Text & "',  '" & Tx_Alamat.Text & "',  '" & Tx_Alamat1.Text & "',  '" & Tx_Alamat2.Text & "',  '" & Tx_Poskod.Text & "',  '" & Tx_Bandar.Text & "',  " & Cb_Negeri.SelectedValue & ",  " & Cb_Sektor.SelectedValue & ",  '" & Tx_Tel.Text & "',  '" & Tx_Fax.Text & "', '" & Session("Id_PG") & "',getdate(),'1'"

        'Fix Single Quote ' Error 23062023-OSH 
        SQL = "insert pn_tpt_amalan (dc_amalan, alamat, alamat1, alamat2, poskod, bandar, negeri, sektor, tel, fax, Log_Id, Log_Tkh, Status_Rekod) "
        SQL += "select '" & Apo(Tx_Nama.Text.Trim) & "',  '" & Apo(Tx_Alamat.Text.Trim) & "',  '" & Apo(Tx_Alamat1.Text.Trim) & "',  '" & Apo(Tx_Alamat2.Text.Trim) & "',  '" & Apo(Tx_Poskod.Text.Trim) & "',  '" & Apo(Tx_Bandar.Text.Trim) & "',  " & Cb_Negeri.SelectedValue & ",  " & Cb_Sektor.SelectedValue & ",  '" & Tx_Tel.Text & "',  '" & Tx_Fax.Text & "', '" & Session("Id_PG") & "',getdate(),'1'"
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Msg(Me, "Rekod Telah Disimpan...")
            'Comment Ori 08012013 -OSH 
            'Cari("where negeri = '" & Cb_Negeri.SelectedValue & "'")

            'Improvement sort by active records 08012013 -OSH 
            Cari("where status_rekod='1' and negeri = '" & Cb_Negeri.SelectedValue & "'")
        Catch ex As Exception
            Msg(Me, "Error...")
        End Try
        Cn.Close()
        Reset()
        Cari("where negeri = '" & Cb_Negeri.SelectedValue & "'")
    End Sub

    Protected Sub Cb_Negeri_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Negeri.SelectedIndexChanged
        'Comment Ori 08012013 -OSH 
        'Cari("where negeri = '" & Cb_Negeri.SelectedValue & "'")

        'Improvement active record only  08012013 -OSH 
        Cari("where status_rekod = '1' and negeri = '" & Cb_Negeri.SelectedValue & "'")
    End Sub

    Protected Sub Cb_Sektor_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Sektor.SelectedIndexChanged
        If Cb_Negeri.SelectedIndex = 0 Then Cb_Negeri.Focus() : Exit Sub
        If Cb_Sektor.SelectedIndex = 0 Then
            'Comment Ori 08012013 -OSH 
            'Cari("where negeri = '" & Cb_Negeri.SelectedValue & "'")

            'Improvement active record only  08012013 -OSH 
            Cari("where status_rekod = '1' and negeri = '" & Cb_Negeri.SelectedValue & "'")
        Else
            'Comment Ori 08012013 -OSH 
            'Cari("where negeri = '" & Cb_Negeri.SelectedValue & "' and sektor = '" & Cb_Sektor.SelectedValue & "'")

            'Improvement active record only  08012013 -OSH 
            Cari("where status_rekod = '1' and negeri = '" & Cb_Negeri.SelectedValue & "' and sektor = '" & Cb_Sektor.SelectedValue & "'")
        End If
    End Sub

    Protected Sub cb_baru_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cb_baru.Click
        Reset()
        cb_baru.Visible = False
    End Sub

    Protected Sub Cb_Padam_Click(ByVal sender As Object, ByVal e As EventArgs)
        Session("Pn_Padam") = True
    End Sub

    Protected Sub cb_cari_Click(sender As Object, e As EventArgs) Handles cb_cari.Click

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim SQL As String

        lblCarian.Text = ""

        If Tx_Nama.Text <> "" Then

            SQL = "select * from pn_tpt_amalan where dc_amalan like '%" & Apo(Tx_Nama.Text.Trim) & "%'"
            Cmd.CommandText = SQL
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                Cari("where dc_amalan like '%" & Apo(Tx_Nama.Text.Trim) & "%'")
                Rdr.Close()
            Else
                lblCarian.Text = "Carian " & Tx_Nama.Text.Trim & "- Rekod Fasiliti Ini Tidak Ada Dalam Pangkalan Data!!"
                Cari("where dc_amalan like '%" & Apo(Tx_Nama.Text.Trim) & "%'")
                Exit Sub
            End If
            Rdr.Close()

        End If
    End Sub
End Class