﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="PN_Jawatan.aspx.vb" Inherits="SPMJ.WebForm44" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 23px;
        }
        .style2
        {
            height: 7px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td></td>
            <td width="600"></td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style1"></td>
            <td width="600" align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#719548" class="style1">penyelenggaraan&nbsp; - Peralihan Jawatan</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td></td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <asp:TextBox ID="Cb_Sbj3" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">NO. KP</asp:TextBox>
                                                 <asp:TextBox ID="Tx_NoKP" runat="server" 
                    CssClass="std" Width="190px" 
                                                     Wrap="False"></asp:TextBox>
                <asp:Button ID="cmd_Semak" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SEMAK" Width="80px" />
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td></td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px"  Enabled="False" 
                            ReadOnly="True">NAMA</asp:TextBox>
                <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="350px" 
                    Wrap="False" ReadOnly="True"></asp:TextBox>
            </td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj1" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px"  Enabled="False" 
                            ReadOnly="True">NO. DAFTAR</asp:TextBox>
                                                 <asp:TextBox ID="Tx_NoPd" runat="server" 
                    CssClass="std" Width="190px" 
                                                     Wrap="False" ReadOnly="True"></asp:TextBox>
                <asp:CheckBox ID="chkUndo" Font-Names="Arial" 
                                            Font-Size="8pt" runat="server" Text="KEMBALI" 
                            AutoPostBack="True" Visible="False" />
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style2"></td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style2">
                &nbsp;&nbsp;</td>
            <td class="style2"></td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">
                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj6" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px"  Enabled="False" 
                            ReadOnly="True">KE JAWATAN</asp:TextBox>
                        <asp:DropDownList ID="Cb_Jawatan" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="192px">
                        </asp:DropDownList>
                        &nbsp;&nbsp;&nbsp;&nbsp;                        
                        <asp:TextBox ID="Tx_Tkh" runat="server" CssClass="std" Width="95px" 
                            Visible="False"></asp:TextBox>
                        <cc1:MaskedEditExtender ID="Tx_Tkh_MaskedEditExtender" runat="server" 
                            CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                            CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                            CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                            Enabled="True" Mask="99/99/9999" MaskType="Date" 
                            TargetControlID="Tx_Tkh" UserDateFormat="DayMonthYear">
                        </cc1:MaskedEditExtender>
                        <cc1:CalendarExtender ID="Tx_Tkh_CalendarExtender" runat="server" Enabled="True" 
                            Format="dd/MM/yyyy" PopupPosition="Right" TargetControlID="Tx_Tkh">
                        </cc1:CalendarExtender>
                    </ContentTemplate>                   
                </asp:UpdatePanel>
            </td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="TextBox1" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px"  Enabled="False" 
                            ReadOnly="True">NO. DAFTAR</asp:TextBox>
                                                 <asp:TextBox ID="Tx_NoPd_Baru" runat="server" 
                    CssClass="std" Width="190px" 
                                                     Wrap="False"></asp:TextBox>
                 <asp:Button ID="cmd_Cari" runat="server" Font-Names="Arial" Font-Size="8pt" 
                            Height="20px" tabIndex="3" Text="CARI" Width="80px" />
            </td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="TextBox2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">TARIKH DAFTAR</asp:TextBox>
                        <asp:TextBox ID="Tx_Tkh_Daftar" runat="server" CssClass="std" 
                    Width="80px"></asp:TextBox>
                        <cc1:MaskedEditExtender ID="Tx_Tkh_Daftar_MaskedEditExtender" runat="server" 
                            CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                            CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                            CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                            Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Daftar" 
                            UserDateFormat="DayMonthYear">
                        </cc1:MaskedEditExtender>
                        <cc1:CalendarExtender ID="Tx_Tkh_Daftar_CalendarExtender" runat="server" 
                            Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                            TargetControlID="Tx_Tkh_Daftar">
                        </cc1:CalendarExtender>
            </td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style2">
                &nbsp;</td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px"></asp:TextBox>
                <asp:Button ID="cmd_Simpan" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SIMPAN" Width="80px" />
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; border-bottom-style: solid; border-bottom-width: 1px;" 
                bgcolor="White">
                &nbsp;&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td></td>
            <td width="600">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td width="600">
                &nbsp;</td>
            <td></td>
        </tr>
    
    
        </table>
    
    
    </div></asp:Content>