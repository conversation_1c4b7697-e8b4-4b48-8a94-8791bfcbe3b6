﻿<%@ Master Language="VB" AutoEventWireup="false" CodeBehind="Main.master.vb" Inherits="SPMJ.Main" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%--<link href="/css/Layout.css" rel="stylesheet" type="text/css" />--%>
    
<style type="text/css">
        .std_hd
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             color: #ffffff;
             font-variant :small-caps ;
             background-color: #6699cc;
        }
        .std_hd2
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             font-variant :small-caps ;             
             color: #ffffff;
        }
        .menu_big
        {
             font-family: Arial;
             font-size: 7pt; 
             color: #ffffff;
        }
        .menu_small
        {
             font-family: Arial;
             font-size: 7pt; 
             color: #ffffff;
        }
        .shadow
        {
        	float:left;

        }
        .std
        {
             font-family: Arial;
             font-size: 8pt; 
             color: #000000;
        }
        </style>
<html xmlns="http://www.w3.org/1999/xhtml" >
<head id="Head1" runat="server">
    <title>SPMJ</title>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body  style="margin: 0px;padding: 0; background-color: #FFFFFF; background-image: url('Image/Bg_Sgt.gif'); background-attachment: fixed;">
    <form id="form1" runat="server">
    <div style="margin: 0px;padding: 0; " align="center">
        <table cellpadding="-1" cellspacing="-1" 
            style="border-style: none; width: 1024px;">
            <tr>
                <td align="center" bgcolor="Black">
        <asp:ScriptManager ID="ScriptManager1" runat="server">
        </asp:ScriptManager><asp:Image ID="Image1" 
                runat="server" style="margin: 0px;" ImageUrl="~/Image/Hd_Top.gif"  Width="1024px"/></td></tr>
            <tr align="left" bgcolor="Black">
                <td><asp:Menu ID="Menu1" runat="server" 
                BackColor="#5E8635" BorderColor="#003300" 
    BorderStyle="Solid" BorderWidth="1px" 
    DynamicPopOutImageTextFormatString="" 
    Font-Bold="True" Font-Names="Arial" Font-Size="7pt" 
    ForeColor="White" MaximumDynamicDisplayLevels="2" 
    Orientation="Horizontal" StaticPopOutImageTextFormatString="" 
    StaticSubMenuIndent="" 
    style="color: #FFFFFF;left: -1px; margin-left: 0px; margin-right: 0px; margin-top: 0px;" 
                        Width="1024px" Font-Strikeout="False" 
                DynamicHorizontalOffset="-1" StaticEnableDefaultPopOutImage="False" 
                >
                        <StaticSelectedStyle BackColor="White" ForeColor="Black" />
                        <LevelSubMenuStyles>
                            <asp:SubMenuStyle Font-Underline="False" />
                            <asp:SubMenuStyle Font-Underline="False" />
                            <asp:SubMenuStyle Font-Underline="False" />
                            <asp:SubMenuStyle Font-Underline="False" />
                        </LevelSubMenuStyles>
                        <StaticMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" 
                                   Width="20px" />
                        <DynamicHoverStyle Font-Bold="True" Font-Italic="False" 
        Font-Names="Arial" Font-Size="7pt" ForeColor="White" BackColor="#669900" />
                        <DynamicMenuStyle BackColor="White" BorderColor="#003300" BorderStyle="Solid" 
        BorderWidth="1px" HorizontalPadding="0px" VerticalPadding="0px" CssClass="shadow" />
                        <DynamicItemTemplate>
                            <%#Eval("Text", "")%>
                        </DynamicItemTemplate>
                        <DynamicSelectedStyle BackColor="#FFCC00" Font-Bold="True" ForeColor="Black" />
                        <DynamicMenuItemStyle Font-Bold="True" 
        Font-Names="Arial" Font-Size="7pt" ForeColor="#003300" 
        HorizontalPadding="5px" ItemSpacing="0px" VerticalPadding="3px" />
                        <StaticHoverStyle ForeColor="#006699" BackColor="White" BorderStyle="None" />
                        <Items>
                            <asp:MenuItem Text="PEPERIKSAAN" Value="a">                                                                
                <%--                 <asp:MenuItem Text="PENDAFTARAN PELATIH (BARU)" Value="a8" 
                                           NavigateUrl="~/P1_Pelatih_Daftar4.aspx"></asp:MenuItem>                                            
                                 <asp:MenuItem NavigateUrl="~/P1_Pelatih_Daftar5.aspx"  
                                     Text="PENDAFTARAN PELATIH (IJAZAH)" Value="a9"></asp:MenuItem> --%>
                              <asp:MenuItem Text="PINDA/SEMAK REKOD PELATIH" 
                                           Value="a2" NavigateUrl="~/P1_Pelatih_Cari.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="SARING PELATIH UNTUK PEPERIKSAAN" 
                                           Value="a3" NavigateUrl="~/P1_Pelatih_Saring.aspx" SeparatorImageUrl="~/Image/Border300.gif"></asp:MenuItem>
                                <asp:MenuItem NavigateUrl="~/P1_XM_JanaCalon.aspx" 
                                           Text="JANA CALON PEPERIKSAAN" Value="a4"></asp:MenuItem>
                                <asp:MenuItem Text="SEMAK CALON PEPERIKSAAN" Value="a5" 
                                           NavigateUrl="~/P1_XM_SemakCalon.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="CETAK SLIP NO. PEPERIKSAAN" Value="a6" 
                                    SeparatorImageUrl="~/Image/Border300.gif"></asp:MenuItem>
                                <asp:MenuItem Text="ISI MARKAH PEPERIKSAAN" Value="a7" 
                                           NavigateUrl="~/P1_XM_Markah.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="SAH MARKAH PEPERIKSAAN" Value="a7i" 
                                           NavigateUrl="~/P1_XM_Markah_Sah.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="JANA KEPUTUSAN PEPERIKSAAN" Value="a9" 
                                           NavigateUrl="~/P1_XM_JanaKeputusan.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="SAH KEPUTUSAN PEPERIKSAAN" Value="a10" 
                                           NavigateUrl="~/P1_XM_SahKeputusan.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="CETAK SLIP KEPUTUSAN PEPERIKSAAN" Value="a11" 
                                    SeparatorImageUrl="~/Image/Border300.gif">
                                </asp:MenuItem>
                                <asp:MenuItem NavigateUrl="~/LP_XM.aspx" Text="LAPORAN" Value="LAPORAN">
                                </asp:MenuItem>
                                <asp:MenuItem NavigateUrl="~/LP_STAT_XM.aspx" Text="STATISTIK KEPUTUSAN PEPERIKSAAN" Value="STATISTIK KEPUTUSAN PEPERIKSAAN">
                                </asp:MenuItem>
                                <asp:MenuItem NavigateUrl="~/LP_STAT_XM_Markah.aspx" Text="STATISTIK MARKAH PEPERIKSAAN" Value="STATISTIK MARKAH PEPERIKSAAN">
                                </asp:MenuItem>   
                                <asp:MenuItem NavigateUrl="~/LP_STAT_XM_Distribution.aspx" Text="STATISTIK TABURAN MARKAH PEPERIKSAAN" Value="STATISTIK TABURAN MARKAH PEPERIKSAAN">
                                </asp:MenuItem> 
                                <asp:MenuItem NavigateUrl="~/LP_STAT_XM_Mark.aspx" Text="STATISTIK ANALISA MARKAH" Value="STATISTIK ANALISA MARKAH">
                                </asp:MenuItem>                                                             
                                <asp:MenuItem NavigateUrl="~/LP_STAT_XM_Ques.aspx" Text="STATISTIK SOALAN PEPERIKSAAN" Value="STATISTIK SOALAN PEPERIKSAAN">
                                </asp:MenuItem>
                                 <asp:MenuItem NavigateUrl="~/LP_STAT_XM_Analys.aspx" Text="STATISTIK ANALISA SOALAN PEPERIKSAAN" Value="STATISTIK ANALISA SOALAN PEPERIKSAAN">
                                </asp:MenuItem>
                                <asp:MenuItem Text="SENARAI CALON BARU" Value="a120"></asp:MenuItem>
                                <asp:MenuItem Text="SENARAI CALON ULANGAN" Value="a121"></asp:MenuItem>
                                <asp:MenuItem Text="SENARAI CALON MENUMPANG" Value="a123"></asp:MenuItem>                                
                                <asp:MenuItem Text="STATISTIK KEPUTUSAN PEPERIKSAAN" Value="a122"></asp:MenuItem>
                                <asp:MenuItem Text="SENARAI CALON MARKAH TERTINGGI/TERENDAH" Value="a124"></asp:MenuItem>  
                               <%-- <asp:MenuItem Text="SENARAI CALON GAGAL (test)" Value="a125"></asp:MenuItem>  --%>  
                                <asp:MenuItem Text="STATISTIK CALON GAGAL" Value="a126"></asp:MenuItem>      
                                <asp:MenuItem Text="STATISTIK CALON BARU LULUS" Value="a127"></asp:MenuItem>       
                                 <asp:MenuItem Text="STATISTIK CALON MARKAH TERRENDAH" Value="a128"></asp:MenuItem>   
                                 <asp:MenuItem Text="STATISTIK CALON MARKAH TERTINGGI" Value="a129"></asp:MenuItem>    
                                 <asp:MenuItem Text="SENARAI CALON TUMPANG" Value="a130"></asp:MenuItem>           
                                 <asp:MenuItem Text="SENARAI CALON ULANGAN" Value="a131"></asp:MenuItem>                  
                            </asp:MenuItem>
                            <asp:MenuItem Text="PENDAFTARAN PENUH" Value="b">
                                <asp:MenuItem Text="PENDAFTARAN BARU" Value="b1">
                                </asp:MenuItem>
                                <asp:MenuItem Text="PENGESAHAN PENDAFTARAN" Value="b4" 
                                    NavigateUrl="~/P2_Daftar_Sah.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="IMBAS FOTO" 
                                    Value="b8"></asp:MenuItem>
                                <asp:MenuItem Text="PINDA/SEMAK REKOD" Value="b2" NavigateUrl="~/P2_Semak.aspx" 
                                    SeparatorImageUrl="~/Image/Border300.gif"></asp:MenuItem>
                                <asp:MenuItem Text="CETAK SIJIL PERAKUAN PENDAFTARAN PENUH &amp; TAMBAHAN" 
                                    Value="b5">
                                </asp:MenuItem>
                                <asp:MenuItem Text="CETAK SLIP AKUAN TERIMA" Value="b7" 
                                    SeparatorImageUrl="~/Image/Border300.gif"></asp:MenuItem>
                                <asp:MenuItem NavigateUrl="~/LP_Daftar.aspx" Text="LAPORAN" 
                                    Value="LAPORAN SENARAI PENGELUARAN APC"></asp:MenuItem>
                                    <asp:MenuItem Text="SENARAI PENDAFTARAN PENUH" Value= 'b9'></asp:MenuItem>
                               <%-- <asp:MenuItem NavigateUrl="~/P2_SN_Penuh.aspx" Text="SENARAI PENDAFTARAN PENUH" 
                                    Value="SENARAI PENDAFTARAN PENUH"></asp:MenuItem>--%>
                                <asp:MenuItem NavigateUrl="~/LP_P2_Sah_Nama.aspx" Text="STATISTIK PENGESAHAN PENDAFTARAN" 
                                    Value="STATISTIK PENGESAHAN PENDAFTARAN"></asp:MenuItem>
                            </asp:MenuItem>
                            <asp:MenuItem Text="PENDAFTARAN TPC" Value="c">
                                <asp:MenuItem Text="PENDAFTARAN BARU" Value="c1" ></asp:MenuItem>
                                <asp:MenuItem Text="PINDA/SEMAK REKOD" Value="c2" NavigateUrl="~/P4_Semak.aspx" 
                                    SeparatorImageUrl="~/Image/Border650.gif"></asp:MenuItem>
                                <asp:MenuItem Text="IMBAS FOTO" Value="c3">
                                </asp:MenuItem>
                                <asp:MenuItem Text="PROSES TPC (BARU)" Value="c4" 
                                    NavigateUrl="~/P4_Daftar_Sah.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="PROSES TPC (ULANGAN)" Value="e2" 
                                    NavigateUrl="~/P4_TPC_Cari.aspx" SeparatorImageUrl="~/Image/Border650.gif"></asp:MenuItem>
                                <asp:MenuItem Text="CETAK SIJIL TPC" Value="c6"></asp:MenuItem>
                                <asp:MenuItem Text="CETAK SURAT AKUAN TERIMA (INDIVIDU)" Value="c7" 
                                    NavigateUrl="~/P4_Cetak_Akuan.aspx"></asp:MenuItem>
                                <asp:MenuItem 
                                    Text="CETAK SURAT AKUAN TERIMA (MAJIKAN)" 
                                    Value="CETAK SURAT AKUAN TERIMA (MAJIKAN)" 
                                    NavigateUrl="~/P4_Cetak_Akuan2.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="KELULUSAN PERLANJUTAN PERKHIDMATAN PENGAJAR JURURAWAT, INSTRUKTOR KLINIKAL & JURURAWAT TERLATIH WARGANEGARA ASING" 
                                    Value="CETAK SURAT - KEPUTUSAN MESYUARAT (IMIGRESEN)" 
                                    NavigateUrl="~/P4_Cetak3.aspx" SeparatorImageUrl="~/Image/Border650.gif"></asp:MenuItem>
                                <asp:MenuItem NavigateUrl="~/P4_Cetak.aspx" 
                                    Text="CETAK SURAT KEPUTUSAN MESYUARAT BAGI KELULUSAN TPC - (INDIVIDU)" 
                                    Value="CETAK SURAT KEPUTUSAN MESYUARAT BAGI KELULUSAN TPC - (INDIVIDU)">
                                </asp:MenuItem>
                                <asp:MenuItem Text="CETAK SURAT KEPUTUSAN MESYUARAT BAGI KELULUSAN TPC - (MAJIKAN)" 
                                    Value="s1" NavigateUrl="~/P4_Cetak2.aspx">
                                </asp:MenuItem>
                                <asp:MenuItem NavigateUrl="~/P4_Cetak5.aspx" 
                                    Text="CETAK SURAT KEPUTUSAN MESYUARAT BAGI KELULUSAN TPC - (IMIGRESEN)" 
                                    Value="CETAK SURAT KEPUTUSAN MESYUARAT BAGI KELULUSAN TPC - (IMIGRESEN)">
                                </asp:MenuItem>
                                <asp:MenuItem Text="CETAK SURAT KEPUTUSAN MESYUARAT  BAGI KELULUSAN TPC - (KPT)" 
                                    Value="CETAK SURAT - KELULUSAN TPC (INDIVIDU)" 
                                    NavigateUrl="~/P4_Cetak4.aspx" SeparatorImageUrl="~/Image/Border650.gif"></asp:MenuItem>
                                <asp:MenuItem NavigateUrl="~/LP_TPC.aspx" Text="LAPORAN" Value="TPC - SENARAI">
                                </asp:MenuItem>
                                <asp:MenuItem NavigateUrl="~/LP_TPC_Ulang.aspx" Text="LAPORAN TPC (TEMPOH)" Value="TPC - SENARAI">
                                </asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENDAFTARAN TPC - WARGANEGARA, NEGERI AMALAN" Value="c10">
                                </asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENDAFTARAN TPC - MAJIKAN, JANTINA" Value="c11">
                                </asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENDAFTARAN TPC - NEGARA, JANTINA" Value="c12">
                                </asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENDAFTARAN TPC - MAJIKAN, JAWATAN, JANTINA" Value="c13">
                                </asp:MenuItem>                             
                            </asp:MenuItem>
                            <asp:MenuItem Text="PEMBAHARUAN APC" Value="d">
                                <asp:MenuItem Text="PINDA/SEMAK REKOD" Value="d1" NavigateUrl="~/P2_Semak.aspx" 
                                    SeparatorImageUrl="~/Image/Border400.gif"></asp:MenuItem>
                                <asp:MenuItem Text="PROSES APC - MANUAL" Value="d2" 
                                    NavigateUrl="~/P3_APC_Cari.aspx"></asp:MenuItem>
                                <%--    <asp:MenuItem Text="PROSES APC - MANUAL" Value="d2" 
                                    NavigateUrl="~/P3_APC_Cari2.aspx"></asp:MenuItem>--%>
                                <asp:MenuItem Text="PROSES APC - ONLINE" Value="d3"></asp:MenuItem>
                                <%--<asp:MenuItem Text="PROSES PENGEKALAN NAMA" Value="d4" 
                                    NavigateUrl="~/P3_RET_Cari.aspx" SeparatorImageUrl="~/Image/Border400.gif"></asp:MenuItem>--%>
                                     <asp:MenuItem Text="PROSES PENGEKALAN NAMA" Value="d4"
                                     NavigateUrl="~/P3_RET_Cari.aspx"></asp:MenuItem>
                                     <asp:MenuItem Text="PEMBATALAN PENGEKALAN NAMA" Value="d8" SeparatorImageUrl="~/Image/Border400.gif">
                                     </asp:MenuItem>
                                <asp:MenuItem Text="CETAK SIJIL APC" Value="d5"></asp:MenuItem>
                                <asp:MenuItem Text="CETAK SLIP AKUAN TERIMA" Value="d6"></asp:MenuItem>
                                <asp:MenuItem Text="CETAK SALINAN PENDUA APC" Value="d7" 
                                    SeparatorImageUrl="~/Image/Border400.gif"></asp:MenuItem>
                                <asp:MenuItem NavigateUrl="~/LP_APC.aspx" Text="LAPORAN" 
                                    Value="PEMBAHARUAN APC "></asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENGELUARAN APC" Value="d10">
                                </asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENGELUARAN APC - NEGERI, SEKTOR" Value="d13">
                                </asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENGELUARAN APC - NEGERI, SEKTOR, BANGSA" Value="d11">
                                </asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENGELUARAN APC - NEGERI, SEKTOR, JANTINA" Value="d12">
                                </asp:MenuItem>
                                 <asp:MenuItem Text="STATISTIK PENGELUARAN APC - NEGERI, JAWATAN, JANTINA" Value="d14">
                                </asp:MenuItem>
                                 <asp:MenuItem Text="STATISTIK PENGELUARAN APC - NEGERI, TAJAAN" Value="d15">
                                </asp:MenuItem>
                                 <asp:MenuItem Text="STATISTIK PENGELUARAN APC - NEGERI, GRED JAWATAN" Value="d16">
                                </asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENGEKALAN NAMA - BULAN, JANTINA" Value="d17">
                                </asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENGEKALAN NAMA - TAHUN, JANTINA" Value="d18">
                                </asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENGEKALAN NAMA - NEGARA, BULAN" Value="d19">
                                </asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENGEKALAN NAMA - SEBAB PENGEKALAN, BULAN" Value="d20">
                                </asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENGELUARAN PENGEKALAN NAMA (BULANAN) - KATEGEORI PENDFTARAN" Value="d21">
                                </asp:MenuItem>
                                <asp:MenuItem Text="STATISTIK PENGELUARAN PENGEKALAN NAMA (BULANAN) - TAHUN PENGEKALAN" Value="d22">
                                </asp:MenuItem>
                            </asp:MenuItem>
                            <asp:MenuItem Text="NOTIS NIAT" Value="f">
                                <asp:MenuItem Text="PINDA/SEMAK REKOD" Value="f1" NavigateUrl="~/P2_Semak.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="PROSES NOTIS NIAT" Value="f2" NavigateUrl="~/P5_Cari.aspx" 
                                    SeparatorImageUrl="~/Image/Border200.gif"></asp:MenuItem>
                                <asp:MenuItem Text="CETAK SURAT NOTIS NIAT" Value="f3" 
                                    NavigateUrl="~/P5_Cetak.aspx"></asp:MenuItem>
                            </asp:MenuItem>
                            <asp:MenuItem Text="PENYELENGGARAAN" Value="h">
                                <asp:MenuItem Text="PENGGUNA" Value="z1"></asp:MenuItem>
                                <asp:MenuItem Text="PENGGUNA KOLEJ" Value="z1a" 
                                    SeparatorImageUrl="~/Image/Border200.gif"></asp:MenuItem>
                               <%-- <asp:MenuItem Text="SIRI PEPERIKSAAN" Value="z2" NavigateUrl="~/PN_Siri.aspx"></asp:MenuItem>--%>
                                <asp:MenuItem Text="SIRI PEPERIKSAAN" Value="z2" NavigateUrl="~/PN_Siri2.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="INSTITUSI LATIHAN" Value="z3"></asp:MenuItem>
                                <asp:MenuItem Text="MESEJ UNTUK PENGGUNA KOLEJ" Value="z3a"></asp:MenuItem>
                                <asp:MenuItem Text="NEGARA/WARGANEGARA" Value="z5"></asp:MenuItem>
                                <asp:MenuItem Text="ETNIK" Value="z6"></asp:MenuItem>
                                <asp:MenuItem Text="GRED JAWATAN" Value="z8"></asp:MenuItem>
                                <asp:MenuItem Text="TAJAAN" Value="z9"></asp:MenuItem>
                                <asp:MenuItem Text="MAJIKAN/ TEMPAT AMALAN" Value="z10" 
                                    NavigateUrl="~/PN_Tpt_Amalan.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="DISIPLIN" Value="z7"></asp:MenuItem>
                                <asp:MenuItem Text="KRITERIA CPD" Value="z14"></asp:MenuItem>
                                <asp:MenuItem Text="SUBJEK" Value="z21"></asp:MenuItem>
                                <asp:MenuItem Text="PENGEKALAN NAMA" Value="z19"></asp:MenuItem>
                                <asp:MenuItem Text="KELAYAKAN IKHTISAS" Value="z15"></asp:MenuItem>
                                <asp:MenuItem Text="KELAYAKAN AKADEMIK" Value="z16" 
                                    SeparatorImageUrl="~/Image/Border200.gif"></asp:MenuItem>
                                <asp:MenuItem Text="NO. PENDAFTARAN" Value="z4"></asp:MenuItem>
                                <asp:MenuItem Text="NO. SIRI APC" Value="z12" 
                                    SeparatorImageUrl="~/Image/Border200.gif"></asp:MenuItem>
                                <asp:MenuItem Text="PERALIHAN JAWATAN" Value="z17" 
                                    NavigateUrl="~/PN_Jawatan.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="PINDA NO. KAD PENGENALAN" Value="z18" 
                                    NavigateUrl="~/PN_NoKP.aspx"></asp:MenuItem>
                                <asp:MenuItem Text="TUKAR KATA LALUAN" Value="z20"></asp:MenuItem>
                            </asp:MenuItem>
                            <asp:MenuItem Text="KELUAR" Value="z" NavigateUrl="~/P0_Login.aspx"></asp:MenuItem>
                            <asp:MenuItem Selectable="False" 
                                Text="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" 
                                Value="">
                            </asp:MenuItem>
                        </Items>
                    </asp:Menu>
                </td>
            </tr>
            <tr>
                <td style="border-style: none solid solid solid; border-width: 1px; border-color: #000000" bgcolor="White" align="left">
        <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
    </asp:ContentPlaceHolder>
                </td>
            </tr>
        </table>
    </div>
                    </form>
</body>
</html>