﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm55
    Inherits System.Web.UI.Page

    Public Sub Reset()
        Session("PN_Pinda") = False
        Tx_Id.ReadOnly = False
        Tx_Id.Text = ""
        Tx_Kolej.Text = ""
        Cb_Jenis.SelectedIndex = 0
        cmd_baru.Visible = False
    End Sub

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        'SQL = "select dc_kolej 'INSTITUSI LATIHAN', id_kolej, case jenis when 1 then 'K' when 2 then 'S'  when 3 then 'LN' else '' end 'JENIS' from pn_kolej order by jenis, dc_kolej"
        SQL = "select dc_kolej 'INSTITUSI LATIHAN', id_kolej, case jenis when 1 then 'K' when 2 then 'S'  when 3 then 'LN' else '' end 'JENIS' from pn_kolej where status = '1' order by jenis, dc_kolej"
        Tb = "pn_kolej"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("PN", "Institut", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PENYELENGGARAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
        Cari("")
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Dim SQL As String

        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        If Session("PN_Pinda") Then
            SQL = "update pn_kolej set dc_kolej = '" & Replace(Tx_Kolej.Text.Trim, "'", "''") & "', jenis=" & Cb_Jenis.SelectedIndex & "  where id_kolej = '" & Tx_Id.Text & "'"
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Tx_Id.Text = ""
            Tx_Kolej.Text = ""
            Cari("")
            Reset()
            Msg(Me, "Rekod Telah Disimpan...")
            Exit Sub
        End If

        'If Tx_Id.Text = "" Then Msg(Me, "Sila isi Kod Negara!") : Exit Sub
        If Tx_Kolej.Text = "" Then Msg(Me, "Sila isi Negara!") : Exit Sub
        If Cb_Jenis.SelectedIndex < 1 Then Msg(Me, "Sila pilih Jenis!") : Exit Sub

        Cmd.CommandText = "select * from pn_kolej where dc_kolej = '" & Replace(Tx_Kolej.Text.Trim, "'", "''") & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Telah Ada!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()

        SQL = "insert pn_kolej (dc_kolej, jenis) select '" & Replace(Tx_Kolej.Text.Trim, "'", "''") & "', " & Cb_Jenis.SelectedIndex
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Tx_Id.Text = ""
            Tx_Kolej.Text = ""
            Msg(Me, "Rekod Telah Disimpan...")
            Cari("")
        Catch ex As Exception
            Msg(Me, "Ralat!")
        End Try
        Reset()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(3).Visible = False
        e.Row.Cells(0).Wrap = False
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        If Session("PN_Padam") Then
            Session("PN_Padam") = False
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
            Dim SQL As String
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            'Comment Original 07092023 -OSH 
            'SQL = "delete from pn_kolej where id_kolej='" & Gd.SelectedRow.Cells(3).Text & "' and dc_kolej='" & Gd.SelectedRow.Cells(2).Text & "'"
            'Disable Collage From The List 07092023 - OSH 
            SQL = "update pn_kolej set status = '0', date_inactive = getdate() where id_kolej='" & Gd.SelectedRow.Cells(3).Text & "' "
            Try
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Tx_Id.Text = ""
                Msg(Me, "Rekod Telah Dipadam...")
                Cari("")
                Tx_Id.Text = ""
                Tx_Kolej.Text = ""
            Catch ex As Exception
                Msg(Me, "Error...")
            End Try
            Cn.Close()
            Exit Sub
        End If
        Reset()
        Session("PN_Pinda") = True
        cmd_baru.Visible = True
        Tx_Id.Text = Gd.SelectedRow.Cells(3).Text
        Tx_Kolej.Text = Gd.SelectedRow.Cells(2).Text
        Select Case Gd.SelectedRow.Cells(4).Text
            Case "K"
                Cb_Jenis.SelectedIndex = 1
            Case "S"
                Cb_Jenis.SelectedIndex = 2
            Case "LN"
                Cb_Jenis.SelectedIndex = 3
            Case Else
                Cb_Jenis.SelectedIndex = 0
        End Select
    End Sub

    Protected Sub Cb_Padam_Click(ByVal sender As Object, ByVal e As EventArgs)
        Session("PN_Padam") = True
    End Sub

    Protected Sub cmd_baru_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_baru.Click
        Reset()
        Tx_Id.Focus()
    End Sub
End Class