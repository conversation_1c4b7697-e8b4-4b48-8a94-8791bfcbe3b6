<%@ Page Language="vb" MaintainScrollPositionOnPostback="true" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P2_Penuh.aspx.vb" Inherits="SPMJ.WebForm17" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .std_hd
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             color: #ffffff;
             font-variant :small-caps ;
             background-color: #6699cc;
        }
        .std_hd2
        {
             font-family: Arial;
             font-size: 9pt; 
             font-weight :bold ;
             font-variant :small-caps ;             
             color: #ffffff;
        }
        .std
        {
             font-family: Arial;
             font-size: 8pt; 
             color: #000000;
        }
        .style9
        {
            height: 23px;
        }
        .style12
        {
            height: 21px;
        }
        .style33
    {
            height: 20px;
        }
        p.<PERSON><PERSON><PERSON><PERSON>
	{margin-bottom:.0001pt;
	font-size:12.0pt;
	font-family:"Times New Roman";
	        margin-left: 0in;
            margin-right: 0in;
            margin-top: 0in;
        }
        .style62
        {
            height: 24px;
            width: 283px;
        }
        .style79
        {
            height: 17px;
            width: 283px;
        }
        .style80
        {
            height: 17px;
        }
        .style81
        {
            width: 650px;
            height: 436px;
            left: 0px;
            top: 70px;
            position: static;
        }
        .style82
        {
            height: 23px;
            color: #CC0000;
        }
        .style83
        {
            height: 20px;
            width: 283px;
        }
        .style87
        {
            width: 283px;
            height: 21px;
        }
        .style90
        {
            height: 19px;
            margin-left: 40px;
            }
        .style92
        {
            height: 22px;
            width: 283px;
        }
        .style96
        {
        }
        .style102
        {
            height: 20px;
            width: 74px;
        }
        .style103
        {
            height: 17px;
            }
        .style106
    {
        height: 19px;
        width: 74px;
    }
    .style108
    {
        height: 19px;
        width: 283px;
    }
    .style110
    {
        height: 24px;
        width: 74px;
    }
        .style111
    {
        height: 24px;
        }
    .style112
    {
        height: 10px;
        margin-left: 40px;
        }
    .style113
    {
        height: 10px;
        width: 74px;
    }
    .style115
    {
        height: 10px;
        width: 283px;
    }
        .style116
        {
            height: 23px;
            width: 74px;
        }
        .style117
        {
            height: 21px;
            width: 74px;
        }
        .style118
        {
            width: 74px;
        }
        .style119
        {
            height: 17px;
            width: 74px;
        }
        .style121
        {
            width: 283px;
            height: 23px;
        }
        .style127
        {
            width: 283px;
            height: 103px;
        }
        .style128
        {
            height: 103px;
        }
        .style129
        {
            width: 74px;
            height: 103px;
        }
        .style136
        {
            font-weight: bold;
            }
        .style137
        {
            height: 7px;
        }
        .style138
        {
            font-weight: normal;
        }
        .style139
        {
            font-weight: bold;
        }
        .style140
        {
            text-decoration: underline;
        }
        .style141
        {
            color: #CC0000;
            font-weight: bold;
        }
        .style142
        {
            width: 283px;
        }
        .style143
        {
            height: 17px;
            width: 65px;
        }
        .style144
        {
            height: 24px;
            width: 65px;
        }
        .style145
        {
            height: 10px;
            width: 65px;
        }
        .style146
        {
            height: 19px;
            width: 65px;
        }
        .style147
        {
            height: 20px;
            width: 65px;
        }
        .style148
        {
            width: 65px;
        }
        .style149
        {
            width: 65px;
            height: 103px;
        }
        .style150
        {
            height: 23px;
            width: 65px;
        }
        .style151
        {
            height: 9px;
        }
        .style152
        {
            height: 9px;
            width: 65px;
        }
        .style153
        {
            height: 9px;
            width: 283px;
        }
        .style154
        {
            height: 9px;
            width: 74px;
        }
        </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:Localize ID="Localize1" runat="server"></asp:Localize>
    <div style="width:100%; background-image: url('Image/Bg_Sgt.gif'); background-attachment: fixed;">
    <table id="Table1" width="100%" style="font-variant: small-caps">
    <br>
    <tr><td>
    
    <table id="Table2" align="center" border="0" cellpadding="-1" cellspacing="0" 
        
            style="border: 1px solid black; margin-left: 0px; font-family: Arial; font-size: 8pt; text-transform: none; line-height: 21px; width: 75%;" 
            bgcolor="White" class="style81">
        <tr>
            <td align="center" bgcolor="#62823F" 
                valign="top" colspan="4" 
                
                
                style="border-bottom-style: solid; border-bottom-width: 1px; border-bottom-color: #000000; vertical-align: middle; text-align: center; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bolder;" >
                pinda/semak rekod jururawat</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
                &nbsp;</td>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style83">
                &nbsp;</td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style33">
                            <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                            </asp:ScriptManagerProxy>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" 
                class="style102">
                &nbsp;</td>
        </tr>
        <tr style="line-height: 21px; background-color: #999966; vertical-align: middle;" 
            bgcolor="#999966">
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style144">
                </td>
								<TD vAlign="middle" align="left" bgColor="#759A4B" 
                class="style62" 
                
                
                
                
                
                style="border-color: #A0BF7D; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold; ">&nbsp; No. Pendaftaran</TD>
 
            <td align="left" bgcolor="#759A4B" valign="middle" class="style111" 
                style="border-color: #A0BF7D">
                <asp:Label ID="Tx_NoPd" runat="server" Font-Bold="True" Font-Names="Arial" 
                    Font-Size="14pt" ForeColor="White" Text="JB-01234" 
                    CssClass="menu_small"></asp:Label>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style110">
                </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style144">
                </td>
								<TD vAlign="middle" align="left" bgColor="#759A4B" 
                class="style62" 
                
                
                
                style="font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;">&nbsp; Tarikh Pendaftaran</TD>
 
            <td align="left" bgcolor="#759A4B" valign="middle" class="style111">
                <asp:Label ID="Tx_Tkh_Daftar" runat="server" Font-Bold="True" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="White" Text="JB-01234" 
                    CssClass="menu_small"></asp:Label>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style110">
                </td>
        </tr>
        <tr style="line-height: 10px">
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style145">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style115">
                </td>
                                     <td bgcolor="White" class="style112" 
                style="line-height: 5px">
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style113">
            </td>
        </tr>
        <tr style="line-height: 21px">
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style146"><span class="style82">*</span>
            </td><td valign="top" align="left" bgcolor="white" 
                class="style108">&nbsp; NO. KP/TENTERA/PASPORT</td><td bgcolor="White" class="style90">
                                         <asp:UpdatePanel ID="UpdatePanel5" runat="server">
                                             <ContentTemplate>
                                                 <asp:TextBox ID="Tx_NoKP" runat="server" CssClass="std" Width="190px" 
                                                     Wrap="False" ReadOnly="True"></asp:TextBox>
                                             </ContentTemplate>
                                         </asp:UpdatePanel>
                                     </td><td align="left" bgcolor="#ffffff" valign="top" class="style106">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style143"><span class="style82">*</span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style92">&nbsp;
									NAMA</td>
            <td bgcolor="White">
                <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="282px" 
                    Wrap="False"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style116">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style143"><span class="style82">*</span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style92">&nbsp;
									WARGANEGARA</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:DropDownList ID="Cb_Warga" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="11" Width="282px" CssClass="std">
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp; TARIKH LAHIR</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
            <asp:UpdatePanel ID="UpdatePanel8" runat="server">
            <ContentTemplate>
                            <asp:TextBox ID="Tx_Tkh_Lahir" runat="server" CssClass="std" 
                    Width="95px" AutoPostBack="True"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_Tkh_Lahir_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Lahir" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_Tkh_Lahir_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_Tkh_Lahir" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                                  </ContentTemplate>    
        </asp:UpdatePanel>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp; TEMPAT LAHIR</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:TextBox ID="Tx_Tpt_Lahir" runat="server" CssClass="std" Width="282px" 
                    Wrap="False"></asp:TextBox>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style143"><span class="style82">*</span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp;
									JANTINA</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:DropDownList ID="Cb_Jantina" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="11" Width="190px" CssClass="std">
                    <asp:ListItem Value=""></asp:ListItem>
                    <asp:ListItem Value="1">LELAKI</asp:ListItem>
                    <asp:ListItem Value="2">PEREMPUAN</asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style143"><span class="style82">*</span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp;
									BANGSA</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:UpdatePanel ID="UpdatePanel4" runat="server">
                    <ContentTemplate>
                        <asp:DropDownList ID="Cb_Bangsa" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="12" Width="190px" AutoPostBack="True">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style92">&nbsp; ETNIK&nbsp;</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:UpdatePanel ID="UpdatePanel3" runat="server">
                    <ContentTemplate>
                        <asp:DropDownList ID="Cb_Etnik" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="12" Width="190px" Enabled="False">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style92">&nbsp;
									AGAMA</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:DropDownList ID="Cb_Agama" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="13" Width="190px">
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style92">&nbsp; UMUR&nbsp;</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:UpdatePanel ID="UpdatePanel7" runat="server">
            <ContentTemplate>
                <asp:TextBox ID="Tx_Umur" runat="server" CssClass="std" Width="95px" 
                    Wrap="False" Enabled="False"></asp:TextBox>
                     </ContentTemplate>    
        </asp:UpdatePanel>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp;
									TARAF PERKAHWINAN</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:DropDownList ID="Cb_Kahwin" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="22" Width="190px">
                    <asp:ListItem> </asp:ListItem>
                    <asp:ListItem Value="1">BUJANG</asp:ListItem>
                    <asp:ListItem Value="2">BERKAHWIN</asp:ListItem>
                    <asp:ListItem Value="3">DUDA</asp:ListItem>
                    <asp:ListItem Value="4">JANDA</asp:ListItem>
                    <asp:ListItem Value="5">BALU</asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style143"><span class="style82">*</span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp; ALAMAT TETAP</TD>
            <td align="left" bgcolor="white" valign="top" 
                id="Tx_TP_Alamat" class="style33">
                <asp:TextBox ID="Tx_TP_Alamat" runat="server" CssClass="std" Height="60px" 
                    TextMode="MultiLine" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style143"><span class="style82">*</span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp;
									POSKOD</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_TP_Poskod" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style143"><span class="style82">*</span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style92">&nbsp;
									BANDAR</TD>
            <td align="left" bgcolor="white" valign="top" class="style9">
                <asp:TextBox ID="Tx_TP_Bandar" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style116">
            </td>
        </tr>
        <tr>
            <td align="right" bgcolor="#ffffff" 
                valign="top" class="style143"><span class="style82">*</span>
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style87">&nbsp;
									NEGERI/NEGARA</TD>
            <td align="left" bgcolor="white" valign="top" class="style12">
                <asp:DropDownList ID="Cb_TP_Negeri" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="220px">
                    <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style117">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp; ALAMAT SURAT-MENYURAT</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_SM_Alamat" runat="server" CssClass="std" Height="60px" 
                    TextMode="MultiLine" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp;
									POSKOD</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_SM_Poskod" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp;
									BANDAR</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_SM_Bandar" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp;
									NEGERI</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:DropDownList ID="Cb_SM_Negeri" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="220px">
                    <asp:ListItem></asp:ListItem>
                </asp:DropDownList>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp; NO. TELEFON RUMAH</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_Tel_R" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
            </td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
                &nbsp;</td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp; NO. TELEFON BIMBIT</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_Tel_HP" runat="server" CssClass="std" Width="282px"></asp:TextBox>
                                     </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
                &nbsp;</td>
        </tr>
        <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" class="style143">
            </td>
								<TD vAlign="top" align="left" bgColor="white" 
                class="style83">&nbsp;
									E-MEL</TD>
            <td align="left" bgcolor="white" valign="top" class="style33">
                <asp:TextBox ID="Tx_Emel" runat="server" CssClass="std" Width="282px"></asp:TextBox>
            </td>
            <td align="left" bgcolor="#ffffff" valign="top" class="style102">
            </td>
        </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
								<TD vAlign="top" bgColor="#ffffff" class="style142">&nbsp;</TD>
                        <td bgcolor="#ffffff" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
								<TD vAlign="top" bgColor="#ffffff" class="style139" colspan="2" 
                            style="color: #990000"><span class="style138">&nbsp; </span><span class="style140">MAKLUMAT LATIHAN</span></TD>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr style="line-height: 10px">
                        <td bgcolor="#ffffff" class="style103" colspan="4" style="height: 7px">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td align="right" valign="top" bgcolor="#ffffff" class="style143"><span class="style82">*</span>
                        </td>
								<TD vAlign="top" bgColor="#ffffff" class="style142">&nbsp;
									KOLEJ/INSTITUSI</TD>
                        <td bgcolor="#ffffff" valign="top">
                                                    <asp:UpdatePanel ID="UpdatePanel20" runat="server">
                                                        <ContentTemplate>
                                                            <asp:RadioButtonList ID="RadioButtonList1" runat="server" AutoPostBack="True" 
                                                                CellPadding="0" CellSpacing="0" Height="16px" RepeatDirection="Horizontal" 
                                                                Width="276px">
                                                                <asp:ListItem Selected="True" Value="1">KERAJAAN</asp:ListItem>
                                                                <asp:ListItem Value="2">SWASTA</asp:ListItem>
                                                                <asp:ListItem Value="3">LUAR NEGARA</asp:ListItem>
                                                            </asp:RadioButtonList>
                                                            masukkan kata kunci dan klik butang &#39;cari&#39;
                                                            <asp:TextBox ID="tx_Cari" runat="server" CssClass="std" Width="150px"></asp:TextBox>
                                    <asp:Button ID="bt_Cari" runat="server" Font-Names="Arial" Font-Size="8pt" 
                                        Height="20px" tabIndex="3" Text="CARI" Width="50px" />
                                        <br />
                                                            <asp:DropDownList ID="Cb_Kolej" runat="server" Font-Names="Arial" 
                                                                Font-Size="8pt" Height="19px" tabIndex="33" Width="529px" 
                                                                AutoPostBack="True">
                                                            </asp:DropDownList>
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                        </td>
                        <td bgcolor="#ffffff" class="style118" style="color: #597739">
                        </td>
                    </tr>
                    <tr>
                        <td align="right" bgcolor="#ffffff" class="style143"><span class="style82">*</span>
                        </td>
								<TD vAlign="top" bgColor="#ffffff" class="style142">&nbsp;
									TAJAAN</TD>
                        <td bgcolor="#ffffff" valign="top">
                            <asp:DropDownList ID="Cb_Tajaan" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="193px">
                            </asp:DropDownList>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                        </td>
                    </tr>
                    <tr style="line-height: 21px">
                        <td align="right" bgcolor="#ffffff" class="style147"><span class="style82">*</span>
                        </td>
								<td valign="top" bgcolor="#ffffff" class="style83">&nbsp;
									SESI PENGAMBILAN</td><td bgcolor="#ffffff" 
                            style="FONT-SIZE: 8pt; FONT-FAMILY: arial; FONT-VARIANT: small-caps; white-space: normal;" 
                            valign="top">
                            <asp:DropDownList ID="Cb_Sesi_Bulan" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="100px">
                            </asp:DropDownList><asp:DropDownList ID="Cb_Sesi_Tahun" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="93px">
                            </asp:DropDownList></td><td bgcolor="#ffffff" class="style102"></td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                        </td>
								<TD vAlign="top" align="left" bgColor="#ffffff" class="style79">&nbsp;
									TARIKH MULA LATIHAN</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style80">
                            <asp:TextBox ID="Tx_M_Latihan" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_M_Latihan_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_M_Latihan" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_M_Latihan_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_M_Latihan" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                        <td bgcolor="#ffffff" class="style119">
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                        </td>
								<TD vAlign="top" align="left" bgColor="#ffffff" class="style83">&nbsp; TARIKH TAMAT 
                                    LATIHAN</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                            <asp:TextBox ID="Tx_T_Latihan" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_T_Latihan_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_T_Latihan" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_T_Latihan_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_T_Latihan" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                        <td bgcolor="#ffffff" class="style102">
                        </td>
                    </tr>
                    <tr>
                        <td align="right" bgcolor="#ffffff" class="style148"><span class="style82">*</span>
                        </td>
								<TD vAlign="top" align="left" bgColor="#ffffff" class="style142">&nbsp; TARIKH 
                                    PEPERIKSAAN LEMBAGA</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top">
                            <asp:TextBox ID="Tx_Periksa" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_Periksa_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Periksa" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_Periksa_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_Periksa" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                        <td bgcolor="#ffffff" class="style118">
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;
                        </td>
                        <td bgcolor="#ffffff" class="style142">
                        </td>
                        <td bgcolor="#ffffff">
                            <br />
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                        </td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style148">
                        </td>
                        <td bgcolor="#ffffff" class="style136" colspan="2" style="color: #990000">
                            &nbsp; <span class="style140">MAKLUMAT KELAYAKAN</span></td>
                        <td bgcolor="#ffffff" class="style118">
                        </td>
                    </tr>
                    <tr>
            <td align="left" bgcolor="#ffffff" 
                valign="top" style="line-height: 10px" colspan="4">
            </td>
								</tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142" valign="top">
                        &nbsp; KELAYAKAN IKHTISAS</td>
                        <td bgcolor="#ffffff" align="left">
                            <table align="left"><tr>
                                <td align="left" width="100%"><asp:UpdatePanel ID="UpdatePanel1" runat="server">
                                        <ContentTemplate>
                                            <asp:GridView ID="Gd" 
                            runat="server" CellPadding="0" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" GridLines="Horizontal" HorizontalAlign="Left" 
                    Width="100%" BorderColor="#5D7B9D" 
    AutoGenerateColumns="False" Height="106px" BorderStyle="Solid" BorderWidth="1px">
                                                <FooterStyle BackColor="#A6B7CA" Font-Bold="True" ForeColor="White" />
                                                <RowStyle BackColor="White" VerticalAlign="Middle" />
                                                <Columns>
                                                    <asp:TemplateField HeaderText="#">
                                                        <HeaderStyle HorizontalAlign="Center" />
                                                        <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="KELAYAKAN">
                                                        <ItemTemplate>
                                                            <asp:TextBox ID="Tx_Layak" runat="server" CssClass="std" Width="200px"></asp:TextBox>
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="TARIKH KELAYAKAN">
                                                        <ItemTemplate>
                                                            <asp:TextBox ID="Tx_Tkh_Layak" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                            <cc1:MaskedEditExtender ID="Tx_Tkh_Layak_MaskedEditExtender" runat="server" 
                                    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                    CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                    CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                    Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Layak" 
                                    UserDateFormat="DayMonthYear">
                                                            </cc1:MaskedEditExtender>
                                                            <cc1:CalendarExtender ID="Tx_Tkh_Layak_CalendarExtender" runat="server" 
                                    Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                    TargetControlID="Tx_Tkh_Layak">
                                                            </cc1:CalendarExtender>
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField ShowHeader="False">
                                                        <ItemTemplate>
                                                            <asp:Button ID="Button1" runat="server" CausesValidation="False" 
                                    CommandName="Select" Font-Names="Arial" Font-Size="8pt" Height="20px" 
                                    Text="+" Width="21px" Font-Bold="True" onclick="Button1_Click" />
                                                        </ItemTemplate>
                                                        <ControlStyle Font-Names="Arial" Font-Size="8pt" />
                                                        <HeaderStyle Width="65px" />
                                                    </asp:TemplateField>
                                                </Columns>
                                                <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                                                <SelectedRowStyle Font-Bold="False" />
                                                <HeaderStyle BackColor="#5D7B9D" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                                                <AlternatingRowStyle BackColor="White" />
                                            </asp:GridView>
                                            
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </td></tr></table>
                            
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142" valign="top">
                            &nbsp;&nbsp;KELAYAKAN AKADEMIK</td>
                        <td bgcolor="#ffffff">
                            <table align="left"><tr>
                                <td align="left">
                                    <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                                        <ContentTemplate>
                                            <asp:GridView ID="GdA" 
                            runat="server" CellPadding="0" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" GridLines="Horizontal" HorizontalAlign="Left" 
                    Width="100%" BorderColor="#5D7B9D" 
    AutoGenerateColumns="False" Height="106px" BorderStyle="Solid" BorderWidth="1px">
                                                <FooterStyle BackColor="#A6B7CA" Font-Bold="True" ForeColor="White" />
                                                <RowStyle BackColor="White" />
                                                <Columns>
                                                    <asp:TemplateField HeaderText="#">
                                                        <HeaderStyle HorizontalAlign="Center" />
                                                        <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="KELAYAKAN">
                                                        <ItemTemplate>
                                                            <asp:TextBox ID="Tx_Layak" runat="server" CssClass="std" Width="200px"></asp:TextBox>
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="TARIKH KELAYAKAN">
                                                        <ItemTemplate>
                                                            <asp:TextBox ID="Tx_Tkh_Layak" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                                                            <cc1:MaskedEditExtender ID="Tx_Tkh_Layak_MaskedEditExtender" runat="server" 
                                    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                    CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                    CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                    Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_Layak" 
                                    UserDateFormat="DayMonthYear">
                                                            </cc1:MaskedEditExtender>
                                                            <cc1:CalendarExtender ID="Tx_Tkh_Layak_CalendarExtender" runat="server" 
                                    Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                    TargetControlID="Tx_Tkh_Layak">
                                                            </cc1:CalendarExtender>
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField ShowHeader="False">
                                                        <ItemTemplate>
                                                            <asp:Button ID="Button2" runat="server" CausesValidation="False" 
                                    CommandName="Select" Font-Names="Arial" Font-Size="8pt" Height="20px" 
                                    Text="+" Width="21px" Font-Bold="True" onclick="Button1_Click" />
                                                        </ItemTemplate>
                                                        <ControlStyle Font-Names="Arial" Font-Size="8pt" />
                                                        <HeaderStyle Width="65px" />
                                                    </asp:TemplateField>
                                                </Columns>
                                                <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                                                <SelectedRowStyle Font-Bold="False" />
                                                <HeaderStyle BackColor="#5D7B9D" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                                                <AlternatingRowStyle BackColor="White" />
                                            </asp:GridView>
                                        </ContentTemplate>
                                    </asp:UpdatePanel>
                                </td></tr></table>
                            
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style136" colspan="2" style="color: #990000">
                            &nbsp; <span class="style140">MAKLUMAT APC &amp; PERKHIDMATAN</span></td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr style="line-height: 10px">
                        <td bgcolor="#ffffff" class="style137" colspan="4" style="height: 7px">
                            </td>
                    </tr>
                     <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style96" colspan="2">
                                            <asp:GridView ID="Gd_APC" 
                            runat="server" CellPadding="0" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" GridLines="Horizontal" HorizontalAlign="Left" 
                    Width="100%" BorderColor="#5D7B9D" BorderStyle="Solid" 
                                BorderWidth="1px">
                                                <FooterStyle BackColor="#A6B7CA" Font-Bold="True" ForeColor="White" />
                                                <RowStyle BackColor="White" VerticalAlign="Middle" />
                                                <Columns>
                                                    <asp:TemplateField ShowHeader="False">
                                                        <ItemTemplate>
                                                            <asp:Button ID="Button1" runat="server" CausesValidation="False" 
                                                                CommandName="Select" Font-Names="Arial" Font-Size="8pt" Height="19px" 
                                                                Text="SEMAK" Width="60px" />
                                                        </ItemTemplate>
                                                    </asp:TemplateField>
                                                    <asp:TemplateField HeaderText="#">
                                                        <HeaderStyle HorizontalAlign="Center" />
                                                        <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                    </asp:TemplateField>
                                                </Columns>
                                                <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                                                <SelectedRowStyle Font-Bold="False" ForeColor="#CC0000" />
                                                <HeaderStyle BackColor="#5D7B9D" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                                                <AlternatingRowStyle BackColor="White" />
                                            </asp:GridView>
                                            
                                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>                   <tr>
                        <td bgcolor="#ffffff" class="style152">
                            </td>
                        <td bgcolor="#ffffff" class="style153">
                            </td>
                        <td bgcolor="#ffffff" class="style151">
                            </td>
                        <td bgcolor="#ffffff" class="style154">
                            </td>
                    </tr>
                                 <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; NO. APC&nbsp;</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="Tx_APC" runat="server" CssClass="std" Width="282px" ReadOnly="True"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                                 <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; TEMPAT AMALAN&nbsp;</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="Tx_Tpt_Amalan" runat="server" CssClass="std" Width="282px" ReadOnly="True"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142" valign="top">
                            &nbsp; ALAMAT AMALAN&nbsp;</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="Tx_Amalan_Alamat" runat="server" CssClass="std" Height="60px" 
                    TextMode="MultiLine" Width="282px" ReadOnly="True"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; POSKOD&nbsp;</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="Tx_Amalan_Poskod" runat="server" CssClass="std" Width="282px" 
                                ReadOnly="True"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; BANDAR&nbsp;</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="Tx_Amalan_Bandar" runat="server" CssClass="std" Width="282px" 
                                ReadOnly="True"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; NEGERI&nbsp;</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="Tx_Amalan_Negeri" runat="server" CssClass="std" Width="282px" 
                                ReadOnly="True"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; NO. TELEFON&nbsp;</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="Tx_Amalan_Tel" runat="server" CssClass="std" Width="282px" ReadOnly="True"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; NO. FAKS&nbsp;</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="Tx_Amalan_Fax" runat="server" CssClass="std" Width="282px" ReadOnly="True"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; SEKTOR</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="Tx_Amalan_Sektor" runat="server" CssClass="std" Width="282px" 
                                ReadOnly="True"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; <span class="style141">KEBIDANAN I&nbsp;</span></td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; NO. PD KEBIDANAN I&nbsp;</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="Tx_NoPd_KBI" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                                                <tr>
                        <td bgcolor="#ffffff" class="style143">
                        </td>
								<TD vAlign="top" align="left" bgColor="#ffffff" class="style79">&nbsp; TARIKH DAFTAR 
                                    KBDN. I</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style80">
                            <asp:TextBox ID="Tx_Daftar_B1" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_Daftar_B1_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Daftar_B1" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_Daftar_B1_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_Daftar_B1" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                        <td bgcolor="#ffffff" class="style119">
                        </td>
                                                </tr>
                                                <tr>
                        <td bgcolor="#ffffff" class="style143">
                        </td>
								<TD vAlign="top" bgColor="#ffffff" class="style142">&nbsp;
									KOLEJ/INSTITUSI</TD>
                        <td bgcolor="#ffffff" valign="top">
                            <asp:DropDownList ID="Cb_Kolej_B1" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="529px">
                            </asp:DropDownList>
                        </td>
                        <td bgcolor="#ffffff" class="style118" style="color: #597739">
                        </td>
                                                </tr>
                                                <tr>
                        <td bgcolor="#ffffff" class="style143">
                        </td>
								<TD vAlign="top" align="left" bgColor="#ffffff" class="style79">&nbsp;
									TARIKH MULA LATIHAN</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style80">
                            <asp:TextBox ID="Tx_M_Latihan_B1" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_M_Latihan_B1_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_M_Latihan_B1" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_M_Latihan_B1_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_M_Latihan_B1" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                        <td bgcolor="#ffffff" class="style119">
                        </td>
                                                </tr>
                                                <tr>
                        <td bgcolor="#ffffff" class="style143">
                        </td>
								<TD vAlign="top" align="left" bgColor="#ffffff" class="style83">&nbsp; TARIKH TAMAT 
                                    LATIHAN</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top" class="style33">
                            <asp:TextBox ID="Tx_T_Latihan_B1" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_T_Latihan_B1_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_T_Latihan_B1" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_T_Latihan_B1_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_T_Latihan_B1" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                        <td bgcolor="#ffffff" class="style102">
                        </td>
                                                </tr>
                                                <tr>
                        <td bgcolor="#ffffff" class="style148">
                        </td>
								<TD vAlign="top" align="left" bgColor="#ffffff" class="style142">&nbsp; TARIKH 
                                    PEPERIKSAAN LEMBAGA</TD>
                        <td bgcolor="#ffffff" 
                            style="FONT-WEIGHT: normal; FONT-SIZE: 8pt; COLOR: black; FONT-FAMILY: arial; FONT-VARIANT: small-caps" 
                            valign="top">
                            <asp:TextBox ID="Tx_Periksa_B1" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_Periksa_B1_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Periksa_B1" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_Periksa_B1_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_Periksa_B1" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                        <td bgcolor="#ffffff" class="style118">
                        </td>
                                                </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; NO. PD KESIHATAN UMUM&nbsp;</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="Tx_NoPd_KU" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; NO. PD KESIHATAN JIWA&nbsp;</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="Tx_NoPd_KJ" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; NO. PD Jururawat masy.</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="tx_noJM" runat="server" CssClass="std" Width="95px" Enabled="False"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; NO. PD Pen. Jururawat&nbsp;</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="tx_noPJ" runat="server" CssClass="std" Width="95px" Enabled="False"></asp:TextBox>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; TARIKH MULA BERKHIDMAT&nbsp;</td>
                        <td bgcolor="#ffffff">
                            <asp:TextBox ID="Tx_Mula_Khidmat" runat="server" CssClass="std" Width="95px" 
                                ReadOnly="True"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_Mula_Khidmat_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Mula_Khidmat" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_Mula_Khidmat_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_Mula_Khidmat" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; TARIKH SAH JAWATAN&nbsp;</td>
                        <td bgcolor="#ffffff">
                            <asp:TextBox ID="Tx_Sah_Jawatan" runat="server" CssClass="std" Width="95px" 
                                ReadOnly="True"></asp:TextBox>
                            <cc1:MaskedEditExtender ID="Tx_Sah_Jawatan_MaskedEditExtender" runat="server" 
                                CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                CultureThousandsPlaceholder="" CultureTimePlaceholder="" Enabled="True" 
                                Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Sah_Jawatan" 
                                CultureName="en-GB" UserDateFormat="DayMonthYear">
                            </cc1:MaskedEditExtender>
                            <cc1:CalendarExtender ID="Tx_Sah_Jawatan_CalendarExtender" runat="server" 
                                Enabled="True" PopupPosition="Right" TargetControlID="Tx_Sah_Jawatan" 
                                Format="dd/MM/yyyy">
                            </cc1:CalendarExtender>
                            </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td align="right" bgcolor="#ffffff" class="style143"><span class="style82">*</span></td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; MARKAH CPD&nbsp;</td>
                        <td bgcolor="#ffffff">
                <asp:TextBox ID="Tx_CPD" runat="server" CssClass="std" Width="95px"></asp:TextBox>
                            <asp:Label ID="lbMyCPD_Status" runat="server"></asp:Label>
                            </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; GELARAN JAWATAN&nbsp;
                                        </td>
                        <td bgcolor="#ffffff">
                            <asp:DropDownList ID="Cb_Gelaran" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="193px">
                                <asp:ListItem Value="0">-</asp:ListItem>
                                <asp:ListItem Value="1">JB</asp:ListItem>
                                <asp:ListItem Value="5">JM</asp:ListItem>
                                <asp:ListItem Value="3">MATRON</asp:ListItem>
                                <asp:ListItem Value="4">PENGAJAR</asp:ListItem>
                                <asp:ListItem Value="6">PJ</asp:ListItem>
                                <asp:ListItem Value="2">SISTER</asp:ListItem>
                                <asp:ListItem Value="7">JK MENTAL</asp:ListItem>
                            </asp:DropDownList>
                            </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp; GRED JAWATAN</td>
                        <td bgcolor="#ffffff">
                            <asp:DropDownList ID="Cb_Gred" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" Height="19px" tabIndex="33" Width="193px">
                            </asp:DropDownList>
                        </td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
        <tr style="line-height: 10px">
                        <td bgcolor="#ffffff" class="style137" colspan="4" style="height: 7px">
                            </td>
                    </tr>

                    <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142">
                            &nbsp;</td>
                        <td bgcolor="#ffffff">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
        <tr valign="bottom">
                        <td align="right" bgcolor="#ffffff" class="style144"><span class="style82">*</span>
                            </td>
                        <td bgcolor="#999966" class="style62" 
                                         
                            style="font-weight: bold; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-variant: small-caps;" 
                            valign="bottom">
                            &nbsp; Status&nbsp;</td>
                        <td bgcolor="#999966" class="style111">
                            <asp:UpdatePanel ID="UpdatePanel6" runat="server">
                                <ContentTemplate>
                                    <asp:DropDownList ID="Cb_Status" runat="server" Font-Names="Arial" 
                    Font-Size="8pt" Height="19px" tabIndex="18" Width="200px" AutoPostBack="True">
                                        <asp:ListItem></asp:ListItem>
                                        <asp:ListItem Value="1">LENGKAP</asp:ListItem>
                                        <asp:ListItem Value="2">TIDAK LENGKAP</asp:ListItem>
                                        <asp:ListItem Value="3">MENINGGAL DUNIA</asp:ListItem>
                                        <asp:ListItem Value="4">TATATERTIB</asp:ListItem>
                                        <asp:ListItem Value="5">BERSARA WAJIB</asp:ListItem>
                                        <asp:ListItem Value="6">DISENARAI HITAM</asp:ListItem>
                                    </asp:DropDownList>
                                </ContentTemplate>
                            </asp:UpdatePanel>
            </td>
                        <td bgcolor="#ffffff" class="style110">
                            </td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style149">
                            </td>
                        <td bgcolor="#999966" class="style127" valign="top" 
                            
                            style="font-weight: bold; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-variant: small-caps;">
                            &nbsp; Catatan</td>
                        <td bgcolor="#999966" valign="top" class="style128">
                <asp:TextBox ID="Tx_Catatan" runat="server" CssClass="std" Height="89px" 
                    TextMode="MultiLine" Width="325px"></asp:TextBox>
            </td>
                        <td bgcolor="#ffffff" class="style129">
                            </td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style143">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style142" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" valign="top">
                            &nbsp;</td>
                        <td bgcolor="#ffffff" class="style118">
                            &nbsp;</td>
                    </tr>
        <tr>
                        <td bgcolor="#ffffff" class="style150">
                            </td>
                        <td bgcolor="#ffffff" class="style121" valign="top">
                            </td>
                        <td bgcolor="#ffffff" class="style9" valign="top">
                <asp:Button ID="cmdHantar" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SIMPAN" Width="96px" />
            &nbsp;<asp:Button ID="cmdHantar0" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SIMPAN &amp; PILIH" Width="96px" />
            </td>
                        <td bgcolor="#ffffff" class="style116">
                            </td>
                    </tr>
        <tr>
            <td bgcolor="#ffffff" class="style143">
                &nbsp;</td>
            <td bgcolor="#ffffff" align="center" colspan="2" valign="middle">
                &nbsp;</td>
            <td bgcolor="#ffffff" class="style118">
                &nbsp;</td>
        </tr>
    </table></td></tr>
    <tr><td>
    
        &nbsp;</td></tr></table>
    
    </br></br>
    </div>

</asp:Content>