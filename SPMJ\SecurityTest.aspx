<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="SecurityTest.aspx.vb" Inherits="SPMJ.SecurityTest" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Security Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #e7f3ff; border: 1px solid #b3d9ff; color: #0c5460; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>SPMJ Security Implementation Test</h1>
        
        <div class="test-result info">
            <h3>Security Functions Test</h3>
            <p>This page tests the security functions implemented in the SPMJ application.</p>
        </div>
        
        <asp:Button ID="btnTestSecurity" runat="server" Text="Run Security Tests" />
        
        <div style="margin-top: 20px;">
            <asp:Label ID="lblResults" runat="server" />
        </div>
        
        <div style="margin-top: 20px;">
            <h3>Test Results:</h3>
            <asp:TextBox ID="txtResults" runat="server" TextMode="MultiLine" Rows="20" Width="100%" 
                ReadOnly="true" style="font-family: monospace; font-size: 12px;" />
        </div>
    </form>
</body>
</html>
