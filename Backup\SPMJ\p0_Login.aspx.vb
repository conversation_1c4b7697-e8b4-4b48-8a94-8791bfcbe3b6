﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm30
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        Dim x As Menu = Master.FindControl("Menu1")
        x.Visible = False
    End Sub

    Protected Sub cmd_OK_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_OK.Click
        If Tx_Id.Text.Trim = "" Then Tx_Id.Focus() : Exit Sub
        If Tx_Pwd.Text.Trim = "" Then Tx_Pwd.Focus() : Exit Sub

        If Chk_SQL(Tx_Id.Text) = True Then Msg(Me, "Kesalahan pada Kod Pengguna/Kata Laluan!") : Exit Sub
        If Chk_SQL(Tx_Pwd.Text) = True Then Msg(Me, "Kesalahan pada Kod Pengguna/Kata Laluan!") : Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Try
            Cmd.CommandText = "select * from pn_pengguna where id_pg = '" & Tx_Id.Text & "' and pwd = '" & Tx_Pwd.Text & "' and status=1"
            'Cmd.CommandText = "delete from pn_pengguna where id_pg = '" & Tx_Id.Text & "' and pwd = '" & Tx_Pwd.Text & "'"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                Session("Id_PG") = Rdr("Id_PG")
                Session("PWD") = Rdr("PWD")
                Session("MODUL") = Rdr("MODUL")
                Session("AKSES") = Rdr("AKSES")
                Rdr.Close() : Cn.Close()
                'Check access right 23072018 -OSH
                Session("ORIGIN") = "yes"
                Response.Redirect("blank.aspx")
            Else
                Msg(Me, "Kesalahan pada Kod Pengguna/Kata Laluan!")
                Exit Sub
            End If
            Rdr.Close()
            Cn.Close()
        Catch ex As Exception
            Msg(Me, "Kesalahan pada Kod Pengguna/Kata Laluan!!!")
        End Try
    End Sub
End Class