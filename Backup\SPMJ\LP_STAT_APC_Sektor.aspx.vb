﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_APC_Sektor
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        'TAHUN APC
        For i = Now.Year To Now.Year - 20 Step -1
            Cb_Tahun.Items.Add(i)
        Next

        'JAWATAN
        With Cb_Jawatan
            .Items.Clear()
            .Items.Add("JURURAWAT BERDAFTAR")
            .Items.Item(.Items.Count - 1).Value = "1"
            .Items.Add("JURURAWAT MASYARAKAT")
            .Items.Item(.Items.Count - 1).Value = "2"
            .Items.Add("PENOLONG JURURAWAT")
            .Items.Item(.Items.Count - 1).Value = "3"
        End With
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim SQL As String
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        SQL = "delete from temp_ret; "
        SQL += "insert temp_ret "
        'Comment Original 26062020 - OSH 
        'SQL += "select pn.id_negeri as negara, count(*) as b1, null as b2, null as b3, null as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa inner join jt_penuh jp on jpa.nokp=jp.nokp left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and pta.sektor=1 group by pn.id_negeri "
        'SQL += "union select pn.id_negeri as negara, null as b1, count(*) as b2, null as b3, null as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa inner join jt_penuh jp on jpa.nokp=jp.nokp left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and pta.sektor=2 group by pn.id_negeri "
        ''add 20022013
        'SQL += "union select pn.id_negeri as negara, null as b1, null as b2, count(*) as b3, null as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa inner join jt_penuh jp on jpa.nokp=jp.nokp left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and pta.sektor=3 group by pn.id_negeri "
        'SQL += "union select pn.id_negeri as negara, null as b1, null as b2, null as b3, count(*) as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa inner join jt_penuh jp on jpa.nokp=jp.nokp left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 group by pn.id_negeri "

        'Improve query active valid apc exclude cancel apc 25062020 - OSH
        'SQL += "select pn.id_negeri as negara, count(*) as b1, null as b2, null as b3, null as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa inner join jt_penuh jp on jpa.nokp=jp.nokp left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and pta.sektor=1 and apc_batal =0 group by pn.id_negeri "
        'SQL += "union select pn.id_negeri as negara, null as b1, count(*) as b2, null as b3, null as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa inner join jt_penuh jp on jpa.nokp=jp.nokp left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and pta.sektor=2 and apc_batal =0 group by pn.id_negeri "
        'SQL += "union select pn.id_negeri as negara, null as b1, null as b2, count(*) as b3, null as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa inner join jt_penuh jp on jpa.nokp=jp.nokp left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and pta.sektor=3 and apc_batal =0 group by pn.id_negeri "
        'SQL += "union select pn.id_negeri as negara, null as b1, null as b2, null as b3, count(*) as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa inner join jt_penuh jp on jpa.nokp=jp.nokp left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and apc_batal =0 group by pn.id_negeri "

        'Improve query active valid apc exclude cancel apc 25062020 - OSH
        'SQL += "select pn.id_negeri as negara, count(*) as b1, null as b2, null as b3, null as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and pta.sektor=1 and apc_batal =0 group by pn.id_negeri "
        'SQL += "union select pn.id_negeri as negara, null as b1, count(*) as b2, null as b3, null as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and pta.sektor=2 and apc_batal =0 group by pn.id_negeri "
        'SQL += "union select pn.id_negeri as negara, null as b1, null as b2, count(*) as b3, null as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and pta.sektor=3 and apc_batal =0 group by pn.id_negeri "
        'SQL += "union select pn.id_negeri as negara, null as b1, null as b2, null as b3, count(*) as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and apc_batal =0 group by pn.id_negeri "

        'Improve query active valid apc exclude cancel apc and  non application 23032021 - OSH
        SQL += "select pn.id_negeri as negara, count(*) as b1, null as b2, null as b3, null as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and pta.sektor=1 and apc_batal =0 group by pn.id_negeri "
        SQL += "union select pn.id_negeri as negara, null as b1, count(*) as b2, null as b3, null as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and pta.sektor=2 and apc_batal =0 group by pn.id_negeri "
        SQL += "union select pn.id_negeri as negara, null as b1, null as b2, count(*) as b3, null as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and pta.sektor=3 and apc_batal =0 group by pn.id_negeri "
        SQL += "union select pn.id_negeri as negara, null as b1, null as b2, null as b3, count(*) as b4 ,null as b5 ,null as b6 ,null as b7 ,null as b8 ,null as b9,null as b10,null as b11,null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_tpt_amalan pta on pta.id_amalan=jpa.id_amalan left outer join pn_negeri pn on pn.id_negeri=pta.negeri where apc_tahun=" & Cb_Tahun.Text & " and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " and ret=0 and apc_batal =0 and non_ap is null group by pn.id_negeri "

        SQL += "; "

        Cmd.CommandText = SQL
        Cmd.ExecuteNonQuery()
        Cn.Close()

        Jana()
    End Sub

    Public Sub Jana()
        Dim Tajuk, Tajuk2 As String, x, gov, priv, non_moh, xx, total As Integer
        Tajuk = "STATISTIK PENGELUARAN APC BAGI " & Cb_Jawatan.SelectedItem.Text & ", " & Cb_Tahun.Text
        Tajuk2 = "MENGIKUT NEGERI DAN SEKTOR"

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "laporan" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='5' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='5' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>NEGERI</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>KERAJAAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>SWASTA</td>"
        '20022013
        Header += "    <td style='vertical-align: middle; text-align: center;'>BUKAN KKM</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>X</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        Cmd.CommandText = "select dc_negeri, isnull(sum(b1),'0'), isnull(sum(b2),'0'), isnull(sum(b3),'0'), isnull(sum(b4),'0'), isnull(sum(b5),'0'), isnull(sum(b6),'0'), isnull(sum(b7),'0'), " & _
                          "isnull(sum(b8),'0'), isnull(sum(b9),'0'), isnull(sum(b10),'0'), isnull(sum(b11),'0'), isnull(sum(b12),'0'), isnull(sum(x),'0'), isnull(sum(nokp),'0'), isnull(sum(total),'0') " & _
                          "from temp_ret ret left outer join pn_negeri pn on ret.negara=pn.id_negeri group by dc_negeri order by dc_negeri"
        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "temp_ret")
        Rdr.Close()

        Header = ""
        For Each dr As DataRow In Ds.Tables(0).Rows
            Header += "<tr>"
            Header += "    <td style='vertical-align: middle; text-align: left;'>" & dr.Item(0) & "</td> "
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(1) & "</td> " : gov += CInt(dr.Item(1))
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(2) & "</td> " : priv += CInt(dr.Item(2))
            'Add 20022013
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(3) & "</td> " : non_moh += CInt(dr.Item(3))
            x = CInt(dr.Item(4)) - CInt(dr.Item(3)) - CInt(dr.Item(2)) - CInt(dr.Item(1))
            'Comment 20022013
            'x = CInt(dr.Item(3)) - CInt(dr.Item(2)) - CInt(dr.Item(1))
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & x & "</td> " : xx += x
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(4) & "</td> " : total += CInt(dr.Item(4))
            Header += "</tr>"
        Next
        Header += "<tr>"
        Header += "    <td>JUMLAH KESELURUHAN</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & gov & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & priv & "</td> "
        'Add 20022013
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & non_moh & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & xx & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & total & "</td> "
        Header += "</tr>"
        'Add 20022013
        Response.Write(Header)
        Response.Write("</table>")

        Header = ""
        Header = "<table>"
        Header += "<tr></tr>"
        Header += "<tr></tr>"
        Header += "<tr>"
        Header += "    <td colspan='5'>X dan (space): Rekod yang didaftarkan tidak lengkap</td> "
        Header += "</tr>"
        Response.Write(Header)
        Response.Write("</table>")
        Response.End()
        Response.Flush()
    End Sub
End Class