﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm38
    Inherits System.Web.UI.Page

    Public Sub Hide_Panel()
        Panel4.Visible = False
        Panel5.Visible = False
        Panel6.Visible = False
        Panel7.Visible = False
        Panel8.Visible = False
        Panel9.Visible = False
        Panel10.Visible = False
        Panel11.Visible = False
        Panel12.Visible = False
        Panel13.Visible = False
    End Sub

    Public Sub Reset()
        Cb_Jenis.SelectedIndex = 0
        Tx_Tahun.Text = "" : Tx_Id.Text = "" : Tx_Siri.Text = ""
        Tx_Tkh_JB.Text = "" : Tx_Ms_JB.Text = "" : Tx_Mk_JB.Text = ""
        Tx_Tkh_BIT.Text = "" : Tx_Ms_BIT.Text = "" : Tx_Mk_BI.Text = ""
        Tx_Tkh_BIO.Text = "" : Tx_Ms_BIO.Text = ""
        Tx_Tkh_BIV.Text = "" : Tx_Ms_BIV.Text = ""
        Tx_Tkh_PJT1.Text = "" : Tx_Ms_PJT1.Text = "" : Tx_Mk_PJ.Text = ""
        Tx_Tkh_PJT2.Text = "" : Tx_Ms_PJT2.Text = ""
        Tx_Tkh_PJP.Text = "" : Tx_Ms_PJP.Text = ""
        Tx_Tkh_JMT1.Text = "" : Tx_Ms_JMT1.Text = "" : Tx_Mk_JM.Text = ""
        Tx_Tkh_JMT2.Text = "" : Tx_Ms_JMT2.Text = ""
        Tx_Tkh_JMO.Text = "" : Tx_Ms_JMO.Text = ""
        Tx_Tkh_Papar.Text = ""
        Hide_Panel()
    End Sub

    Public Sub Cari()
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select id_xm, j_xm, Tahun, Siri, T1_Tkh, T1_Masa, T2_Tkh, T2_Masa, O_Tkh, O_Masa, V_Tkh, V_Masa, P_Tkh, P_Masa, AG, Markah_Min, Status, ISNULL(convert(char(12), tkh_papar, 103),'') as tkh_papar, Tahun as 'Tahun ', Siri as 'Siri ', case j_xm when 1 then 'Jb' when 2 then 'Jm' when 3 then 'Pj' when 4 then 'Kb1' end as 'Jenis', case status when 1 then 'Buka' else 'Tutup' end as 'Status ', ag as 'Angka Giliran', markah_min as 'Markah Lulus' from pn_xm order by id_xm desc"
        Tb = "pn_tpt_amalan"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("PN", "Exam", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PENYELENGGARAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
        Cari()
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Tx_Tahun.Text = "" Then Tx_Tahun.Focus() : Exit Sub
        If Tx_Siri.Text = "" Then Tx_Siri.Focus() : Exit Sub
        If Cb_Jenis.SelectedIndex < 1 Then Cb_Jenis.Focus() : Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim SQL As String = ""

        'Comment Original 20082018 -OSH 
        'If Session("PN_Pinda") Then
        '    Select Case Cb_Jenis.SelectedIndex
        '        Case 1
        '            SQL = "update pn_xm set "
        '            SQL += "t1_tkh = " & Chk_Tkh(Tx_Tkh_JB.Text) & ", t1_masa = '" & Tx_Ms_JB.Text & "', markah_min =" & Tx_Mk_JB.Text & ", tkh_papar=" & Chk_Tkh(Tx_Tkh_Papar.Text) & " "
        '            SQL += "where j_xm=1 and id_xm=" & Tx_Id.Text & ""
        '        Case 2
        '            SQL = "update pn_xm set "
        '            SQL += "t1_tkh = " & Chk_Tkh(Tx_Tkh_JMT1.Text) & ", t1_masa = '" & Tx_Ms_JMT1.Text & "', markah_min =" & Tx_Mk_JM.Text & ", tkh_papar=" & Chk_Tkh(Tx_Tkh_Papar.Text) & " "
        '            'SQL += "t2_tkh = " & Chk_Tkh(Tx_Tkh_JMT2.Text) & ", t2_masa = '" & Tx_Ms_JMT2.Text & "', "
        '            'SQL += "o_tkh = " & Chk_Tkh(Tx_Tkh_JMO.Text) & ", o_masa = '" & Tx_Ms_JMO.Text & "' "
        '            SQL += "where j_xm=2 and id_xm=" & Tx_Id.Text & ""
        '        Case 3
        '            SQL = "update pn_xm set "
        '            SQL += "t1_tkh = " & Chk_Tkh(Tx_Tkh_PJT1.Text) & ", t1_masa = '" & Tx_Ms_PJT1.Text & "', markah_min =" & Tx_Mk_PJ.Text & ", tkh_papar=" & Chk_Tkh(Tx_Tkh_Papar.Text) & " "
        '            'SQL += "t2_tkh = " & Chk_Tkh(Tx_Tkh_PJT2.Text) & ", t2_masa = '" & Tx_Ms_PJT2.Text & "', "
        '            'SQL += "p_tkh = " & Chk_Tkh(Tx_Tkh_PJP.Text) & ", p_masa = '" & Tx_Ms_PJP.Text & "' "
        '            SQL += "where j_xm=3 and id_xm=" & Tx_Id.Text & ""
        '        Case 4
        '            SQL = "update pn_xm set "
        '            SQL += "t1_tkh = " & Chk_Tkh(Tx_Tkh_BIT.Text) & ", t1_masa = '" & Tx_Ms_BIT.Text & "', markah_min =" & Tx_Mk_BI.Text & ", "
        '            SQL += "o_tkh = " & Chk_Tkh(Tx_Tkh_BIO.Text) & ", o_masa = '" & Tx_Ms_BIO.Text & "', "
        '            SQL += "v_tkh = " & Chk_Tkh(Tx_Tkh_BIV.Text) & ", v_masa = '" & Tx_Ms_BIV.Text & "', tkh_papar=" & Chk_Tkh(Tx_Tkh_Papar.Text) & " "
        '            SQL += "where j_xm=4 and id_xm=" & Tx_Id.Text & ""
        '    End Select
        '    Try
        '        Cmd.CommandText = SQL
        '        Cmd.ExecuteNonQuery()
        '        Cari()
        '        Session("PN_Pinda") = False
        '        Msg(Me, "Rekod Telah Dikemaskini...")
        '        Exit Sub
        '    Catch ex As Exception
        '        Msg(Me, ex.Message)
        '    End Try
        'End If

        'Cmd.CommandText = "select * from pn_xm where j_xm=" & Cb_Jenis.SelectedIndex & " and tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text & ""
        'Rdr = Cmd.ExecuteReader()
        'If Rdr.Read Then
        '    Msg(Me, "Rekod Telah Ada!")
        '    Rdr.Close()
        '    Exit Sub
        'End If
        'Rdr.Close()

        'Select Case Cb_Jenis.SelectedIndex
        '    Case 1
        '        SQL += "insert pn_xm (j_xm, tahun, siri, t1_tkh, t1_masa, ag, markah_min, status) select "
        '        SQL += "1,"
        '        SQL += Tx_Tahun.Text & ","
        '        SQL += Tx_Siri.Text & ","
        '        SQL += Chk_Tkh(Tx_Tkh_JB.Text) & ","
        '        SQL += "'" & Tx_Ms_JB.Text & "',"
        '        SQL += "0,"
        '        SQL += Tx_Mk_JB.Text & ","
        '        SQL += "1"
        '    Case 4
        '        SQL += "insert pn_xm (j_xm, tahun, siri, t1_tkh, t1_masa, o_tkh, o_masa, v_tkh, v_masa, ag, markah_min, status) select "
        '        SQL += "4,"
        '        SQL += Tx_Tahun.Text & ","
        '        SQL += Tx_Siri.Text & ","
        '        SQL += Chk_Tkh(Tx_Tkh_BIT.Text) & ","
        '        SQL += "'" & Tx_Ms_BIT.Text & "',"
        '        SQL += Chk_Tkh(Tx_Tkh_BIO.Text) & ","
        '        SQL += "'" & Tx_Ms_BIO.Text & "',"
        '        SQL += Chk_Tkh(Tx_Tkh_BIV.Text) & ","
        '        SQL += "'" & Tx_Ms_BIV.Text & "',"
        '        SQL += "0,"
        '        SQL += Tx_Mk_BI.Text & ","
        '        SQL += "1"
        '    Case 2
        '        SQL += "insert pn_xm (j_xm, tahun, siri, t1_tkh, t1_masa, t2_tkh, t2_masa, o_tkh, o_masa, ag, markah_min, status) select "
        '        SQL += "2,"
        '        SQL += Tx_Tahun.Text & ","
        '        SQL += Tx_Siri.Text & ","
        '        SQL += Chk_Tkh(Tx_Tkh_JMT1.Text) & ","
        '        SQL += "'" & Tx_Ms_JMT1.Text & "',"
        '        SQL += Chk_Tkh(Tx_Tkh_JMT2.Text) & ","
        '        SQL += "'" & Tx_Ms_JMT2.Text & "',"
        '        SQL += Chk_Tkh(Tx_Tkh_JMO.Text) & ","
        '        SQL += "'" & Tx_Ms_JMO.Text & "',"
        '        SQL += "0,"
        '        SQL += Tx_Mk_JM.Text & ","
        '        SQL += "1"
        '    Case 3
        '        SQL += "insert pn_xm (j_xm, tahun, siri, t1_tkh, t1_masa, t2_tkh, t2_masa, p_tkh, p_masa, ag, markah_min, status) select "
        '        SQL += "3,"
        '        SQL += Tx_Tahun.Text & ","
        '        SQL += Tx_Siri.Text & ","
        '        SQL += Chk_Tkh(Tx_Tkh_PJT1.Text) & ","
        '        SQL += "'" & Tx_Ms_PJT1.Text & "',"
        '        SQL += Chk_Tkh(Tx_Tkh_PJT2.Text) & ","
        '        SQL += "'" & Tx_Ms_PJT2.Text & "',"
        '        SQL += Chk_Tkh(Tx_Tkh_PJP.Text) & ","
        '        SQL += "'" & Tx_Ms_PJP.Text & "',"
        '        SQL += "0,"
        '        SQL += Tx_Mk_PJ.Text & ","
        '        SQL += "1"
        'End Select

        'Fixing date issue 20082018 - OSH

        'Tarikh Jururawat Berdaftar
        Dim RN_date As DateTime
        Dim RN_date_text As String
        If Tx_Tkh_JB.Text.Trim <> String.Empty Then
            RN_date_text = Tx_Tkh_JB.Text.Trim
            RN_date = DateTime.ParseExact(RN_date_text, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            RN_date_text = RN_date.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            RN_date_text = "'" & RN_date_text & "'"
        Else
            RN_date_text = "NULL"
        End If

        'Tarikh Jururawat Masyarakat
        Dim CN_date As DateTime
        Dim CN_date_text As String
        If Tx_Tkh_JMT1.Text.Trim <> String.Empty Then
            CN_date_text = Tx_Tkh_JMT1.Text.Trim
            CN_date = DateTime.ParseExact(CN_date_text, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            CN_date_text = CN_date.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            CN_date_text = "'" & CN_date_text & "'"
        Else
            CN_date_text = "NULL"
        End If

        'Tarikh Penolong Jururawat
        Dim AN_date As DateTime
        Dim AN_date_text As String
        If Tx_Tkh_PJT1.Text.Trim <> String.Empty Then
            AN_date_text = Tx_Tkh_PJT1.Text.Trim
            AN_date = DateTime.ParseExact(AN_date_text, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            AN_date_text = AN_date.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            AN_date_text = "'" & AN_date_text & "'"
        Else
            AN_date_text = "NULL"
        End If


        'Tarikh Post Basic Bidan1
        Dim MP_date As DateTime
        Dim MP_date_text As String
        If Tx_Tkh_BIT.Text.Trim <> String.Empty Then
            MP_date_text = Tx_Tkh_BIT.Text.Trim
            MP_date = DateTime.ParseExact(MP_date_text, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            MP_date_text = MP_date.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            MP_date_text = "'" & MP_date_text & "'"
        Else
            MP_date_text = "NULL"
        End If

        'Tarikh Pengisytiharan
        Dim Result_date As DateTime
        Dim Result_date_text As String
        If Tx_Tkh_Papar.Text.Trim <> String.Empty Then
            Result_date_text = Tx_Tkh_Papar.Text.Trim
            Result_date = DateTime.ParseExact(Result_date_text, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            Result_date_text = Result_date.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            Result_date_text = "'" & Result_date_text & "'"
        Else
            Result_date_text = "NULL"
        End If


        If Session("PN_Pinda") Then
            Select Case Cb_Jenis.SelectedIndex
                Case 1
                    SQL = "update pn_xm set "
                    SQL += "t1_tkh = " & RN_date_text & ", t1_masa = '" & Tx_Ms_JB.Text & "', markah_min =" & Tx_Mk_JB.Text & ", tkh_papar=" & Result_date_text & " "
                    SQL += "where j_xm=1 and id_xm=" & Tx_Id.Text & ""
                Case 2
                    SQL = "update pn_xm set "
                    SQL += "t1_tkh = " & CN_date_text & ", t1_masa = '" & Tx_Ms_JMT1.Text & "', markah_min =" & Tx_Mk_JM.Text & ", tkh_papar=" & Result_date_text & " "
                    SQL += "where j_xm=2 and id_xm=" & Tx_Id.Text & ""
                Case 3
                    SQL = "update pn_xm set "
                    SQL += "t1_tkh = " & AN_date_text & ", t1_masa = '" & Tx_Ms_PJT1.Text & "', markah_min =" & Tx_Mk_PJ.Text & ", tkh_papar=" & Result_date_text & " "
                    SQL += "where j_xm=3 and id_xm=" & Tx_Id.Text & ""
                Case 4
                    SQL = "update pn_xm set "
                    SQL += "t1_tkh = " & MP_date_text & ", t1_masa = '" & Tx_Ms_BIT.Text & "', markah_min =" & Tx_Mk_BI.Text & ", tkh_papar=" & Result_date_text & " "
                    SQL += "where j_xm=4 and id_xm=" & Tx_Id.Text & ""
            End Select
            Try
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Cari()
                Session("PN_Pinda") = False
                Msg(Me, "Rekod Telah Dikemaskini...")
                Exit Sub
            Catch ex As Exception
                Msg(Me, ex.Message)
            End Try
        End If

        Cmd.CommandText = "select * from pn_xm where j_xm=" & Cb_Jenis.SelectedIndex & " and tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text & ""
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Telah Ada!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()

        Select Case Cb_Jenis.SelectedIndex
            Case 1
                SQL += "insert pn_xm (j_xm, tahun, siri, t1_tkh, t1_masa, ag, markah_min, status) select "
                SQL += "1,"
                SQL += Tx_Tahun.Text & ","
                SQL += Tx_Siri.Text & ","
                SQL += RN_date_text & ","
                SQL += "'" & Tx_Ms_JB.Text & "',"
                SQL += "0,"
                SQL += Tx_Mk_JB.Text & ","
                SQL += "1"
            Case 4
                SQL += "insert pn_xm (j_xm, tahun, siri, t1_tkh, t1_masa, ag, markah_min, status) select "
                SQL += "4,"
                SQL += Tx_Tahun.Text & ","
                SQL += Tx_Siri.Text & ","
                SQL += MP_date_text & ","
                SQL += "'" & Tx_Ms_BIT.Text & "',"
                SQL += "0,"
                SQL += Tx_Mk_BI.Text & ","
                SQL += "1"
            Case 2
                SQL += "insert pn_xm (j_xm, tahun, siri, t1_tkh, t1_masa, ag, markah_min, status) select "
                SQL += "2,"
                SQL += Tx_Tahun.Text & ","
                SQL += Tx_Siri.Text & ","
                SQL += CN_date_text & ","
                SQL += "'" & Tx_Ms_JMT1.Text & "',"
                SQL += "0,"
                SQL += Tx_Mk_JM.Text & ","
                SQL += "1"
            Case 3
                SQL += "insert pn_xm (j_xm, tahun, siri, t1_tkh, t1_masa, ag, markah_min, status) select "
                SQL += "3,"
                SQL += Tx_Tahun.Text & ","
                SQL += Tx_Siri.Text & ","
                SQL += Chk_Tkh(Tx_Tkh_PJT1.Text) & ","
                SQL += "'" & Tx_Ms_PJT1.Text & "',"
                SQL += "0,"
                SQL += Tx_Mk_PJ.Text & ","
                SQL += "1"
        End Select

        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Msg(Me, "Rekod Telah Disimpan...")
            Cari()
            Reset()
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
        Cn.Close()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        Dim i As Int16
        For i = 2 To e.Row.Cells.Count - 7
            e.Row.Cells(i).Visible = False
        Next

        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Public Sub Fn_Check()
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Dim dt As DateTime
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "select tkh_papar from pn_xm where id_xm=" & Tx_Id.Text & " and tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text & ""
        Rdr = Cmd.ExecuteReader()
        
        If Rdr.Read Then
            'Comment Original 20082018 - OSH
            'If Not IsDBNull(Rdr("tkh_papar")) Then Tx_Tkh_Papar.Text = Rdr("tkh_papar") Else Tx_Tkh_Papar.Text = ""

            'Fixing date format display 20082018 - OSH
            If Not IsDBNull(Rdr("tkh_papar")) Then dt = Rdr("tkh_papar") : Tx_Tkh_Papar.Text = dt.ToString("dd'/'MM'/'yyyy") Else Tx_Tkh_Papar.Text = ""
        End If
        Rdr.Close()
        Cn.Close()
    End Sub

    Private Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles Gd.SelectedIndexChanged
        Tx_Id.Text = Gd.SelectedRow.Cells(2).Text
        Tx_Tahun.Text = Gd.SelectedRow.Cells(4).Text
        Tx_Siri.Text = Gd.SelectedRow.Cells(5).Text
        Fn_Check()
        'If Gd.SelectedRow.Cells(19).Text = "" Then Tx_Tkh_Papar.Text = "" Else Tx_Tkh_Papar.Text = Gd.SelectedRow.Cells(19).Text
        Hide_Panel()
        'comment Original 28082018 -OSH
        'Select Case Gd.SelectedRow.Cells(3).Text
        '    Case "1"
        '        Cb_Jenis.SelectedIndex = 1
        '        Tx_Tkh_JB.Text = Gd.SelectedRow.Cells(6).Text
        '        Tx_Ms_JB.Text = Gd.SelectedRow.Cells(7).Text
        '        Tx_Mk_JB.Text = Gd.SelectedRow.Cells(17).Text
        '        Panel4.Visible = True
        '    Case "2"
        '        Cb_Jenis.SelectedIndex = 2
        '        Tx_Tkh_JMT1.Text = Gd.SelectedRow.Cells(6).Text
        '        Tx_Ms_JMT1.Text = Gd.SelectedRow.Cells(7).Text
        '        Tx_Tkh_JMT2.Text = Gd.SelectedRow.Cells(8).Text
        '        Tx_Ms_JMT2.Text = Gd.SelectedRow.Cells(9).Text
        '        Tx_Tkh_JMO.Text = Gd.SelectedRow.Cells(10).Text
        '        Tx_Ms_JMO.Text = Gd.SelectedRow.Cells(11).Text
        '        Tx_Mk_JM.Text = Gd.SelectedRow.Cells(17).Text
        '        Panel11.Visible = True
        '        'Panel12.Visible = True
        '        'Panel13.Visible = True
        '    Case "3"
        '        Cb_Jenis.SelectedIndex = 3
        '        Tx_Tkh_PJT1.Text = Gd.SelectedRow.Cells(6).Text
        '        Tx_Ms_PJT1.Text = Gd.SelectedRow.Cells(7).Text
        '        Tx_Tkh_PJT2.Text = Gd.SelectedRow.Cells(8).Text
        '        Tx_Ms_PJT2.Text = Gd.SelectedRow.Cells(9).Text
        '        Tx_Tkh_PJP.Text = Gd.SelectedRow.Cells(14).Text
        '        Tx_Ms_PJP.Text = Gd.SelectedRow.Cells(15).Text
        '        Tx_Mk_PJ.Text = Gd.SelectedRow.Cells(17).Text
        '        Panel8.Visible = True
        '        'Panel9.Visible = True
        '        'Panel10.Visible = True
        '    Case "4"
        '        Cb_Jenis.SelectedIndex = 4
        '        Tx_Tkh_BIT.Text = Gd.SelectedRow.Cells(6).Text
        '        Tx_Ms_BIT.Text = Gd.SelectedRow.Cells(7).Text
        '        Tx_Tkh_BIO.Text = Gd.SelectedRow.Cells(10).Text
        '        Tx_Ms_BIO.Text = Gd.SelectedRow.Cells(11).Text
        '        Tx_Tkh_BIV.Text = Gd.SelectedRow.Cells(12).Text
        '        Tx_Ms_BIV.Text = Gd.SelectedRow.Cells(13).Text
        '        Tx_Mk_BI.Text = Gd.SelectedRow.Cells(17).Text
        '        Panel5.Visible = True
        '        'Comment Original 20082018 - OSH 
        '        'Panel6.Visible = True
        '        'Panel7.Visible = True
        'End Select

        'Fixing Date format 28082018 - OSH
        Dim dtr, dtr2, dtr3, dtr4 As DateTime

        Select Case Gd.SelectedRow.Cells(3).Text
            Case "1"
                Cb_Jenis.SelectedIndex = 1
                dtr = Gd.SelectedRow.Cells(6).Text : Tx_Tkh_JB.Text = dtr.ToString("dd'/'MM'/'yyyy")
                Tx_Ms_JB.Text = Gd.SelectedRow.Cells(7).Text
                Tx_Mk_JB.Text = Gd.SelectedRow.Cells(17).Text
                Panel4.Visible = True
            Case "2"
                Cb_Jenis.SelectedIndex = 2
                dtr2 = Gd.SelectedRow.Cells(6).Text : Tx_Tkh_JMT1.Text = dtr2.ToString("dd'/'MM'/'yyyy")
                Tx_Ms_JMT1.Text = Gd.SelectedRow.Cells(7).Text
                dtr3 = Gd.SelectedRow.Cells(8).Text : Tx_Tkh_JMT2.Text = dtr3.ToString("dd'/'MM'/'yyyy")
                Tx_Ms_JMT2.Text = Gd.SelectedRow.Cells(9).Text
                dtr4 = Gd.SelectedRow.Cells(10).Text : Tx_Tkh_JMO.Text = dtr3.ToString("dd'/'MM'/'yyyy")
                Tx_Ms_JMO.Text = Gd.SelectedRow.Cells(11).Text
                Tx_Mk_JM.Text = Gd.SelectedRow.Cells(17).Text
                Panel11.Visible = True
                'Panel12.Visible = True
                'Panel13.Visible = True
            Case "3"
                Cb_Jenis.SelectedIndex = 3
                dtr2 = Gd.SelectedRow.Cells(6).Text : Tx_Tkh_PJT1.Text = dtr2.ToString("dd'/'MM'/'yyyy")
                Tx_Ms_PJT1.Text = Gd.SelectedRow.Cells(7).Text
                'Comment Original 28112018 - OSH 
                'dtr3 = Gd.SelectedRow.Cells(8).Text : Tx_Tkh_PJT2.Text = dtr3.ToString("dd'/'MM'/'yyyy")
                Tx_Ms_PJT2.Text = Gd.SelectedRow.Cells(9).Text
                'Comment Original 28112018 - OSH 
                'dtr4 = Gd.SelectedRow.Cells(14).Text : Tx_Tkh_PJP.Text = dtr3.ToString("dd'/'MM'/'yyyy")
                Tx_Ms_PJP.Text = Gd.SelectedRow.Cells(15).Text
                Tx_Mk_PJ.Text = Gd.SelectedRow.Cells(17).Text
                Panel8.Visible = True
                'Panel9.Visible = True
                'Panel10.Visible = True
            Case "4"
                Cb_Jenis.SelectedIndex = 4
                dtr2 = Gd.SelectedRow.Cells(6).Text : Tx_Tkh_BIT.Text = dtr2.ToString("dd'/'MM'/'yyyy")
                Tx_Ms_BIT.Text = Gd.SelectedRow.Cells(7).Text
                'Comment Original 02102018 - OSH
                'dtr3 = Gd.SelectedRow.Cells(10).Text : Tx_Tkh_BIO.Text = dtr3.ToString("dd'/'MM'/'yyyy")
                Tx_Ms_BIO.Text = Gd.SelectedRow.Cells(11).Text
                'Comment Original 02102018 - OSH
                'dtr4 = Gd.SelectedRow.Cells(12).Text : Tx_Tkh_BIV.Text = dtr3.ToString("dd'/'MM'/'yyyy")
                Tx_Ms_BIV.Text = Gd.SelectedRow.Cells(13).Text
                Tx_Mk_BI.Text = Gd.SelectedRow.Cells(17).Text
                Panel5.Visible = True
                'Comment Original 20082018 - OSH 
                'Panel6.Visible = True
                'Panel7.Visible = True
        End Select
        Session("PN_Pinda") = True
    End Sub

    Protected Sub Cb_Jenis_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Jenis.SelectedIndexChanged
        Hide_Panel()
        If Cb_Jenis.SelectedIndex = 1 Then
            Panel4.Visible = True
        ElseIf Cb_Jenis.SelectedIndex = 4 Then
            Panel5.Visible = True
            'Comment Original 20082018 - OSH
            'Panel6.Visible = True
            'Panel7.Visible = True
        ElseIf Cb_Jenis.SelectedIndex = 2 Then
            Panel11.Visible = True
            'Panel12.Visible = True
            'Panel13.Visible = True
        ElseIf Cb_Jenis.SelectedIndex = 3 Then
            Panel8.Visible = True
            'Panel9.Visible = True
            'Panel10.Visible = True
        End If
    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari2.Click
        Reset()
    End Sub

End Class