﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm11
    Inherits System.Web.UI.Page    

    Public Sub Cari()
        Dim Jawatan, Jawatan2, Jawatan3 As Integer
        If Cb_Kursus.SelectedValue = 1 Then Jawatan = 1 : Jawatan2 = 5 : Jawatan3 = 8
        If Cb_Kursus.SelectedValue = 2 Then Jawatan = 2 : Jawatan2 = 2 : Jawatan3 = 2
        If Cb_Kursus.SelectedValue = 3 Then Jawatan = 3 : Jawatan2 = 3 : Jawatan3 = 3
        If Cb_Kursus.SelectedValue = 4 Then Jawatan = 4 : Jawatan2 = 4 : Jawatan3 = 4

        Dim SQL As String = ""
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        'Comment Ori 17092015 -OSH
        'SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as '<PERSON><PERSON><PERSON><PERSON>', cast(xc.markah_jum as decimal(6,1)) as 'JUMLA<PERSON> MARKAH', xc.KEPUTUSAN, xc.id_xm from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where (j_kursus = " & Jawatan & " or j_kursus = " & Jawatan2 & " or j_kursus = " & Jawatan3 & ") and xc.status_ulang=0 and xc.id_xm in (select id_xm from pn_xm where j_xm = " & Cb_Kursus.SelectedValue & " and status=1) order by cast(xc.ag as integer), p.id_kolej"


        'Fixing multiple records display per canidate 17092015 - OSH
        'SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', cast(xc.markah_jum as decimal(6,1)) as 'JUMLAH MARKAH', xc.KEPUTUSAN, xc.id_xm from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where (j_kursus = " & Jawatan & " or j_kursus = " & Jawatan2 & " or j_kursus = " & Jawatan3 & ") and xc.status_ulang=0 and xc.id_xm in (select id_xm from pn_xm where j_xm = " & Cb_Kursus.SelectedValue & " and status=1) and p.status is null order by cast(xc.ag as integer), p.id_kolej"

        'Comment Ori 05042016 -OSH
        ' Fixing query list canidate result 22032016 -OSH
        'SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', cast(xc.markah_jum as decimal(6,1)) as 'JUMLAH MARKAH', xc.KEPUTUSAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where (j_kursus = " & Jawatan & " or j_kursus = " & Jawatan2 & " or j_kursus = " & Jawatan3 & ") and xc.status_ulang=0 and xc.id_xm in (select id_xm from pn_xm where j_xm = " & Cb_Kursus.SelectedValue & " and status=1) and p.status is null order by cast(xc.ag as integer), p.id_kolej"
        ' Fixing query list canidate result + exam id  05042016 -OSH
        SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', cast(xc.markah_jum as decimal(6,1)) as 'JUMLAH MARKAH', xc.KEPUTUSAN,  xc.id_xm from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where (j_kursus = " & Jawatan & " or j_kursus = " & Jawatan2 & " or j_kursus = " & Jawatan3 & ") and xc.status_ulang=0 and xc.id_xm in (select id_xm from pn_xm where j_xm = " & Cb_Kursus.SelectedValue & " and status=1) and p.status is null order by cast(xc.ag as integer), p.id_kolej"

     'SQL = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'ANGKA GILIRAN', p.NAMA, p.NOKP as 'NO. KP/PASPORT', pnk.DC_KOLEJ as 'KOLEJ', cast(xc.markah_jum as decimal(6,1)) as 'JUMLAH MARKAH', xc.KEPUTUSAN from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej inner join xm_calon xc on p.nokp = xc.nokp where (j_kursus = " & Jawatan & " or j_kursus = " & Jawatan2 & " or j_kursus = " & Jawatan3 & ") and xc.status_ulang=0 and xc.id_xm in (select top 1 id_xm from pn_xm where j_xm = " & Cb_Kursus.SelectedValue & " and status=0 order by id_xm desc ) and p.status is null order by cast(xc.ag as integer), p.id_kolej"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)

        List_Adp.Fill(List_Data, "pelatih")
        Gd.DataSource = List_Data.Tables("pelatih")
        DataBind()
        Gd.Visible = True
    End Sub

    Public Sub Statistik()
        'Dim i As Int16, SQL As String = "", L As Integer = 0, G As Integer = 0
        Dim i As Int16, SQL As String = "", L As Double = 0, G As Double = 0, T As Double = 0

        For i = 0 To Gd.Rows.Count - 1
            If Gd.Rows.Item(i).Cells(6).Text = "L" Then
                L += 1
                Gd.Rows.Item(i).BackColor = Drawing.Color.WhiteSmoke
            ElseIf Gd.Rows.Item(i).Cells(6).Text = "G" Then
                G += 1
                'Gd.Rows.Item(i).ForeColor = Gd.HeaderStyle.BackColor
                Gd.Rows.Item(i).ForeColor = Gd.ForeColor.Red
                'Gd.Rows.Item(i).BackColor = Drawing.Color.Red ' FIXING 22032016 - OSH
            ElseIf Gd.Rows.Item(i).Cells(6).Text = "T" Then
                T += 1
                Gd.Rows.Item(i).ForeColor = Gd.HeaderStyle.BackColor
                Gd.Rows.Item(i).BackColor = Drawing.Color.WhiteSmoke
            End If
        Next

        cmd_Tutup.Enabled = False
        If L = 0 And G = 0 Then Exit Sub

        cmd_Tutup.Enabled = True
        Tx_Calon.Text = Gd.Rows.Count
        Tx_Calon0.Text = L + G + T
        'Comment Ori 06072018 - OSH
        'Tx_Calon_L.Text = L & " (" & Math.Round(CDbl(L / (Gd.Rows.Count - T) * 100), 1) & "%)"
        'Tx_Calon_G.Text = G & " (" & Math.Round((100 - CDbl(L / (Gd.Rows.Count - T) * 100)), 1) & "%)"
        'Tx_Calon_T.Text = T & " (" & Math.Round(CDbl(T / (Gd.Rows.Count) * 100), 1) & "%)"

        'Adjust 2 decimal point 06072018 - OSH
        Tx_Calon_L.Text = L & " (" & Math.Round(CDbl(L / (Gd.Rows.Count - T) * 100), 2) & "%)"
        Tx_Calon_G.Text = G & " (" & Math.Round((100 - CDbl(L / (Gd.Rows.Count - T) * 100)), 2) & "%)"
        Tx_Calon_T.Text = T & " (" & Math.Round(CDbl(T / (Gd.Rows.Count) * 100), 2) & "%)"
        Tx_Menduduki.Text = L + G
    End Sub

    private sub gd_rowcreated(byval sender as object, byval e as system.web.ui.webcontrols.gridviewroweventargs) handles gd.rowcreated
         'Add filter needed columns only from result query 22032016 -osh
          if e.row.rowindex = -1 then  else e.row.cells(0).text = e.row.rowindex + 1 & "."   
                e.row.cells(1).horizontalalign = horizontalalign.center 'canidate number 
                e.row.cells(2).horizontalalign = horizontalalign.left   'name
                e.row.cells(3).horizontalalign = horizontalalign.center 'idenfication numbers 
                e.row.cells(4).horizontalalign = horizontalalign.left   'college
                e.row.cells(5).horizontalalign = horizontalalign.center 'score
        e.Row.Cells(6).HorizontalAlign = HorizontalAlign.Center 'result
        e.Row.Cells(7).Visible = False 'exam id  hidden
                e.row.cells(1).width = unit.pixel(60)
                e.row.cells(2).width = unit.pixel(60)
                e.row.cells(3).width = unit.pixel(60)
                e.row.cells(4).width = unit.pixel(60)
                e.row.cells(5).width = unit.pixel(60)
                e.row.cells(6).width = unit.pixel(60)
    end sub

    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(0).Text = CStr(e.Row.RowIndex + 1) & "."
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Sah_Keputusan", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
        cmd_Tutup.Enabled = False
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Semak.Click
        If Cb_Kursus.SelectedIndex < 1 Then Msg(Me, "Sila pilih Jenis Kursus!") : Cb_Kursus.Focus() : Exit Sub
        Tx_Calon.Text = "" : Tx_Calon_G.Text = "" : Tx_Calon_L.Text = ""
        Cari()
        Statistik()

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select markah_min, id_xm from pn_xm where j_xm = " & Cb_Kursus.SelectedIndex & " and status = 1"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Tx_Lulus.Text = (Rdr(0))
        Else
            Tx_Lulus.Text = "TIADA"
        End If
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmd_Tutup_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Tutup.Click
        Dim i As Int16, SQL As String = "", SQL2 As String = ""

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Cmd2 As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Cmd2.Connection = Cn

        If Tx_Calon.Text = Tx_Calon0.Text Then  Else Msg(Me, "Sila Pastikan Markah Calon Dijana") : Exit Sub
        Dim jwtn As Integer = Cb_Kursus.SelectedValue
        Dim ulangan As Integer
        'Comment test 21122015
        'If Jwtn = 4 Then  Else Jwtn = 3



        For i = 0 To Gd.Rows.Count - 1
            If Gd.Rows.Item(i).Cells(6).Text = "L" Then
                'SQL += "update xm_calon set ulangan = (select ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ")+1, status_ulang='1' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ";" & vbCrLf
                SQL += "update xm_calon set status_ulang='1' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ";" & vbCrLf
                'Add Improvent Pass Query With Category Type 15092012 - OSH
                SQL += "update pelatih set saring='2' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and J_Kursus = " & jwtn & " ;"
                'comment original Pass Status Without Category Type 15092012 - OSH
                'SQL += "update pelatih set saring='2' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "';"
            ElseIf Gd.Rows.Item(i).Cells(6).Text = "G" Then
                'Comment Ori 22092015 - OSH
                'SQL += "update xm_calon set ulangan = (select ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ")+1, status_ulang=NULL where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ";" & vbCrLf
                'SQL += "update pelatih set saring='0' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and ((select top 1 ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "' order by id_xm desc)<" & jwtn & ");"
                'SQL += "update pelatih set saring='2' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and ((select  top 1  ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "' order by id_xm desc)=" & jwtn & ");"

                'Comment Ori - mistake rule 22122015 - OSH
                'Fixing  add ulangan parameter 22092015 - OSH
                'SQL += "update xm_calon set ulangan = (select ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ")+1, status_ulang=NULL where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ";" & vbCrLf
                'SQL += "update pelatih set saring='0' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and ulangan = ((select top 1 ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "' order by id_xm desc)<" & jwtn & ");"
                'SQL += "update pelatih set saring='2' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and ulangan = ((select  top 1  ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "' order by id_xm desc)=" & jwtn & ");"


                'Fixing rules mistake - couse types 2212015-OSH
                SQL += "update xm_calon set ulangan = (select ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ")+1, status_ulang=NULL where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ";" & vbCrLf
                SQL2 = " select count(nokp) from xm_calon where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm in (select distinct x.id_xm from pn_kursus k inner join pn_xm x on (k.j_xm = x.j_xm) where x.j_xm = " & jwtn & " and x.status = 0);"
                Cmd2.CommandText = SQL2
                ulangan = Convert.ToInt32(Cmd2.ExecuteScalar())
                If ulangan < 4 Then
                    SQL += "update pelatih set saring='0' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and j_kursus in (select id_kursus from pn_kursus where j_xm = " & jwtn & ");"
                ElseIf ulangan = 4 Then
                    SQL += "update pelatih set saring='2' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and j_kursus in (select id_kursus from pn_kursus where j_xm = " & jwtn & ");"
                End If

            ElseIf Gd.Rows.Item(i).Cells(6).Text = "T" Then

                'Comment Ori 22092015 - OSH
                'SQL += "update xm_calon set ulangan = (select ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ")+1, status_ulang=NULL where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ";" & vbCrLf
                'SQL += "update pelatih set saring='0' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and ((select  top 1 ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "  order by id_xm desc')<" & jwtn & ");"
                'SQL += "update pelatih set saring='2' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and ((select  top 1 ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "'  order by id_xm desc)=" & jwtn & ");"

                'Comment Ori - mistake rule 22122015 - OSH
                'Fixing  add ulangan parameter 22092015 - OSH
                'SQL += "update xm_calon set ulangan = (select ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ")+1, status_ulang=NULL where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ";" & vbCrLf
                'SQL += "update pelatih set saring='0' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and ((select  top 1 ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "  order by id_xm desc')<" & jwtn & ");"
                'SQL += "update pelatih set saring='2' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and ((select  top 1 ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "'  order by id_xm desc)=" & jwtn & ");"

                'Fixing rules mistake - couse types 2212015-OSH
                SQL += "update xm_calon set ulangan = (select ulangan from xm_calon where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ")+1, status_ulang=NULL where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm=" & Gd.Rows.Item(i).Cells(7).Text & ";" & vbCrLf
                SQL2 = " select count(nokp) from xm_calon where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and id_xm in (select distinct x.id_xm from pn_kursus k inner join pn_xm x on (k.j_xm = x.j_xm) where x.j_xm = " & jwtn & " and x.status = 0);"
                Cmd2.CommandText = SQL2
                ulangan = Convert.ToInt32(Cmd2.ExecuteScalar())
                If ulangan < 4 Then
                    SQL += "update pelatih set saring='0' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and j_kursus in (select id_kursus from pn_kursus where j_xm = " & jwtn & ");"
                ElseIf ulangan = 4 Then
                    SQL += "update pelatih set saring='2' where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and j_kursus in (select id_kursus from pn_kursus where j_xm = " & jwtn & ");"
                End If
            End If
        Next

        SQL += "update pn_xm set status=0 where j_xm = " & Cb_Kursus.SelectedIndex & " and status = 1;"

        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Cn.Close()

            Session("Msg_Tajuk") = "Sah Keputusan Peperiksaan"
            Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
            Response.Redirect("p0_Mesej.aspx")
        Catch ex As Exception
            Msg(Me, ex.Message)
            Cn.Close()
        End Try

        'Msg(Me, "Rekod Telah Dikemaskini...")
    End Sub

    
    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged

    End Sub
End Class