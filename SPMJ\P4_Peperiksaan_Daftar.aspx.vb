﻿Imports System.Globalization
Imports System.Data.SqlClient

Public Class P4_Peperiksaan_Daftar
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If String.IsNullOrEmpty(Session("Id_PG")) Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If IsPostBack Then Exit Sub

        Dim Cn As New SqlConnection : Dim Cmd As New SqlCommand : Dim Rdr As SqlDataReader
        Cn.ConnectionString = ServerId_SQL : Cn.Open() : Cmd.Connection = Cn

        'WARGANEGARA
        Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara ORDER BY dc_NEGARA"
        Rdr = Cmd.ExecuteReader()
        Cb_TP_Negera.Items.Clear()
        While Rdr.Read
            Cb_TP_Negera.Items.Add(Rdr(0))
            Cb_TP_Negera.Items.Item(Cb_TP_Negera.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()


    End Sub

    Protected Sub cmdHantar_Click(sender As Object, e As EventArgs) Handles cmdHantar.Click
        Dim X As String = ""

        If Cb_Jenis.Text.Trim = "" Then X += "Jenis Pendaftaran, "
        If Tx_NoKP.Text.Trim = "" Then
            If Cb_NoKP.SelectedIndex = 0 Then X += "No. Kad Pengenalan, "
            If Cb_NoKP.SelectedIndex = 1 Then X += "No. Tentera, "
            If Cb_NoKP.SelectedIndex = 2 Then X += "No. Pasport, "
        End If
        If Tx_Nama.Text.Trim = "" Then X += "Nama, "
        If Cb_Warga.Text.Trim = "" Then X += "Warganegara, "
        If Cb_Jantina.Text.Trim = "" Then X += "Jantina, "
        If Cb_Bangsa.Text.Trim = "" Then X += "Bangsa, "
        If Tx_TP_Alamat.Text.Trim = "" Then X += "Alamat Tetap, "
        If Cb_Warga.Text.Trim = "" Or Cb_Warga.SelectedItem.Text = "MALAYSIA" Then If Tx_TP_Poskod.Text.Trim = "" Then X += "Poskod, "
        If Tx_TP_Bandar.Text.Trim = "" Then X += "Bandar, "
        If Cb_Warga.Text.Trim = "" Or Cb_Warga.SelectedItem.Text = "MALAYSIA" Then
            If Cb_TP_Negeri.Text.Trim = "" Then X += "Negeri, "
        Else
            If Cb_TP_Negeri.Text.Trim = "" Then X += "Negara, "
        End If
        If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi, "
        If Cb_Tajaan.Text.Trim = "" Then X += "Tajaan, "
        If Cb_Sesi_Bulan.Text.Trim = "" Or Cb_Sesi_Bulan.Text.Trim = "(BULAN)" Or Cb_Sesi_Tahun.Text.Trim = "" Or Cb_Sesi_Tahun.Text.Trim = "(TAHUN)" Then X += "Sesi Pengambilan, "
        If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "
        If Tx_T_Latihan.Text.Trim = "" Then X += "Tarikh Tamat Latihan, "
        If Tx_Periksa.Text.Trim = "" Then X += "Tarikh Peperiksaan Akhir, "
        If Cb_Status.SelectedIndex = 0 Then X += "Status, "
        'Add brith date check 15092020 - OSH
        If Tx_Tkh_Lahir.Text = "" Or Tx_Umur.Text = "0" Then X += "Tarikh Lahir,"
        If X.Trim = "" Then Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:" + X) : Exit Sub

        'Check No KP length
        X = ""
        If Cb_NoKP.SelectedIndex = 0 Then If Tx_NoKP.Text.Length < 12 Then X = "Kad Pengenalan"
        If X.Trim = "" Then Else Msg(Me, "Maklumat No " & X & " tidak lengkap") : Tx_NoKP.Focus() : Exit Sub

    End Sub
End Class