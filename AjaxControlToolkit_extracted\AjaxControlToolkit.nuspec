<?xml version="1.0"?>
<package xmlns="http://schemas.microsoft.com/packaging/2010/07/nuspec.xsd">
  <metadata>
    <id>AjaxControlToolkit</id>
    <version>4.1.60919</version>
    <authors>Microsoft</authors>
    <owners>Microsoft</owners>
    <licenseUrl>http://ajaxcontroltoolkit.codeplex.com/license</licenseUrl>
    <projectUrl>http://ajaxcontroltoolkit.codeplex.com/</projectUrl>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <description>The Ajax Control Toolkit contains a rich set of controls that you can use to build highly responsive and interactive Ajax-enabled Web applications. The Ajax Control Toolkit contains more than 40 controls, including the AutoComplete, CollapsiblePanel, ColorPicker, MaskedEdit, Calendar, Accordion, and Watermark controls. Using the Ajax Control Toolkit, you can build Ajax-enabled ASP.NET Web Forms applications by dragging-and-dropping Toolkit controls from the Visual Studio Toolbox onto a Web Forms page.</description>
    <summary>Build Ajax applications in Web Forms using the more than 40 controls in the Ajax Control Toolkit</summary>
    <language>en-US</language>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="System.Web" targetFramework=".NETFramework4.5" />
    </frameworkAssemblies>
  </metadata>
</package>