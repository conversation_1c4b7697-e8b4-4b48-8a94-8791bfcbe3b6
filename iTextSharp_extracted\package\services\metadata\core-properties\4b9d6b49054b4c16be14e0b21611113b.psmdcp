﻿<?xml version="1.0" encoding="utf-8"?><coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties"><dc:creator><PERSON>,<PERSON>,et al.</dc:creator><dc:description>iText is a PDF library that allows you to CREATE, ADAPT, INSPECT and MAINTAIN documents in the Portable Document Format (PDF):
 - Generate documents and reports based on data from an XML file or a database
 - Create maps and books, exploiting numerous interactive features available in PDF
 - Add bookmarks, page numbers, watermarks, and other features to existing PDF documents
 - Split or concatenate pages from existing PDF files
 - Fill out interactive forms
 - Serve dynamically generated or manipulated PDF documents to a web browser

 iText is used by Java, .NET, Android and GAE developers to enhance their applications with PDF functionality. 
 iTextSharp is the .NET port.

 Several iText engineers are actively supporting the project on StackOverflow: http://stackoverflow.com/questions/tagged/itext</dc:description><dc:identifier>iTextSharp</dc:identifier><version>5.5.10</version><dc:language>en-US</dc:language><keywords>itext itextsharp c# .net csharp pdf</keywords><dc:title>iTextSharp</dc:title><dc:subject>NuGet Package Explorer</dc:subject></coreProperties>