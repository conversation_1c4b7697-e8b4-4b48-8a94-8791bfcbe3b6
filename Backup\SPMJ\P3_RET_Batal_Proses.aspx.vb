﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class P3_RET_Batal_Proses
    Inherits System.Web.UI.Page


    Public Sub Isi_RET()
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Dim List_Adp As New SqlDataAdapter("", Cn)
        Dim SQL As String

        If Tx_NoKP.Text <> "" Then
            Panel2.Visible = True
            SQL = "SELECT    A.APC_TAHUN AS TAHUN, A.APC_TKH AS TARIKH, P.NAMA AS 'PEMOHON', C.PEMPROSES_TKH AS 'TARIKH PEMOHONAN', C.JUSTIFIKASI FROM JT_PENUH R INNER JOIN JT_PENUH_APC A ON(R.J_DAFTAR = A.J_DAFTAR) AND (R.NOKP = A.NOKP) INNER JOIN PN_RON_BATAL C ON ( A.ID_BATAL = C.ID_BATAL) INNER JOIN PN_PENGGUNA P ON (C.PEMPROSES_ID = P.ID_PG) WHERE R.NOKP = '" & Session("NOKP") & "' AND C.ID_BATAL = '" & Session("RET_ID") & "'  ORDER BY 'TARIKH PEMOHONAN' DESC "
            List_Adp.SelectCommand.CommandText = SQL
            List_Adp.Fill(List_Data, "ret_cancel")
            Gd_RET.DataSource = List_Data.Tables("ret_cancel")
        End If
        Gd_RET.DataBind()

        Tx_Justifikasi.Text = Gd_RET.Rows.Item(0).Cells(6).Text.Trim : Tx_Justifikasi.Enabled = False
        Tx_Nama.Enabled = False : Tx_NoKP.Enabled = False : Tx_NoPd.Enabled = False

    End Sub


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P4", "Batal", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PENGEKALAN NAMA"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        Tx_Nama.Text = Session("NAMA")
        Tx_NoKP.Text = Session("NOKP")
        Tx_NoPd.Text = Session("NOPD")

        'If Tx_NoPd.Text = "(BELUM BERDAFTAR)" Then
        '    Panel1.Visible = True
        'End If

        Isi_RET()
    End Sub

    'Protected Sub cmd_Simpan_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Simpan.Click
    '    If Tx_NoPd.Text = "(BELUM BERDAFTAR)" Then
    '        Daftar_TPC()
    '        Exit Sub
    '    End If

    '    If Tx_Tkh_Mohon.Text = "" Then Tx_Tkh_Mohon.Focus() : Exit Sub
    '    If Tx_Tkh.Text = "" Then Tx_Tkh.Focus() : Exit Sub
    '    If Tx_NoResit.Text = "" Then Tx_NoResit.Focus() : Exit Sub
    '    If Tx_TkhResit.Text = "" Then Tx_TkhResit.Focus() : Exit Sub
    '    If Not IsNumeric(Tx_Amaun.Text) Then Tx_Amaun.Focus() : Exit Sub

    '    UICulture = "en-GB"
    '    Culture = "en-GB"

    '    Dim SQL As String = ""
    '    Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
    '    Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

    '    SQL += "insert jt_tpc_tpc (j_daftar, nokp, tpc_tahun, tpc_tkh_mohon, tpc_tkh, tpc_tkhresit, tpc_noresit, tpc_amaun, tpc_lwt_noresit, tpc_lwt_tkhresit, tpc_lwt_amaun, id_amalan, log_id, log_tkh) select " & _
    '                "" & Session("DAFTAR") & "," & _
    '                "'" & Tx_NoKP.Text.Trim & "'," & _
    '                "year(getdate())," & _
    '                "" & Chk_Tkh(Tx_Tkh_Mohon.Text) & "," & _
    '                "" & Chk_Tkh(Tx_Tkh.Text) & "," & _
    '                "" & Chk_Tkh(Tx_TkhResit.Text) & "," & _
    '                "'" & Tx_NoResit.Text & "'," & _
    '                "" & CInt(Tx_Amaun.Text) & "," & _
    '                "null," & _
    '                "null," & _
    '                "null," & _
    '                "" & CInt(Gd.Rows.Item(Gd.Rows.Count - 1).Cells(4).Text.Trim) & "," & _
    '                "'" & Session("Id_PG") & "', getdate(); "
    '    'SQL += "update pn_noapc set tpc=tpc+1 where tahun=year(getdate()); "

    '    If CStr(Gd.Rows.Item(Gd.Rows.Count - 1).Cells(12).Text.Trim) = "&nbsp;" Then
    '    Else
    '        SQL += "update jt_tpc_tpc set tpc_tmt=" & Chk_Tkh(CStr(Gd.Rows.Item(Gd.Rows.Count - 1).Cells(12).Text.Trim)) & " where nokp='" & Tx_NoKP.Text.Trim & "' and tpc_tkh=" & Chk_Tkh(Tx_Tkh.Text) & "; "
    '    End If

    '    Try
    '        Cmd.CommandText = SQL
    '        Cmd.ExecuteNonQuery()
    '        Cn.Close()
    '        'cmd_Simpan.Visible = False
    '        'Isi_TPC()
    '        Session("Msg_Tajuk") = "Pembaharuan TPC"
    '        Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
    '        Response.Redirect("p0_Mesej.aspx", False)
    '        'Msg(Me, "Rekod Telah Dikemaskini...")
    '    Catch ex As Exception
    '        Cn.Close()
    '        Session("Msg_Tajuk") = "Pembaharuan TPC"
    '        Session("Msg_Isi") = ex.Message
    '        Response.Redirect("p0_Mesej.aspx")
    '    End Try
    'End Sub

    'Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
    '    'e.Row.Cells(0).Width = Unit.Pixel(50)
    '    'e.Row.Cells(5).Width = Unit.Pixel(50)
    '    'e.Row.Cells(0).Visible = False
    '    'e.Row.Cells(2).Visible = False
    '    e.Row.Cells(3).Visible = False
    '    e.Row.Cells(4).Visible = False
    '    e.Row.Cells(5).Visible = False
    '    e.Row.Cells(8).Visible = False
    '    e.Row.Cells(12).Visible = False
    '    If e.Row.RowIndex = -1 Then Exit Sub
    '    e.Row.Height = Unit.Pixel(15)
    '    e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
    '    e.Row.Cells(6).Font.Name = "Webdings"
    'End Sub

    'Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
    '    If e.Row.RowIndex = -1 Then Exit Sub
    '    If e.Row.Cells(5).Text = "TAMAT" Then e.Row.ForeColor = Drawing.Color.Firebrick
    'End Sub

    'Protected Sub cmd_Simpan0_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Simpan0.Click
    '    Dim SQL As String = ""
    '    Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
    '    Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
    '    SQL = "delete from jt_tpc_tpc_ss where nokp = '" & Tx_NoKP.Text.Trim & "'"
    '    SQL += "insert jt_tpc_tpc_ss (nokp, ss1, ss2 , ss3, ss4, ss5, ss6, ss7, ss8) values (" & _
    '            "'" & Tx_NoKP.Text.Trim & "'," & _
    '            "" & SSemak(0) & "," & _
    '            "" & SSemak(1) & "," & _
    '            "" & SSemak(2) & "," & _
    '            "" & SSemak(3) & "," & _
    '            "" & SSemak(4) & "," & _
    '            "" & SSemak(5) & "," & _
    '            "" & SSemak(6) & "," & _
    '            "" & SSemak(7) & ")"
    '    Try
    '        Cmd.CommandText = SQL
    '        Cmd.ExecuteNonQuery()
    '        Msg(Me, "Senarai Semak Telah Dikemaskini..")
    '    Catch ex As Exception
    '        Msg(Me, "Error!")
    '    End Try
    'End Sub

    Private Sub Gd_RET_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd_RET.RowCreated
        e.Row.Cells(1).Width = Unit.Pixel(50)
        e.Row.Cells(6).Visible = False 'reason
        e.Row.Cells(1).HorizontalAlign = HorizontalAlign.Center
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Private Sub Gd_RET_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd_RET.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
    End Sub

    Protected Sub cmd_Simpan_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Simpan.Click

        Dim SQL As String = ""
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

       
        '    If CStr(Gd.Rows.Item(Gd.Rows.Count - 1).Cells(12).Text.Trim) = "&nbsp;" Then
        '    Else
        '        SQL += "update jt_tpc_tpc set tpc_tmt=" & Chk_Tkh(CStr(Gd.Rows.Item(Gd.Rows.Count - 1).Cells(12).Text.Trim)) & " where nokp='" & Tx_NoKP.Text.Trim & "' and tpc_tkh=" & Chk_Tkh(Tx_Tkh.Text) & "; "
        '    End If

     
        If Tx_Ulasan.Text <> "" Then
            If Cb_Status.SelectedValue = "L" Then

                SQL += "update pn_ron_batal set status = 'L',  ulasan = '" & Tx_Justifikasi.Text.Trim & "', log_id = '" & Session("id_pg") & "', log_tkh = getdate() where id_batal = '" & Session("ret_id") & "'" & vbCrLf & ""
                SQL += "update jt_penuh_apc set apc_batal = 1 where id_batal = '" & Session("ret_id") & "'"

            ElseIf Cb_Status.SelectedValue = "R" Then
                SQL += "insert into ron_penuh_tolak (nokp,j_daftar,apc_no, apc_tahun, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_tkhresit, apc_lwt_noresit, apc_lwt_amaun, id_amalan,ret,ret_negara, ret_sebab, ret_catatan, log_id, log_tkh, bil_apc, bless_submit_no, bless_date, apc_tarikh_mula, apc_tarikh_tamat, non_ap, apc_batal, id_batal)" & _
                       "Select nokp,j_daftar,apc_no, apc_tahun, apc_tkh, apc_tkhresit, apc_noresit, apc_amaun, apc_lwt_tkhresit, apc_lwt_noresit, apc_lwt_amaun, id_amalan,ret,ret_negara, ret_sebab, ret_catatan, log_id, log_tkh, bil_apc, bless_submit_no, bless_date, apc_tarikh_mula, apc_tarikh_tamat, non_ap, apc_batal, id_batal from jt_penuh_apc " & _
                       "where id_batal = '" & Session("ret_id") & "'" & vbCrLf & ""
                SQL += "update jt_penuh_apc set id_batal = null where id_batal = '" & Session("ret_id") & "'" & vbCrLf & " "
                SQL += "update pn_ron_batal set status = 'R', ulasan = '" & Tx_Justifikasi.Text.Trim & "', log_id = '" & Session("id_pg") & "', log_tkh = getdate() where id_batal = '" & Session("ret_id") & "'" & vbCrLf & ""

            Else
                Msg(Me, "Sila pilih Lulus / Tolak !")
            End If
        Else
            Msg(Me, "Sila Masukan Ulasan !")
        End If

        If SQL = "" Then
            Exit Sub
        End If

        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Cn.Close()
            'cmd_Simpan.Visible = False
            'Isi_TPC()
            Session("Msg_Tajuk") = "Pembatalan Pengekalan Nama"
            Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
            Response.Redirect("p0_Mesej.aspx", False)
        Catch ex As Exception
            Cn.Close()
            'Session("Msg_Tajuk") = "Pembatalan Pengekalan Nama"
            'Session("Msg_Isi") = ex.Message
            'Response.Redirect("p0_Mesej.aspx")
        End Try

    End Sub
End Class