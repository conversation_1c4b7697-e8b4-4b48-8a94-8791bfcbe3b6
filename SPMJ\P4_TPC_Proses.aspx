﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P4_TPC_Proses.aspx.vb" Inherits="SPMJ.WebForm27" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style3
        {
            height: 23px;
            width: 600px;
        }
        .style4
        {
            width: 600px;
        }
        </style>
<script type="text/javascript">
<!--
window.history.forward(1);
// -->
</script>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%" 
            style="font-variant: small-caps">
        <tr>
            <td width="=" rowspan="24">&nbsp;</td>
            <td class="style3"></td>
            <td rowspan="24">&nbsp;</td>
        </tr>
        <tr>
            <td class="style4">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                </td>
        </tr>
        <tr>
            <td class="style3" align="center" bgcolor="#336699" 
                
                style="font-family: Arial; font-size: 8pt; font-weight: bolder; color: #FFFFFF;">
                proses pembaharuan tpc</td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF">
                &nbsp;</td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" 
                            ReadOnly="True">NAMA</asp:TextBox>
                <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="190px" 
                    Wrap="False" ReadOnly="True"></asp:TextBox>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" 
                            ReadOnly="True">NO. KP/PASPORT</asp:TextBox>
                                                 <asp:TextBox ID="Tx_NoKP" runat="server" 
                    CssClass="std" Width="190px" 
                                                     Wrap="False" ReadOnly="True"></asp:TextBox>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj1" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" 
                            ReadOnly="True">NO. PENDAFTARAN</asp:TextBox>
                                                 <asp:TextBox ID="Tx_NoPd" runat="server" 
                    CssClass="std" Width="190px" 
                                                     Wrap="False" ReadOnly="True"></asp:TextBox>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF">
                &nbsp;</td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
        <table style="width: 522px" align="center"><tr><td>
            <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="100%" GridLines="Horizontal" BorderColor="#336699">
                    <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                    <RowStyle BackColor="White" Height="21px" />
                    <Columns>
                        <asp:TemplateField HeaderText="#">
                            <HeaderStyle HorizontalAlign="Center" />
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                    </Columns>
                    <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                    <SelectedRowStyle Font-Bold="False" />
                    <HeaderStyle BackColor="#528BC5" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                    <AlternatingRowStyle BackColor="White" />
                </asp:GridView></td></tr><tr><td>
                &nbsp;</td></tr></table></td>
        </tr>
        <tr>
            <td class="style4" 
                
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; border-top-style: solid; border-top-width: 1px;">
                &nbsp;&nbsp;</td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
        <table style="width: 522px" align="center" cellpadding="-1" cellspacing="-1">
            <tr>
                        <td bgcolor="#999966" 
                            
                    
                            style="border: 1px solid #999966; color: #FFFFFF; font-weight: bold; font-family: Arial; font-size: 8pt; font-variant: small-caps;" 
                            height="15">
                                                        &nbsp; Senarai Semak </td>
                        </tr>
            <tr><td style="border-color: #999966; border-right-width: 1px; border-left-width: 1px; border-right-style: solid; border-left-style: solid; border-bottom-style: solid; border-bottom-width: 1px;">
                                    <asp:CheckBoxList ID="chk_Semak" runat="server" Font-Names="Arial" 
                                Font-Size="8pt" RepeatColumns="2" 
                                Width="100%" Height="31px">
                                <asp:ListItem>Borang Permohonan</asp:ListItem>
                                        <asp:ListItem>Foto</asp:ListItem>
                                        <asp:ListItem>Salinan Pasport</asp:ListItem>
                                        <asp:ListItem>Bayaran Pendaftaran</asp:ListItem>
                                        <asp:ListItem>Salinan Multiple Entry Visa dari Imigresen</asp:ListItem>
                                        <asp:ListItem>Salinan Kontrak (Untuk Permohonan Kali Pertama)</asp:ListItem>
                                        <asp:ListItem>Laporan Penilaian Majikan (Pembaharuan sahaja)</asp:ListItem>
                                        <asp:ListItem>Salinan Expatriate Identification dari Imigresen (Pengajar)</asp:ListItem>
                                    </asp:CheckBoxList></td></tr></table></td>
        </tr>
        <tr>
            <td class="style4" 
                
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; font-family: Arial, Helvetica, sans-serif; font-size: 3px;" 
                align="center">
                <br />
                <asp:Button ID="cmd_Simpan0" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="KEMASKINI SENARAI SEMAK" Width="170px" />
                </td>
        </tr>
        <tr>
            <td class="style4" 
                
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; border-bottom-style: solid; border-bottom-width: 1px;">
                &nbsp;</td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;</td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                <asp:Panel ID="Panel1" runat="server" Visible="False">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
                    <asp:TextBox ID="Cb_Sbj13" 
    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px">NO. TPC</asp:TextBox>
                    <asp:TextBox ID="Tx_NoTPC" runat="server" 
                    CssClass="std" Width="95px"></asp:TextBox>
                </asp:Panel>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj12" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px">TARIKH PERMOHONAN</asp:TextBox>
                                                <asp:TextBox ID="Tx_Tkh_Mohon" runat="server" 
                    CssClass="std" Width="95px"></asp:TextBox>
                                                <cc1:MaskedEditExtender ID="Tx_Tkh_Mohon_MaskedEditExtender" runat="server" 
                                                    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                    CultureDateFormat="" 
                    CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                    CultureName="en-GB" 
                    CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                    Enabled="True" Mask="99/99/9999" MaskType="Date" 
                                                    TargetControlID="Tx_Tkh_Mohon" 
                    UserDateFormat="DayMonthYear">
                                                </cc1:MaskedEditExtender>
                                                <cc1:CalendarExtender ID="Tx_Tkh_Mohon_CalendarExtender" runat="server" 
                                                    Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                    TargetControlID="Tx_Tkh_Mohon">
                                                </cc1:CalendarExtender>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj11" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" 
                            ReadOnly="True">TARIKH TPC</asp:TextBox>
                                                <asp:TextBox ID="Tx_Tkh" runat="server" 
                    CssClass="std" Width="95px"></asp:TextBox>
                                                <cc1:MaskedEditExtender ID="Tx_Tkh_MaskedEditExtender" runat="server" 
                                                    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                    CultureDateFormat="" 
                    CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                    CultureName="en-GB" 
                    CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                    Enabled="True" Mask="99/99/9999" MaskType="Date" 
                                                    TargetControlID="Tx_Tkh" 
                    UserDateFormat="DayMonthYear">
                                                </cc1:MaskedEditExtender>
                                                <cc1:CalendarExtender ID="Tx_Tkh_CalendarExtender" runat="server" 
                                                    Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                    TargetControlID="Tx_Tkh">
                                                </cc1:CalendarExtender>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj7" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" 
                            ReadOnly="True">NO. RESIT</asp:TextBox>
                <asp:TextBox ID="Tx_NoResit" runat="server" CssClass="std" Width="95px" 
                    Wrap="False"></asp:TextBox>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj8" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" 
                            ReadOnly="True">TARIKH RESIT</asp:TextBox>
                                                <asp:TextBox ID="Tx_TkhResit" runat="server" 
                    CssClass="std" Width="95px"></asp:TextBox>
                                                <cc1:MaskedEditExtender ID="Tx_TkhResit_MaskedEditExtender" runat="server" 
                                                    CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                                                    CultureDateFormat="" 
                    CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                                                    CultureName="en-GB" 
                    CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                                                    Enabled="True" Mask="99/99/9999" MaskType="Date" 
                                                    TargetControlID="Tx_TkhResit" 
                    UserDateFormat="DayMonthYear">
                                                </cc1:MaskedEditExtender>
                                                <cc1:CalendarExtender ID="Tx_TkhResit_CalendarExtender" runat="server" 
                                                    Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                    TargetControlID="Tx_TkhResit">
                                                </cc1:CalendarExtender>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj9" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" 
                            ReadOnly="True">AMAUN</asp:TextBox>
                <asp:TextBox ID="Tx_Amaun" runat="server" CssClass="std" Width="95px" 
                    Wrap="False">70</asp:TextBox>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;</td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj10" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px"></asp:TextBox>
                <asp:Button ID="cmd_Simpan" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SIMPAN" Width="80px" />
                </td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; border-bottom-style: solid; border-bottom-width: 1px;">
                &nbsp;</td>
        </tr>
    
    
        <tr>
            <td class="style4">
                <br />
            </td>
        </tr>
    
    
    </table>
    
    
    </div></asp:Content>
