﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="LP_STAT_XM_Markah.aspx.vb" Inherits="SPMJ.LP_STAT_XM_Markah" 
    title="Untitled Page" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style2
        {
            height: 7px;
        }
        .style7
        {
            width: 730px;
        }
        .style9
        {
            height: 7px;
            width: 730px;
        }
        .style12
        {
            height: 37px;
        }
        .style13
        {
            width: 730px;
            height: 37px;
        }
        .style14
        {
            height: 41px;
        }
        .style15
        {
            width: 730px;
            height: 41px;
        }
        .style16
        {
            width: 173px;
        }
        .style17
        {
            height: 41px;
            width: 173px;
        }
        .style18
        {
            height: 7px;
            width: 173px;
        }
        .style20
        {
            height: 37px;
            width: 173px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td class="style16"></td>
            <td class="style7"></td>
            <td></td>
        </tr>
        <tr>
            <td class="style16">&nbsp;</td>
            <td class="style7">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style17"></td>
            <td align="center" 
                style="border-style: solid; border-width: 1px; border-color: #000000 #000000 #808055 #000000; font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold; background-color: #999966;" 
                bgcolor="#EEEEEE" class="style15">Statistik markah peperiksaan </td>
            <td class="style14"></td>
        </tr>
        <tr>
            <td class="style16"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style7">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style18">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style9" align="left">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj10" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="145px" Enabled="False" 
                            ReadOnly="True">KATEGORI</asp:TextBox>
                        <asp:DropDownList ID="Cb_Kategori" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="250px" AutoPostBack="True">
                        </asp:DropDownList>
                    </td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style18">&nbsp;</td>
            <td                 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style9" align="left">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="TextBox3" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="145px" Enabled="False" 
                            ReadOnly="True">TEMPAT LATIHAN</asp:TextBox>
                        <asp:DropDownList ID="Cb_Kolej" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="520px" >
                        </asp:DropDownList>
                    </td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style18">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style9" align="left">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="TextBox1" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="145px" Enabled="False" 
                            ReadOnly="True">JENIS KURSUS</asp:TextBox>
                        <asp:DropDownList ID="Cb_Kursus" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="250px" >
                        </asp:DropDownList>
                    </td>
            <td class="style2">&nbsp;</td>
        </tr>         
        <tr>
            <td class="style18">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style9" align="left">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="TextBox2" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="145px" Enabled="False" 
                            ReadOnly="True">TAHUN & SIRI PEPERIKSAAN</asp:TextBox>
                        <asp:TextBox ID="Tx_Tahun" runat="server" CssClass="std" Width="80px" 
                            Wrap="False"></asp:TextBox>
                        <asp:TextBox ID="TextBox4" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="10px">&amp;</asp:TextBox>
                        <asp:TextBox ID="Tx_Siri" runat="server" CssClass="std" Width="80px" 
                            Wrap="False"></asp:TextBox>
                    </td>
            <td class="style2">&nbsp;</td>
        </tr>               
        <tr>
            <td class="style16"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style7">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style20"></td>
            <td 
                
                style="border-style: none solid solid solid; border-width: 1px; border-color: #000000 #003300 #003300 #003300; background-color: #D7D7C4;" 
                bgcolor="White" class="style13" align="right">
                &nbsp;&nbsp;<asp:Button ID="cmd_Cari" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="JANA" Width="80px" />
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </td>
            <td class="style12"></td>
        </tr>
        <tr>
            <td class="style16"></td>
            <td class="style7">&nbsp;</td>
            <td></td>
        </tr>
    
    
        <tr>
            <td class="style16">&nbsp;</td>
            <td class="style7">
                <br />
                        <br />
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>
    
    
    </table>
    
    
    </div></asp:Content>
