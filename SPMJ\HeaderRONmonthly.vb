﻿Imports iTextSharp.text.pdf
Imports iTextSharp.text
Imports System.Web

Public Class HeaderRONmonthly
    Inherits PdfPageEventHelper

    Public Overrides Sub OnStartPage(ByVal writer As iTextSharp.text.pdf.PdfWriter, ByVal document As iTextSharp.text.Document)

        Dim beginDate As String
        Dim endDate As String


        If Not (HttpContext.Current.Session("BEGIN_date")) Is Nothing And Not (HttpContext.Current.Session("END_date")) Is Nothing Then
            If HttpContext.Current.Session("RP_ID") = "RET1" Then
                'Add dates for report 06072020 -OSH  
                beginDate = HttpContext.Current.Session("BEGIN_date")
                endDate = HttpContext.Current.Session("END_date")

                Dim ch As New Chunk("LAPORAN PENGELUARAN PENGEKALAN NAMA - KATEGEORI PENDAFTARAN ")
                Dim ch2 As New Chunk("DARI TARIKH (" & beginDate.ToString & " SEHINGGA " & endDate.ToString & ")")
                'Add Improve Paragraph - Center 30102019 - OSH  
                Dim ph As New Phrase(ch)
                'Add Phrase 06072020 - OSH 
                Dim ph2 As New Phrase(ch2)
                Dim p As New Paragraph()
                p.Add(ph)
                p.SpacingBefore = 20
                p.SpacingAfter = 20
                p.Alignment = 1 'center
                p.Add(Chunk.NEWLINE)
                p.Add(ph2)
                document.Add(p)

            ElseIf HttpContext.Current.Session("RP_ID") = "RET2" Then

                'Add dates for report 06072020 -OSH  
                beginDate = HttpContext.Current.Session("BEGIN_date")
                endDate = HttpContext.Current.Session("END_date")

                Dim ch As New Chunk("LAPORAN PENGELUARAN PENGEKALAN NAMA - TAHUN PENGEKALAN")
                Dim ch2 As New Chunk("DARI TARIKH (" & beginDate.ToString & " SEHINGGA " & endDate.ToString & ")")
                'Add Improve Paragraph - Center 30102019 - OSH  
                Dim ph As New Phrase(ch)
                'Add Phrase 06072020 - OSH 
                Dim ph2 As New Phrase(ch2)
                Dim p As New Paragraph()
                p.Add(ph)
                p.SpacingBefore = 20
                p.SpacingAfter = 20
                p.Alignment = 1 'center
                p.Add(Chunk.NEWLINE)
                p.Add(ph2)
                document.Add(p)

            End If
        End If
    End Sub
End Class
