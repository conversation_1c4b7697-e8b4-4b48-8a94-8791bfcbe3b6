﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm32
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select top 100 NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', CONVERT(varchar(12), jpn.nn_tkh, 103) + '-31/12/' + CAST(year(getdate()) as varchar(4))  as 'TEMPOH NOTIS NIAT' from jt_penuh jp left outer join jt_penuh_nniat jpn on jp.nokp=jpn.nokp and jpn.nn_tahun=year(getdate()) left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.apc_tahun = year(getdate()) order by nama" 'where jp.j_daftar = " & Cb_Jenis.SelectedIndex & " and " & X & " order by jp.nama"
        Tb = "jt_penuh"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        Gd.DataBind()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(0).Width = Unit.Pixel(30)
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(3).Width = Unit.Pixel(100)
        e.Row.Cells(4).Width = Unit.Pixel(100)
        e.Row.Cells(5).Width = Unit.Pixel(100)
        e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."

    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged

    End Sub
End Class