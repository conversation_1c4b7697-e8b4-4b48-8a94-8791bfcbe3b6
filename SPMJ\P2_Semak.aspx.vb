﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm15
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        If Cb_Status.SelectedIndex = 0 Then
            If Cb_Jenis.SelectedIndex = 0 Then
                'Comment Original 20052022 - OSH 
                'SQL = "select NAMA, NOKP as 'NO. KP/PASPORT', case j_daftar when 1 then 'JB-' + cast(nopd as varchar(6)) when 2 then 'JM-' + cast(nopd as varchar(6)) when 3 then 'PJ-' + cast(nopd as varchar(6)) when 4 then 'B-' + cast(nopd as varchar(6)) end as 'NO. PENDAFTARAN' from jt_penuh where " & X & " order by nama"

                'Add Eduction status 20052022 - OSH 
                SQL = "select NAMA, NOKP as 'NO. KP/PASPORT', case j_daftar when 1 then 'JB-' + cast(nopd as varchar(6)) when 2 then 'JM-' + cast(nopd as varchar(6)) when 3 then 'PJ-' + cast(nopd as varchar(6)) when 4 then 'B-' + cast(nopd as varchar(6)) end as 'NO. PENDAFTARAN', xm_jenis as 'KELULUSAN' from jt_penuh where " & X & " order by nama"

            Else
                'Comment Original 20052022 - OSH 
                'SQL = "select NAMA, NOKP as 'NO. KP/PASPORT', case j_daftar when 1 then 'JB-' + cast(nopd as varchar(6)) when 2 then 'JM-' + cast(nopd as varchar(6)) when 3 then 'PJ-' + cast(nopd as varchar(6)) when 4 then 'B-' + cast(nopd as varchar(6)) end as 'NO. PENDAFTARAN' from jt_penuh where j_daftar = " & Cb_Jenis.SelectedIndex & " and " & X & " order by nama"

                SQL = "select NAMA, NOKP as 'NO. KP/PASPORT', case j_daftar when 1 then 'JB-' + cast(nopd as varchar(6)) when 2 then 'JM-' + cast(nopd as varchar(6)) when 3 then 'PJ-' + cast(nopd as varchar(6)) when 4 then 'B-' + cast(nopd as varchar(6)) end as 'NO. PENDAFTARAN', xm_jenis as 'KELULUSAN' from jt_penuh where j_daftar = " & Cb_Jenis.SelectedIndex & " and " & X & " order by nama"
            End If
            Tb = "jt_penuh"
        Else
            If Cb_Jenis.SelectedIndex = 0 Then
                SQL = "select NAMA, NOKP as 'NO. KP/PASPORT' from tmp_penuh where " & X & " order by nama"
            Else
                SQL = "select NAMA, NOKP as 'NO. KP/PASPORT' from tmp_penuh where j_daftar = " & Cb_Jenis.SelectedIndex & " and " & X & " order by nama"
            End If
            Tb = "tmp_penuh"
        End If

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If IsPostBack Then Exit Sub
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        'e.Row.Cells(1).Visible = False
        e.Row.Cells(0).Width = Unit.Pixel(30)
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Session("NOKP") = Gd.SelectedRow.Cells(3).Text
        Session("PINDA") = True
        If Cb_Status.SelectedIndex = 0 Then
            Response.Redirect("p2_penuh.aspx")
        Else
            Response.Redirect("p2_daftar.aspx")
        End If
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        'If Cb_Jenis.SelectedIndex < 1 Then Exit Sub
        If Tx_Nama.Text.Trim = "" And Tx_NoKP.Text.Trim = "" And Tx_NoPd.Text.Trim = "" Then Exit Sub
        If Cb_Status.SelectedIndex = 0 Then
            'Fix Remove Trim 30062022 -OSH
            Cari("nama like '" & Tx_Nama.Text.Trim & "%' and nokp like '" & Tx_NoKP.Text & "%' and nopd like '" & Tx_NoPd.Text.Trim & "%'")
            'Comment Original 30062022 - OSH 
            'Cari("nama like '" & Tx_Nama.Text & "%' and nokp like '" & Tx_NoKP.Text & "%' and nopd like '" & Tx_NoPd.Text & "%'")
            'Cari(" nopd like '" & Tx_NoPd.Text & "%'")
        Else
            'Fix Remove Trim 30062022 -OSH 
            Cari("nama like '" & Tx_Nama.Text.Trim & "%' and nokp like '" & Tx_NoKP.Text.Trim & "%'")
            'Comment Original 30062022 - OSH 
            'Cari("nama like '" & Tx_Nama.Text & "%' and nokp like '" & Tx_NoKP.Text & "%'")
        End If
    End Sub
End Class