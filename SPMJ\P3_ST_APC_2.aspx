﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P3_ST_APC_2.aspx.vb" Inherits="SPMJ.P3_ST_APC_2" 
    title="Statistik Pengeluaran Apc" %>
 <%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 22px;
        }
    .style2
    {
        width: 598px;
        height: 40px;
    }
    .style3
    {
        height: 22px;
        width: 975px;
    }
    .style4
    {
            width: 975px;
        }
    .style5
    {
        width: 975px;
        height: 40px;
    }
        .style6
        {
            height: 33px;
        }
        .style7
        {
            width: 975px;
            height: 33px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
        <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td>&nbsp;</td>
            <td class="style4">
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style1"></td>
            <td align="center" 
                style="border-width: 1px; border-color: #000000; font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold; border-right-style: solid; border-left-style: solid; border-top-style: solid;" 
                bgcolor="#719548" class="style3">Statistik Pengeluaran Apc</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style4">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; vertical-align: middle;" 
                bgcolor="White" class="style4">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj17" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="18px" tabIndex="36" Width="94px" Enabled="False" 
                            ReadOnly="True">TARIKH PROSES</asp:TextBox>&nbsp;<asp:TextBox 
                            ID="Tx_Tkh_M" runat="server" CssClass="std" 
            Width="80px"></asp:TextBox><cc1:MaskedEditExtender ID="Tx_Tkh_M_MaskedEditExtender" runat="server" 
                            CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                            CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                            CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                            Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_M" 
                            UserDateFormat="DayMonthYear">
                        </cc1:MaskedEditExtender>
                        <cc1:CalendarExtender ID="Tx_Tkh_M_CalendarExtender" runat="server" 
                            Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                            TargetControlID="Tx_Tkh_M">
                        </cc1:CalendarExtender>
                        <asp:TextBox ID="Cb_Sbj16" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="10px">-</asp:TextBox>
                        <asp:TextBox ID="Tx_Tkh_A" runat="server" CssClass="std" 
            Width="80px"></asp:TextBox>
                        &nbsp;
                        <cc1:MaskedEditExtender ID="Tx_Tkh_A_MaskedEditExtender" runat="server" 
                            CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                            CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                            CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                            Enabled="True" Mask="99/99/9999" MaskType="Date" 
                            TargetControlID="Tx_Tkh_A" UserDateFormat="DayMonthYear">
                        </cc1:MaskedEditExtender>
                        <cc1:CalendarExtender ID="Tx_Tkh_A_CalendarExtender" runat="server" 
                            Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                            TargetControlID="Tx_Tkh_A">
                        </cc1:CalendarExtender>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; vertical-align: middle;" 
                bgcolor="White" class="style4">
                        &nbsp;&nbsp;
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj18" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="18px" tabIndex="36" Width="94px" Enabled="False" 
                            ReadOnly="True">TAHUN APC</asp:TextBox>
                        <asp:DropDownList ID="Cb_Tahun" 
                            runat="server" CssClass="std" 
                                                                Font-Names="Arial" Font-Size="8pt" 
                            Height="19px" style="margin-left: 0px" 
                                                                Width="200px">
                                                            </asp:DropDownList>
            </td>
            <td align="center"></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; vertical-align: middle;" 
                bgcolor="White" class="style4">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj22" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="18px" tabIndex="36" Width="94px" Enabled="False" 
                            ReadOnly="True">PENGGUNA</asp:TextBox>
                        <asp:DropDownList ID="Cb_Pengguna" 
                            runat="server" CssClass="std" 
                                                                Font-Names="Arial" Font-Size="8pt" 
                            Height="19px" style="margin-left: 0px" 
                                                                Width="200px">
                                                            </asp:DropDownList>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td class="style6"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; vertical-align: middle;" 
                bgcolor="White" class="style7">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;<asp:TextBox ID="Cb_Sbj21" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="18px" tabIndex="36" Width="74px"></asp:TextBox><asp:Button 
                    ID="cmd_Cari2" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CETAK" Width="80px" Visible="False" />
            &nbsp;<asp:Button ID="cmd_Cari1" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="JANA" Width="80px" />
            </td>
            <td align="center" class="style6"></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; border-bottom-style: solid; border-bottom-width: 1px;" 
                bgcolor="White" class="style4">
                        &nbsp;</td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td class="style2"></td>
            <td 
                bgcolor="White" class="style5">
                        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="100%" GridLines="Horizontal" BorderColor="#719548">
                            <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                            <RowStyle BackColor="#F4F4F4" Height="21px" />
                            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                            <SelectedRowStyle Font-Bold="False" />
                            <HeaderStyle BackColor="#7EA851" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                            <AlternatingRowStyle BackColor="White" />
                        </asp:GridView>
    
            </td>
            <td class="style2"></td>
        </tr>
    
    
    </table>
    </div> 
</asp:Content>
