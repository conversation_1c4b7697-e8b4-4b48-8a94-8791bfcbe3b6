﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm45
    Inherits System.Web.UI.Page

    Public Sub Notis_Niat()
        'Put user code to initialize the page here
        Dim x As String
        'Dim s As String = "http://localhost:4844/surat/"
        'Dim s As String = "http://************/spmj/surat/"

        x = "<html xmlns:v='urn:schemas-microsoft-com:vml' xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word'>"
        x += "<head><style type='text/css'><!-- .indent_1x { padding-left: 100pt;padding-right: 50pt;} --></style>"
        x += "<style type='text/css'><!-- .indent_3x { padding-left: 400pt;padding-right: 50pt;} --></style>"
        x += "<style> "
        x += "@page Section1 {mso-footer:f1;}"
        x += "div.Section1{page:Section1;}"
        x += "p.<PERSON><PERSON>, li.<PERSON>, div.<PERSON><PERSON><PERSON><PERSON>er{"
        x += "mso-pagination:widow-orphan;"
        x += "tab-stops:center 216.0pt right 432.0pt;}"
        x += "</style>"
        x += "</head><body><div class='Section1'>"

        'Header

        'x += "<table width='100%' style='font-family: Arial; font-size: 9px;background-color:#cfcfcf;'><tr>"
        x += "<table width='100%' style='border: none;mso-border-bottom-alt:solid windowtext .5pt;margin-left: 0px; font-family: Arial; font-size: 9pt;'><tr>"
        x += "<td><img width=110 height=88 src='" & s & "jata.png'></img></td>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 9pt; color:blue;'>"
        x += "<b>LEMBAGA BIDAN MALAYSIA</b>"
        x += "<br/><i>MIDWIVES BOARD MALAYSIA</i>"
        x += "<br/><b>KEMENTERIAN KESIHATAN MALAYSIA</b>"
        x += "<br/><i>MINISTRY OF HEALTH MALAYSIA</i>"
        x += "<br/><b>Aras 3, Blok E1, Kompleks E, Presint 1</b>"
        x += "<br/><i>Level 3, Block E1, Parcel E, Precinct 1</i>"
        x += "<br/><b>Pusat Pentadbiran Kerajaan Persekutuan</b>"
        x += "<br/><i>Federal Government Administrative Centre</i>"
        x += "<br/><b>62590 Putrajaya</b></p></div></td>"
        x += "<td style='width:21.64%;border: none; margin-left: 0px; font-family: Arial; font-size: 9pt;'>"
        x += "<div align ='center'><img width=100 height=100 src='" & s & "bidan.jpg'></img>"
        'x += "<v:shape ><v:imagedata src='" & s & "ljm2.gif'/>"
        x += "<br/>TEL :603-88831339"
        x += "<br/>FAX :603-88831329 </div>"
        x += "</td>"
        x += "</tr></table>"
        ' <div align="center"></div>

        'Rujukan
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 9pt;'><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 9pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 9pt;'> Rujukan Tuan : </td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 9pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 9pt;'> Rujukan Kami : KKM 87/A3/1/158(.........) </td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 9pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 9pt;'> Tarikh : " + Now.ToShortDateString + "</td>"
        x += "</tr></table>"

        'Penerima
        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 9pt;'>(PENERIMA)"
        x += "<br/>" & Cb_Tpt_Amalan.SelectedItem.Text
        x += "<br/>" & Tx_Amalan_Alamat.Text
        If Tx_Amalan_Bandar.Text = "" Then
            x += "<br/>" & Tx_Amalan_Poskod.Text & " " & Tx_Amalan_Negeri.Text
        Else
            x += "<br/>" & Tx_Amalan_Poskod.Text
            x += "<br/>" & Tx_Amalan_Bandar.Text
            x += "<br/>" & Tx_Amalan_Negeri.Text
        End If
        x += "</div>"

        x += "<br/>"
        x += "<div  style='font-family: Arial; font-size: 9pt;'>Tuan/Puan,"
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 9pt;'><b>PERMOHONAN NOTIS MENGENAI NIAT UNTUK MENJALANKAN AMALAN ATAU TERUS MENJALANKAN AMALAN KEBIDANAN MENGIKUT AKTA BIDAN 1966 PINDAAN 1990"
        x += "</b></div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 9pt;'>Dengan segala hormatnya merujuk perkara di atas dan surat Tuan bertarikh ............ adalah berkaitan."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 9pt;'>"
        x += "2. &nbsp; &nbsp; &nbsp; &nbsp; Sukacita dimaklumkan Lembaga Bidan Malaysia telah <b> meluluskan </b> Pengamalan Kebidanan bagi tempoh " + Tx_TkhResit.Text + " hingga 31/12/" + Format(CDate(Tx_TkhResit.Text), "yyyy") + " untuk Pegawai berikut :-"
        x += "</div>"

        'SENARAI JURURAWAT
        x += "<br/>"
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 9pt;'>"
        x += "<tr>"
        x += "<td style='width:5%;border: none; margin-left: 0px;'></td>"
        x += "<td style='text-decoration: underline; font-weight: bold; border: none; margin-left: 0px;'>Nama Jururawat</td>"
        x += "<td style='text-decoration: underline; font-weight: bold; border: none; margin-left: 0px;'>No. Pendaftaran</td>"
        x += "<td style='text-decoration: underline; font-weight: bold; border: none; margin-left: 0px;'>Kebidanan</td>"
        x += "</tr>"

        Dim i As Int16, Bahagian As String = ""
        For i = 0 To Gd.Rows.Count - 1
            If Left(Gd.Rows.Item(i).Cells(3).Text, 2) = "JB" Then Bahagian = "Bahagian I"
            If Left(Gd.Rows.Item(i).Cells(3).Text, 2) = "JM" Then Bahagian = "Bahagian III"
            If Left(Gd.Rows.Item(i).Cells(3).Text, 2) = "B" Then Bahagian = "Bahagian II"
            x += "<tr>"
            If Gd.Rows.Count > 1 Then
                x += "<td style='text-align: right;'>" & i + 1 & ".</td>"
            Else
                x += "<td></td>"
            End If
            x += "<td>" + Gd.Rows.Item(i).Cells(1).Text + "</td>"
            x += "<td>" + Gd.Rows.Item(i).Cells(3).Text + "</td>"
            x += "<td>" + Bahagian + "</td>"
            x += "</tr>"
        Next

        x += "</table>"


        x += "<br/><div style='font-family: Arial; font-size: 9pt;'>"
        x += "Sekian, terima kasih."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 9pt;'>"
        x += "<b> &quot;BERKHIDMAT UNTUK NEGARA&quot; </b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 9pt;'>"
        x += "Saya yang menurut perintah,"
        x += "</div>"


        x += "<br>"
        x += "<br>"
        x += "</br>"


        x += "<br/><div style='font-family: Arial; font-size: 9pt;'>"
        x += "<b> (DATO' HJH. FATHILAH BINTI HJ.ABD. WAHAB) </b>"
        x += "<br>Setiausaha,"
        x += "<br>Lembaga Bidan Malaysia,"
        x += "<br>Kementerian Kesihatan Malaysia."

        x += "<br/><div style='font-family: Arial; font-size: 9pt;'>"
        x += "<br> s.k Cawangan Kawalan Amalan Perubatan Swasta"
        x += "<br>Kementerian Kesihatan Malaysia"
        x += "<br>Putrajaya."
        x += "</div>"

        x += "<br clear=all style='page-break-before:always'>"
        'x += "<br clear=all style='page-break-before:always'>"
        x += "<div style='mso-element:footer' id='f1'>"
        'x += "<p class='MsoFooter'>"
        'x += "<span><img src='" & s & "img1.png'><img src='" & s & "img2.png'><img src='" & s & "img3.jpg'><img src='" & s & "img4.png'></span></p>"
        'x += "<p class=MsoFooter style='border:none;mso-border-top-alt:solid windowtext 0.5pt;"
        'x += "padding:0in;mso-padding-alt:0in 0in 1.0pt 0in'><span style='mso-no-proof:yes'>"
        'x += " <!--[if gte vml 1]>"
        'x += "<v:shape style='position:absolute;left:0;text-align:left;margin-left:315pt;margin-top:3.75pt;width:48.6pt;"
        'x += " height:44.2pt;z-index:-2' wrapcoords='-257 0 -257 21300 21600 21300 21600 0 -257 0'>"
        'x += " <v:imagedata src='" & s & "imgd.png' o:title=''/>"
        'x += "</v:shape><![endif]--><!--[if gte vml 1]><v:shape id='_x0000_s2049' type='#_x0000_t75'"
        'x += " style='position:absolute;left:0;text-align:left;margin-left:178.2pt;"
        'x += " margin-top:5.75pt;width:55.2pt;height:44.2pt;z-index:-4' wrapcoords='-237 0 -237 21375 21600 21375 21600 0 -237 0'"
        'x += " o:allowoverlap='f'>"
        'x += " <v:imagedata src='" & s & "imgb.png' o:title=''/>"
        'x += "</v:shape><![endif]--><!--[if gte vml 1]><v:shape id='_x0000_s2050' type='#_x0000_t75'"
        'x += " style='position:absolute;left:0;text-align:left;margin-left:251.4pt;"
        'x += " margin-top:7.35pt;width:49.2pt;height:40.6pt;z-index:-3' wrapcoords='-304 0 -304 21375 21600 21375 21600 0 -304 0'>"
        'x += " <v:imagedata src='" & s & "imgc.gif' o:title=''/>"
        'x += "</v:shape><![endif]--><!--[if gte vml 1]><v:shape id='_x0000_s2052' type='#_x0000_t75'"
        'x += " style='position:absolute;left:0;text-align:left;margin-left:97.8pt;"
        'x += " margin-top:7.35pt;width:60pt;height:44.2pt;z-index:-1' wrapcoords='-225 0 -225 21373 21600 21373 21600 0 -225 0'"
        'x += " o:allowoverlap='f'>"
        'x += " <v:imagedata src='" & s & "imga.png' o:title=''/>"
        'x += "</v:shape><![endif]--></span></p>"

        x += "<p class=MsoFooter style='border:none;mso-border-top-alt:solid windowtext 0.5pt;"
        x += "padding:0in;mso-padding-alt:0in 0in 1.0pt 0in'>"
        x += "<span style='mso-no-proof:yes'>"
        x += " <!--[if gte vml 1]>"
        x += "<v:shape id='_x0000_s2051' type='#_x0000_t75'"
        x += " style='position:absolute;left:0;text-align:left;margin-left:300.6pt;"
        x += "margin-top:5.75pt;width:68.2pt;height:33.8pt;z-index:-2' wrapcoords='-257 0 -257 21300 21600 21300 21600 0 -257 0'>"
        x += " <v:imagedata src='" & s & "imga3.png' o:title=''/>"
        x += "</v:shape><![endif]-->"
        x += "<!--[if gte vml 1]>"
        x += "<v:shape id='_x0000_s2049' type='#_x0000_t75'"
        x += " style='position:absolute;left:0;text-align:left;margin-left:100.2pt;"
        x += " margin-top:5.75pt;width:68.2pt;height:33.2pt;z-index:-4' wrapcoords='-237 0 -237 21375 21600 21375 21600 0 -237 0'"
        x += " o:allowoverlap='f'>"
        x += " <v:imagedata src='" & s & "imga1.png' o:title=''/>"
        x += "</v:shape><![endif]-->"
        x += "<!--[if gte vml 1]><v:shape id='_x0000_s2050' type='#_x0000_t75'"
        x += " style='position:absolute;left:0;text-align:left;margin-left:200.4pt;"
        x += " margin-top:5.75pt;width:68.2pt;height:32.6pt;z-index:-3' wrapcoords='-304 0 -304 21375 21600 21375 21600 0 -304 0'>"
        x += " <v:imagedata src='" & s & "imga2.png' o:title=''/>"
        x += "</v:shape><![endif]-->"
        x += "</span></p>"
        x += "</div>"
        x += "</div>"
        x += "</body>"
        x += "</html>"

        'Response.Write(x)
        'Exit Sub

        Dim fileName As String = "Surat.doc"
        Response.Clear()
        Response.Buffer = True
        ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub


        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cb_Tpt_Amalan.Items.Add("")
        Cmd.CommandText = "select id_amalan, dc_amalan from pn_tpt_amalan order by dc_amalan"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            Cb_Tpt_Amalan.Items.Add(Rdr(1))
            Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = Rdr(0)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        'e.Row.Cells(0).Width = Unit.Pixel(30)
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
        'cmdHantar0.Visible = True
    End Sub

    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        'e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
        cmdCetak.Visible = True
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged

    End Sub

    Protected Sub Cb_Tpt_Amalan_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Tpt_Amalan.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cb_Tpt_Amalan.Items.Add("")
        Cmd.CommandText = "select *, case sektor when 1 then 'KERAJAAN' when 2 then 'SWASTA' else '' end as 'sekt' from pn_tpt_amalan pta inner join pn_negeri png on pta.negeri = png.id_negeri where id_amalan = '" & Cb_Tpt_Amalan.SelectedValue & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            If Not IsDBNull(Rdr("alamat")) Then Tx_Amalan_Alamat.Text = Rdr("alamat")
            If Not IsDBNull(Rdr("poskod")) Then Tx_Amalan_Poskod.Text = Rdr("poskod")
            If Not IsDBNull(Rdr("bandar")) Then Tx_Amalan_Bandar.Text = Rdr("bandar")
            If Not IsDBNull(Rdr("dc_negeri")) Then Tx_Amalan_Negeri.Text = Rdr("dc_negeri")
            If Not IsDBNull(Rdr("tel")) Then Tx_Amalan_Tel.Text = Rdr("tel")
            If Not IsDBNull(Rdr("fax")) Then Tx_Amalan_Fax.Text = Rdr("fax")
            If Not IsDBNull(Rdr("sekt")) Then Tx_Amalan_Sektor.Text = Rdr("sekt")
        End If
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmdHantar0_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar0.Click
        If Cb_Tpt_Amalan.SelectedIndex < 1 Then Cb_Tpt_Amalan.Focus() : Exit Sub
        If Tx_TkhResit.Text = "" Then Tx_TkhResit.Focus() : Exit Sub

        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select jp.NAMA, jpn.nokp as 'No. KP', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) 'NO. APC TAHUN SEMASA', CONVERT(varchar(12), jpn.nn_tkh, 103)  as 'TARIKH NOTIS NIAT'  from jt_penuh_nniat jpn inner join jt_penuh jp on jpn.nokp = jp.nokp left outer join jt_penuh_apc jpa on jpn.nokp = jpa.nokp and jpa.apc_tahun=year(getdate()) where jpn.nn_tkh = " & Chk_Tkh(Tx_TkhResit.Text) & " and jpn.id_amalan = " & Cb_Tpt_Amalan.SelectedValue & " order by nama"
        Tb = "jt_penuh_nniat"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        Gd.DataBind()
    End Sub

    Protected Sub cmdCetak_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdCetak.Click
        'If Gd.Rows.Count > 1 Then  Else Exit Sub
        Notis_Niat()
    End Sub
End Class