﻿Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports System.Text
Imports iTextSharp.text
Imports iTextSharp.text.pdf
Imports iTextSharp.text.html
Imports iTextSharp.text.html.simpleparser

Partial Public Class P2_SN_Penuh2
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Protected Sub cmd_Jana_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Jana.Click
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()
        Dim rdr As SqlDataReader
        Dim num As Integer = 1


        cmd.CommandType = CommandType.StoredProcedure
        If Cb_Jenis.SelectedValue = 4 Then
            cmd.CommandText = "SPMJ_Exam_Complete_Reg_MDW"
        Else
            cmd.CommandText = "SPMJ_Exam_Complete_Reg"
        End If
        cmd.Parameters.Add("@j_daftar", SqlDbType.Int, 4).Value = Cb_Jenis.SelectedValue
        cmd.Parameters.Add("@begin_regno", SqlDbType.Int).Value = Tx_NoPd1.Text
        cmd.Parameters.Add("@end_regno", SqlDbType.Int).Value = Tx_NoPd2.Text
        cmd.Connection = con
        'Try
        '    con.Open()
        '    Dim GridView1 = New GridView()
        '    GridView1.EmptyDataText = "No Records Found"
        '    GridView1.DataSource = cmd.ExecuteReader()
        '    GridView1.DataBind()

        '    'Dump to PDF
        '    Response.ContentType = "application/pdf"
        '    Response.AddHeader("content-disposition", "attachment;filename=senarai_pendaftaran.pdf")
        '    Response.Cache.SetCacheability(HttpCacheability.NoCache)
        '    Dim sw As New StringWriter()
        '    Dim hw As New HtmlTextWriter(sw)
        '    GridView1.RenderControl(hw)
        '    Dim sr As New StringReader(sw.ToString())
        '    Dim pdfDoc As New Document(PageSize.A4.Rotate, 10.0F, 10.0F, 10.0F, 0.0F)
        '    Dim htmlparser As New HTMLWorker(pdfDoc)
        '    'Add New 
        '    Dim pdfWrite As PdfWriter = PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
        '    Dim ev As New itsEvents
        '    pdfWrite.PageEvent = ev
        '    'PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
        '    pdfDoc.Open()
        '    htmlparser.Parse(sr)
        '    pdfDoc.Close()
        '    Response.Write(pdfDoc)
        '    Response.End()
        'Catch ex As Exception
        '    Throw ex
        'Finally
        '    con.Close()
        '    con.Dispose()
        'End Try


        Dim Table As New PdfPTable(2) 'Table columns
        Table.TotalWidth = 216.0F
        Table.LockedWidth = True

        Dim widths() As Single = {1.0F, 2.0F} ', 2.0F, 2.0F, 2.0F, 2.0F, 2.0F, 2.0F, }
        Table.SetWidths(widths)
        Table.HorizontalAlignment = 0

        Table.SpacingBefore = 20.0F
        Table.SpacingAfter = 30.0F

        Dim cell As New PdfPCell(New Phrase("Laporan Daftar"))
        cell.Colspan = 2
        cell.Border = 0
        cell.HorizontalAlignment = 1
        Try
            con.Open()
            rdr = cmd.ExecuteReader()
            While rdr.Read
                Table.AddCell(num)
                Table.AddCell(rdr(0).ToString)
                'Table.AddCell(rdr(1).ToString)
                'Table.AddCell(rdr(2).ToString)
                'Table.AddCell(rdr(3).ToString)
                'Table.AddCell(rdr(4).ToString)
                'Table.AddCell(rdr(5).ToString)
                num = num + 1
            End While
        Catch ex As Exception
            'Msg(Me, ex)
        End Try
    End Sub
End Class