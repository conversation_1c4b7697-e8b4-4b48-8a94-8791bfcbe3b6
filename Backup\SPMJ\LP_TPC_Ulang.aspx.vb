﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class LP_TPC_Ulang
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'JAWATAN
        Cb_Jawatan.Items.Clear()
        Cb_Jawatan.Items.Add("(SEMUA)")
        Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = ""
        Cb_Jawatan.Items.Add("JURURAWAT TERLATIH")
        Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "1"
        Cb_Jawatan.Items.Add("LATIHAN ELEKTIF")
        Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "2"
        Cb_Jawatan.Items.Add("PENGAJAR")
        Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "3"
        Cb_Jawatan.Items.Add("INSTRUKTOR KLINIKAL")
        Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "4"

        'NEGERI
        Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()
        Cb_Negeri.Items.Clear()
        Cb_Negeri.Items.Add("(SEMUA)")
        Cb_Negeri.Items.Item(Cb_Negeri.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Negeri.Items.Add(Rdr(0))
            Cb_Negeri.Items.Item(Cb_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'AMALAN
        Cmd.CommandText = "SELECT Dc_amalan, Id_amalan FROM pn_tpt_amalan where sektor=2 ORDER BY Dc_amalan"
        Rdr = Cmd.ExecuteReader()
        Cb_Tpt_Amalan.Items.Clear()
        Cb_Tpt_Amalan.Items.Add("(SEMUA)")
        Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Tpt_Amalan.Items.Add(Rdr(0))
            Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        'TAHUN
        Dim i As Int16
        Cb_Tahun.Items.Clear()
        Cb_Tahun.Items.Add("")
        For i = 0 To 9
            Cb_Tahun.Items.Add(Year(Now) - i)
        Next i

        'JANTINA
        Cb_Jantina.Items.Clear()
        Cb_Jantina.Items.Add("(SEMUA)")
        Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = ""
        Cb_Jantina.Items.Add("LELAKI")
        Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = "1"
        Cb_Jantina.Items.Add("PEREMPUAN")
        Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = "2"
    End Sub

    Protected Sub Cb_Negeri_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Negeri.SelectedIndexChanged
        Dim SQL As String
        SQL = "select dc_amalan, id_amalan from pn_tpt_amalan where sektor=2 and negeri = '" & Cb_Negeri.SelectedValue & "' order by dc_amalan"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'TEMPAT AMALAN
        Cmd.CommandText = SQL
        Rdr = Cmd.ExecuteReader()
        Cb_Tpt_Amalan.Items.Clear()
        Cb_Tpt_Amalan.Items.Add("(SEMUA)")
        Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Tpt_Amalan.Items.Add(Rdr(0))
            Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Dim SQL As String

        SQL = "select distinct jt.nokp as 'NO. PASPORT' ,nama as 'NAMA', nopd as 'NO. TPC', Umur as 'UMUR' "
        'SQL += "case t_kahwin when 1 then 'BUJANG' when 2 then 'BERKAHWIN' when 3 then 'DUDA' when 4 then 'JANDA' end as 'TARAF PERKAHWINAN',"
        'SQL += "dc_negara as 'WARGANEGARA', case jantina when 1 then 'L' when 2 then 'P' else '' end as 'JANTINA',"
        'SQL += "case jt.j_daftar when 1 then 'JB' when 2 then 'LE' when 3 then 'PG' when 4 then 'IK' else '' end as 'JAWATAN',"
        'SQL += "CONVERT(char(12), mohon_tkh, 103) as 'TARIKH KELULUSAN',"
        'SQL += "dc_amalan as 'TEMPAT AMALAN', upper(pta.alamat) as 'ALAMAT AMALAN' "
        SQL += "from jt_tpc jt "
        SQL += "inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp "
        SQL += "inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp "
        SQL += "left join pn_negara pn on jt.warganegara=pn.id_negara "
        SQL += "left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan "
        SQL += "left join pn_negeri pni on pni.id_negeri=pta.negeri "
        SQL += "where jt.nokp is not null and status=1 "

        If Cb_Negeri.SelectedIndex < 1 Then  Else SQL += " and pta.negeri = " & Cb_Negeri.SelectedValue

        If Cb_Tpt_Amalan.SelectedIndex < 1 Then  Else SQL += " and pta.id_amalan = " & Cb_Tpt_Amalan.SelectedValue

        If Cb_Jawatan.SelectedIndex < 1 Then  Else SQL += " and jt.j_daftar = " & Cb_Jawatan.SelectedValue

        If Cb_Tahun.SelectedItem.Text = "" Then  Else SQL += " and jtt.tpc_tahun = " & Cb_Tahun.SelectedValue

        If Cb_Jantina.SelectedIndex < 1 Then  Else SQL += " and jt.jantina = " & Cb_Jantina.SelectedValue

        If Tx_NoTPC1.Text = "" Then
        Else
            If Tx_NoTPC2.Text = "" Then Tx_NoTPC2.Text = Tx_NoTPC1.Text
            SQL += " and jt.nopd between '" & CLng(Tx_NoTPC1.Text) & "' and '" & CLng(Tx_NoTPC2.Text) & "' "
        End If

        SQL += " order by nama"

        Laporan(SQL)
    End Sub

    Public Sub Laporan(ByVal SQL As String)
        Dim Tajuk, Tajuk2, tahun, status As String, total As Integer = 0

        Tajuk = "LAPORAN PENDAFTARAN TPC (TEMPOH)"
        Tajuk2 = ""

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='8' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='8' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>NO</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>NO. PASPORT</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>NAMA</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>NO TPC</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>UMUR</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>PERLANJUTAN TPC</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>TEMPOH SAH TPC</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>STATUS</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        Cmd.CommandText = SQL
        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "jt_tpc")
        Rdr.Close()

        Header = "" : Dim i As Integer = 0
        For Each dr As DataRow In Ds.Tables(0).Rows
            i += 1
            Header += "<tr>"
            Header += "    <td>" & i & "</td> "
            Header += "    <td>" & dr.Item(0) & "</td> "
            Header += "    <td>" & dr.Item(1) & "</td> "
            Header += "    <td>" & dr.Item(2) & "</td> "
            Header += "    <td>" & dr.Item(3) & "</td> "

            tahun = "" : status = "" : total = 0
            Cmd.CommandText = "select year(tpc_tmt) from jt_tpc_tpc where nokp='" & dr.Item(0) & "' group by year(tpc_tmt)"
            Rdr = Cmd.ExecuteReader()
            While Rdr.Read
                If Not IsDBNull(Rdr(0)) Then total += 1
            End While
            Rdr.Close()
            Header += "    <td>TAHUN KE-" & total & "</td> "

            Cmd.CommandText = "select top 1 CONVERT(char(12), tpc_tmt, 103) from jt_tpc_tpc where nokp='" & dr.Item(0) & "' order by tpc_tmt desc"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                If Not IsDBNull(Rdr(0)) Then tahun = Rdr(0)
            End If
            Rdr.Close()
            Header += "    <td> " & tahun & "</td> "

            If tahun = "" Then
                status = ""
            Else
                If CDate(tahun) < Now Then status = "TIDAK AKTIF" Else status = "AKTIF"
            End If
            Header += "    <td>" & status & "</td> "
            Header += "</tr>"
        Next
        Response.Write(Header)
        Response.Write("</table>")

        Response.End()
        Response.Flush()
    End Sub
End Class