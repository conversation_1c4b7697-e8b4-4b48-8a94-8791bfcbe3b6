﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="PN_Tajaan.aspx.vb" Inherits="SPMJ.WebForm56" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 23px;
        }
        .style2
        {
            height: 7px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td></td>
            <td width="600"></td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style1"></td>
            <td width="600" align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#719548" class="style1">Penyelenggaraan&nbsp; - Tajaan</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td class="style2"></td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style2">
                &nbsp;&nbsp; </td>
            <td class="style2"></td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; " 
                bgcolor="White" class="style2">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj9" 
    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">TAJAAN</asp:TextBox>
                        <asp:TextBox ID="Tx_Tajaan" runat="server" 
                    CssClass="std" Width="250px" 
                                                     Wrap="False" MaxLength="50"></asp:TextBox>
                        <asp:TextBox ID="Tx_Id" runat="server" CssClass="std" 
                            Width="60px" Wrap="False" Visible="False"></asp:TextBox>
            </td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style2">
                &nbsp;</td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px"></asp:TextBox>
                <asp:Button ID="cmd_Cari1" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SIMPAN" Width="80px" />
            &nbsp;<asp:Button ID="cb_baru" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="BARU" Width="80px" Visible="False" />
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; border-bottom-style: solid; border-bottom-width: 1px;" 
                bgcolor="White">
                &nbsp;&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td></td>
            <td width="600">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td width="600">
                                                <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="600px" GridLines="Horizontal" BorderColor="#719548">
                                                    <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                                                    <RowStyle BackColor="#F4F4F4" Height="21px" />
                                                    <Columns>
                                                        <asp:TemplateField ShowHeader="False">
                                                            <ItemTemplate>
                                                                <asp:Button ID="Button2" runat="server" CausesValidation="False" 
                                                                    CommandName="Select" Text="PILIH" Font-Names="Arial" Font-Size="8pt" 
                                                                    Height="20px" Width="80px"/>
                                                                &nbsp;<asp:Button ID="Cb_Padam" runat="server" CommandName="Select" 
                                                                    Font-Names="Arial" Font-Size="8pt" Height="20px" onclick="Cb_Padam_Click" 
                                                                    tabIndex="3" Text="PADAM" Width="80px" />
                                                                <cc1:ConfirmButtonExtender ID="Cb_Padam_ConfirmButtonExtender" runat="server" 
                                                                    ConfirmText="Padam Rekod Ini?" Enabled="True" TargetControlID="Cb_Padam">
                                                                </cc1:ConfirmButtonExtender>
                                                            </ItemTemplate>
                                                            <ControlStyle Font-Names="Arial" Font-Size="8pt" Width="50px" />
                                                        </asp:TemplateField>
                                                        <asp:TemplateField HeaderText="#">
                                                            <HeaderStyle HorizontalAlign="Center" />
                                                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                                                        </asp:TemplateField>
                                                    </Columns>
                                                    <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                                                    <SelectedRowStyle Font-Bold="True" />
                                                    <HeaderStyle BackColor="#7EA851" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                                                    <AlternatingRowStyle BackColor="White" />
                                                </asp:GridView>
                                        </td>
            <td></td>
        </tr>
    
    
        <tr>
            <td>&nbsp;</td>
            <td width="600">
                &nbsp;</td>
            <td>&nbsp;</td>
        </tr>
    
    
    </table>
    
    
    </div></asp:Content>
