﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_RET_Negara
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'TAHUN APC
        For i = Now.Year To Now.Year - 5 Step -1
            Cb_Tahun.Items.Add(i)
        Next

        'JAWATAN
        With Cb_Jawatan
            .Items.Clear()
            .Items.Add("JURURAWAT BERDAFTAR")
            .Items.Item(.Items.Count - 1).Value = "1"
            .Items.Add("JURURAWAT MASYARAKAT")
            .Items.Item(.Items.Count - 1).Value = "2"
            .Items.Add("PENOLONG JURURAWAT")
            .Items.Item(.Items.Count - 1).Value = "3"
        End With
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim SQL As String
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        SQL = "delete from temp_ret; "
        SQL += "insert temp_ret "
        'Fixing Report based on year operation and months 28052020 - OSH 
        SQL += "select distinct(ret_negara) as negara,count(distinct nokp) as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and month(log_tkh)=1 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        SQL += "union select distinct(ret_negara) as negara,null as b1, count(distinct nokp) as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and month(log_tkh)=2 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, count(distinct nokp) as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and month(log_tkh)=3 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, count(distinct nokp) as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and month(log_tkh)=4 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, count(distinct nokp) as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and month(log_tkh)=5 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, count(distinct nokp) as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and month(log_tkh)=6 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, count(distinct nokp) as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and month(log_tkh)=7 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,count(distinct nokp) as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and month(log_tkh)=8 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, count(distinct nokp) as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and month(log_tkh)=9 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, count(distinct nokp) as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and month(log_tkh)=10 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, count(distinct nokp) as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and month(log_tkh)=11 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, count(distinct nokp) as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and month(log_tkh)=12 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,count(distinct nokp) as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and log_tkh is null and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, count(distinct nokp) as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and year(log_tkh)=" & Cb_Tahun.Text & " and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara"

        'Comment Original 28052020 - OSH 
        'SQL += "select distinct(ret_negara) as negara,count(nokp) as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and month(apc_tkh)=1 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        'SQL += "union select distinct(ret_negara) as negara,null as b1, count(nokp) as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and month(apc_tkh)=2 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        'SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, count(nokp) as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and month(apc_tkh)=3 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        'SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, count(nokp) as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and month(apc_tkh)=4 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        'SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, count(nokp) as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and month(apc_tkh)=5 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        'SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, count(nokp) as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and month(apc_tkh)=6 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        'SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, count(nokp) as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and month(apc_tkh)=7 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        'SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,count(nokp) as b8, null as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and month(apc_tkh)=8 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        'SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, count(nokp) as b9, null as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and month(apc_tkh)=9 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        'SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, count(nokp) as b10, null as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and month(apc_tkh)=10 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        'SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, count(nokp) as b11, null as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and month(apc_tkh)=11 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        'SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, count(nokp) as b12,null as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and month(apc_tkh)=12 and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        'SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,count(nokp) as x, null as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and apc_tkh is null and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara "
        'SQL += "union select distinct(ret_negara) as negara,null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, null as b11, null as b12,null as x, count(nokp) as nokp, null as total from jt_penuh_apc jpa left outer join pn_negara pn on jpa.ret_negara=pn.id_negara where ret=1 and apc_tahun=" & Cb_Tahun.Text & " and nokp in (select nokp from jt_penuh) and jpa.j_daftar=" & Cb_Jawatan.SelectedValue & " group by ret_negara"
        SQL += "; "

        Cmd.CommandText = SQL
        Cmd.ExecuteNonQuery()
        Cn.Close()

        Laporan_RET(Cb_Tahun.Text, Cb_Jawatan.SelectedItem.Text)
    End Sub

    Public Sub Laporan_RET(ByVal tahun As String, ByVal jawatan As String)
        Dim Tajuk, Tajuk2 As String
        Dim b1, b2, b3, b4, b5, b6, b7, b8, b9, b10, b11, b12, x, no As Integer

        'Comment Original 29052020 - OSH 
        'Tajuk = "STATISTIK PENGEKALAN NAMA BAGI JAWATAN " & jawatan & ", TAHUN " & tahun &

        'Changing Label Report Title 29052020 - OSH 
        Tajuk = "STATISTIK PENGEKALAN NAMA BAGI JAWATAN " & jawatan & " (TAHUN OPERASI " & tahun & ")"
        Tajuk2 = "MENGIKUT NEGARA DAN BULAN "

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='15' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='15' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>NEGARA</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JANUARI</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>FEBRUARI</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>MAC</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>APRIL</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>MEI</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JULAI</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>OGOS</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>SEPTEMBER</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>OKTOBER</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>NOVEMBER</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>DISEMBER</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>X</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        'temp ret
        Cmd.CommandText = "select top 10 dc_negara, isnull(sum(b1),'0'), isnull(sum(b2),'0'), isnull(sum(b3),'0'), isnull(sum(b4),'0'), isnull(sum(b5),'0'), isnull(sum(b6),'0'), isnull(sum(b7),'0'), " & _
                          "isnull(sum(b8),'0'), isnull(sum(b9),'0'), isnull(sum(b10),'0'), isnull(sum(b11),'0'), isnull(sum(b12),'0'), isnull(sum(x),'0'), isnull(sum(nokp),'0'), isnull(sum(total),'0') " & _
                          "from temp_ret ret left outer join pn_negara pn on ret.negara=pn.id_negara group by dc_negara order by sum(nokp) desc"
        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "temp_ret")
        Rdr.Close()

        Header = ""
        For Each dr As DataRow In Ds.Tables(0).Rows
            Header += "<tr>"
            Header += "    <td style='vertical-align: middle; text-align: left;'>" & dr.Item(0) & "</td> "
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(1) & "</td> " : b1 += dr.Item(1)
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(2) & "</td> " : b2 += dr.Item(2)
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(3) & "</td> " : b3 += dr.Item(3)
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(4) & "</td> " : b4 += dr.Item(4)
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(5) & "</td> " : b5 += dr.Item(5)
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(6) & "</td> " : b6 += dr.Item(6)
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(7) & "</td> " : b7 += dr.Item(7)
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(8) & "</td> " : b8 += dr.Item(8)
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(9) & "</td> " : b9 += dr.Item(9)
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(10) & "</td> " : b10 += dr.Item(10)
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(11) & "</td> " : b11 += dr.Item(11)
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(12) & "</td> " : b12 += dr.Item(12)
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(13) & "</td> " : x += dr.Item(13)
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(14) & "</td> " : no += dr.Item(14)
            Header += "</tr>"
        Next
        Header += "<tr>"
        Header += "    <td>JUMLAH</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & b1 & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & b2 & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & b3 & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & b4 & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & b5 & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & b6 & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & b7 & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & b8 & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & b9 & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & b10 & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & b11 & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & b12 & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & x & "</td> "
        Header += "    <td style='vertical-align: middle; text-align: right;' >" & no & "</td> "
        Header += "</tr>"

        Response.Write(Header)
        Response.Write("</table>")

        Header = ""
        Header = "<table>"
        Header += "<tr></tr>"
        Header += "<tr></tr>"
        Header += "<tr>"
        Header += "    <td colspan='15'>X dan (space): Rekod yang didaftarkan tidak lengkap</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='15'>Laporan ini berdasarkan pemohonan RON berantai bagi setiap individu (WORKLOAD PROCESSING)</td> "
        Header += "</tr>"
        Response.Write(Header)
        Response.Write("</table>")

        Response.End()
        Response.Flush()
    End Sub
End Class