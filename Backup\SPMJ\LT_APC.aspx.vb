﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm36
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Tb = "jt_penuh"

        Dim List_Adp As New SqlDataAdapter(X, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'NEGERI
        Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()
        Cb_Negeri.Items.Clear()
        Cb_Negeri.Items.Add("(SEMUA)")
        Cb_Negeri.Items.Item(Cb_Negeri.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Negeri.Items.Add(Rdr(0))
            Cb_Negeri.Items.Item(Cb_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        'TAHUN
        Cb_Tahun.Items.Clear()
        Cb_Tahun.Items.Add(Year(Now))
        Cb_Tahun.Items.Add(Year(Now) + 1)

        'BANGSA
        Cb_Bangsa.Items.Clear()
        Cb_Bangsa.Items.Add("(SEMUA)")
        Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = ""
        Cb_Bangsa.Items.Add("MELAYU")
        Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = "1"
        Cb_Bangsa.Items.Add("CINA")
        Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = "2"
        Cb_Bangsa.Items.Add("INDIA")
        Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = "3"
        Cb_Bangsa.Items.Add("LAIN-LAIN")
        Cb_Bangsa.Items.Item(Cb_Bangsa.Items.Count - 1).Value = "4"

        'JANTINA
        Cb_Jantina.Items.Clear()
        Cb_Jantina.Items.Add("(SEMUA)")
        Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = ""
        Cb_Jantina.Items.Add("LELAKI")
        Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = "1"
        Cb_Jantina.Items.Add("PEREMPUAN")
        Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = "2"

        'JAWATAN
        Cb_Jawatan.Items.Clear()
        Cb_Jawatan.Items.Add("(SEMUA)")
        Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = ""
        Cb_Jawatan.Items.Add("JURURAWAT BERDAFTAR")
        Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "1"
        Cb_Jawatan.Items.Add("JURURAWAT MASYARAKAT")
        Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "2"
        Cb_Jawatan.Items.Add("PENOLONG JURURAWAT")
        Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "3"
        Cb_Jawatan.Items.Add("BIDAN")
        Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "4"


    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim SQL, SQL_pilih As String
        SQL = "select"
        SQL_pilih = ""
        If Chk_Pilih.Items(0).Selected Then SQL_pilih += " Nama,"
        If Chk_Pilih.Items(1).Selected Then SQL_pilih += " jp.nokp as 'No. KP',"
        If Chk_Pilih.Items(2).Selected Then SQL_pilih += " nopd as 'No. Daftar',"
        If Chk_Pilih.Items(3).Selected Then SQL_pilih += " case jp.j_daftar when 1 then 'JB' when 2 then 'JM' when 3 then 'PJ' end as 'Jawatan',"
        If Chk_Pilih.Items(4).Selected Then SQL_pilih += " case bangsa when 1 then 'M' when 2 then 'C' when 3 then 'I' else 'L' end as 'Bangsa' ,"
        If Chk_Pilih.Items(5).Selected Then SQL_pilih += " apc_no as 'No. APC',"
        If Chk_Pilih.Items(6).Selected Then SQL_pilih += " dc_amalan as 'Tempat Amalan',"
        If Len(SQL_pilih) > 0 Then SQL_pilih = Mid(SQL_pilih, 1, Len(SQL_pilih) - 1) : SQL += SQL_pilih

        SQL += " from jt_penuh_apc jpa inner join jt_penuh jp on jp.nokp=jpa.nokp  inner join pn_tpt_amalan pta on jpa.id_amalan=pta.id_amalan where jp.nokp is not null"

        If Cb_Negeri.SelectedIndex < 1 Then
            SQL += ""
        Else
            SQL += " and pta.negeri = " & Cb_Negeri.SelectedValue
        End If

        If Cb_Tpt_Amalan.SelectedIndex < 1 Then
            SQL += ""
        Else
            SQL += " and pta.id_amalan = " & Cb_Tpt_Amalan.SelectedValue
        End If

        If Cb_Jawatan.SelectedIndex < 1 Then
            SQL += ""
        Else
            SQL += " and jp.j_daftar = " & Cb_Jawatan.SelectedValue
        End If

        SQL += " and jpa.apc_tahun = " & Cb_Tahun.SelectedValue

        If Cb_Jantina.SelectedIndex < 1 Then
            SQL += ""
        Else
            SQL += " and jp.jantina = " & Cb_Jantina.SelectedValue
        End If

        If Cb_Bangsa.SelectedIndex < 1 Then
            SQL += ""
        Else
            SQL += " and jp.bangsa = " & Cb_Bangsa.SelectedValue
        End If


        Msg(Me, SQL)

        Cari(SQL)
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        ''e.Row.Cells(1).Visible = False
        'e.Row.Cells(0).Width = Unit.Pixel(30)
        'e.Row.Cells(2).Visible = False
        'e.Row.Cells(4).Visible = False
        'e.Row.Cells(5).Visible = False
        'e.Row.Cells(7).Visible = False
        ''e.Row.Cells(8).Visible = False
        'e.Row.Cells(9).Visible = False
        'e.Row.Cells(10).Visible = False
        'e.Row.Cells(11).Visible = False

        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged

    End Sub

    Protected Sub Cb_Negeri_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Negeri.SelectedIndexChanged
        Dim SQL As String
        SQL = "select dc_amalan, id_amalan from pn_tpt_amalan where negeri = '" & Cb_Negeri.SelectedValue & "' order by dc_amalan"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'TEMPAT AMALAN
        Cmd.CommandText = SQL
        Rdr = Cmd.ExecuteReader()
        Cb_Tpt_Amalan.Items.Clear()
        Cb_Tpt_Amalan.Items.Add("(SEMUA)")
        Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Tpt_Amalan.Items.Add(Rdr(0))
            Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari2.Click
        GridViewExportUtil.Export("Laporan.xls", Gd)

    End Sub
End Class