﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm26
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select distinct(NAMA), jp.NOKP as 'NO. KP/PASPORT', 'Tpc-'+ cast(nopd as varchar(6)) as 'NO. PD', case isnull(jpa.tpc_no,-1) when -1 then '' else 'Aktif' end as 'STATUS',jp.j_daftar  from jt_tpc jp left outer join jt_tpc_tpc jpa on jp.nokp=jpa.nokp and jpa.tpc_tahun = year(getdate()) where " & X & " order by jp.nama"
        Tb = "jt_tpc"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        Gd.DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(6).Visible = False
        e.Row.Cells(0).Width = Unit.Pixel(30)
        e.Row.Cells(4).Width = Unit.Pixel(60)
        e.Row.Cells(5).Width = Unit.Pixel(80)
        'e.Row.Cells(6).Width = Unit.Pixel(80)
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Tx_Nama.Text = "" And Tx_NoKP.Text = "" And Tx_NoPd.Text = "" Then Exit Sub
        Cari("nama like '" & Tx_Nama.Text & "%' and jp.nokp like '" & Tx_NoKP.Text & "%' and nopd like '" & Tx_NoPd.Text & "%'")
    End Sub


    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Session("NAMA") = Gd.SelectedRow.Cells(2).Text
        Session("NOKP") = Gd.SelectedRow.Cells(3).Text
        Session("NOPD") = Gd.SelectedRow.Cells(4).Text
        Session("DAFTAR") = Gd.SelectedRow.Cells(6).Text
        Response.Redirect("P4_TPC_Proses.aspx")
    End Sub
End Class