﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient


Partial Public Class WebForm40
    Inherits System.Web.UI.Page

    Public Sub Isi_Subjek(ByVal X As Int16)
        Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
        Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
        Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
        Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
        Bt4.Visible = True : Bt5.Visible = False : Bt6.Visible = False : Bt7.Visible = False : Bt8.Visible = False
        Textbox8.Visible = False : Textbox9.Visible = False : Textbox10.Visible = False : Textbox11.Visible = False : Textbox12.Visible = False

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        If X = 1 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'PMR' order by dc_subjek"
        If X = 2 Or X = 4 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'SPM' order by dc_subjek"
        If X = 3 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'STPM' order by dc_subjek"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If Cb_Kursus.SelectedIndex <> 4 Then
                If Rdr(0) = "MUET" Then
                Else
                    Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
                End If
            Else
                Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
            End If
        End While
        Rdr.Close()
        Cn.Close()

        If X = 1 Then
            Gred_PMR(Cb_Kpts1)
            Gred_PMR(Cb_Kpts2)
            Gred_PMR(Cb_Kpts3)
            Gred_PMR(Cb_Kpts4)
            Gred_PMR(Cb_Kpts5)
            Gred_PMR(Cb_Kpts6)
            Gred_PMR(Cb_Kpts7)
            Gred_PMR(Cb_Kpts8)
            Gred_PMR(Cb_Kpts9)
        ElseIf X = 2 Then
            Gred_SPM(Cb_Kpts1)
            Gred_SPM(Cb_Kpts2)
            Gred_SPM(Cb_Kpts3)
            Gred_SPM(Cb_Kpts4)
            Gred_SPM(Cb_Kpts5)
            Gred_SPM(Cb_Kpts6)
            Gred_SPM(Cb_Kpts7)
            Gred_SPM(Cb_Kpts8)
            Gred_SPM(Cb_Kpts9)
        ElseIf X = 3 Then
            Gred_STPM(Cb_Kpts1)
            Gred_STPM(Cb_Kpts2)
            Gred_STPM(Cb_Kpts3)
            Gred_STPM(Cb_Kpts4)
            Gred_STPM(Cb_Kpts5)
            Gred_STPM(Cb_Kpts6)
            Gred_STPM(Cb_Kpts7)
            Gred_STPM(Cb_Kpts8)
            Gred_STPM(Cb_Kpts9)
        ElseIf X = 4 Then
            Gred_SPM_Baru(Cb_Kpts1)
            Gred_SPM_Baru(Cb_Kpts2)
            Gred_SPM_Baru(Cb_Kpts3)
            Gred_SPM_Baru(Cb_Kpts4)
            Gred_SPM_Baru(Cb_Kpts5)
            Gred_SPM_Baru(Cb_Kpts6)
            Gred_SPM_Baru(Cb_Kpts7)
            Gred_SPM_Baru(Cb_Kpts8)
            Gred_SPM_Baru(Cb_Kpts9)
        Else
        End If
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Pelatih_Daftar", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        UICulture = "en-GB"
        Culture = "en-GB"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cb_Thn_Kelayakan.Items.Clear()
        Cb_Thn_Kelayakan.Items.Add("")
        For i = Now.Year To Now.Year - 30 Step -1
            Cb_Thn_Kelayakan.Items.Add(i)
        Next

        If Cb_NoKP.SelectedIndex = 0 Then Tx_NoKP.MaxLength = 12

        'WARGANEGARA
        Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara ORDER BY dc_NEGARA"
        Rdr = Cmd.ExecuteReader()
        Cb_Warga.Items.Clear()
        Cb_Warga.Items.Add("")
        Cb_Negara.Items.Clear()
        Cb_Negara.Items.Add("")
        While Rdr.Read
            Cb_Warga.Items.Add(Rdr(0))
            Cb_Warga.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
            Cb_Negara.Items.Add(Rdr(0))
            Cb_Negara.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cb_Warga.Items.FindByText("MALAYSIA").Selected = True

        'NEGERI
        Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()
        Cb_TP_Negeri.Items.Clear()
        Cb_TP_Negeri.Items.Add("")
        Cb_W_Negeri.Items.Clear()
        Cb_W_Negeri.Items.Add("")
        While Rdr.Read
            Cb_TP_Negeri.Items.Add(Rdr(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr(1)
            Cb_W_Negeri.Items.Add(Rdr(0))
            Cb_W_Negeri.Items.Item(Cb_W_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'KURSUS
        Cmd.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS ORDER BY ID_KURSUS"
        Rdr = Cmd.ExecuteReader()
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("")
        While Rdr.Read
            Cb_Kursus.Items.Add(Rdr(0))
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'BULAN
        Cb_Sesi_Bulan.Items.Add("(BULAN)") : Cb_Sesi_Bulan0.Items.Add("(BULAN)")
        Cb_Sesi_Bulan.Items.Add("JANUARI") : Cb_Sesi_Bulan0.Items.Add("JANUARI")
        Cb_Sesi_Bulan.Items.Add("FEBRUARI") : Cb_Sesi_Bulan0.Items.Add("FEBRUARI")
        Cb_Sesi_Bulan.Items.Add("MAC") : Cb_Sesi_Bulan0.Items.Add("MAC")
        Cb_Sesi_Bulan.Items.Add("APRIL") : Cb_Sesi_Bulan0.Items.Add("APRIL")
        Cb_Sesi_Bulan.Items.Add("MEI") : Cb_Sesi_Bulan0.Items.Add("MEI")
        Cb_Sesi_Bulan.Items.Add("JUN") : Cb_Sesi_Bulan0.Items.Add("JUN")
        Cb_Sesi_Bulan.Items.Add("JULAI") : Cb_Sesi_Bulan0.Items.Add("JULAI")
        Cb_Sesi_Bulan.Items.Add("OGOS") : Cb_Sesi_Bulan0.Items.Add("OGOS")
        Cb_Sesi_Bulan.Items.Add("SEPTEMBER") : Cb_Sesi_Bulan0.Items.Add("SEPTEMBER")
        Cb_Sesi_Bulan.Items.Add("OKTOBER") : Cb_Sesi_Bulan0.Items.Add("OKTOBER")
        Cb_Sesi_Bulan.Items.Add("NOVEMBER") : Cb_Sesi_Bulan0.Items.Add("NOVEMBER")
        Cb_Sesi_Bulan.Items.Add("DISEMBER") : Cb_Sesi_Bulan0.Items.Add("DISEMBER")

        'TAHUN
        Cb_Sesi_Tahun.Items.Add("(TAHUN)")
        Cb_Sesi_Tahun0.Items.Add("(TAHUN)")
        For i = 0 To 5
            Cb_Sesi_Tahun.Items.Add(Year(Now) - i)
            Cb_Sesi_Tahun0.Items.Add(Year(Now) - i)
        Next

        'KOLEJ
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ WHERE JENIS=" & RadioButtonList1.SelectedValue & " ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'AGAMA -06082013-OSH
        Cmd.CommandText = "SELECT DC_AGAMA, ID_AGAMA FROM SPMJ_REF_AGAMA ORDER BY ID_AGAMA"
        Rdr = Cmd.ExecuteReader()
        Cb_Agama.Items.Clear()
        Cb_Agama.Items.Add("")
        While Rdr.Read
            Cb_Agama.Items.Add(Rdr(0))
            Cb_Agama.Items.Item(Cb_Agama.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'TAJAAN
        Cmd.CommandText = "SELECT DC_TAJAAN, Id_TAJAAN FROM PN_TAJAAN ORDER BY DC_TAJAAN"
        Rdr = Cmd.ExecuteReader()
        Cb_Tajaan.Items.Clear()
        Cb_Tajaan.Items.Add("")
        While Rdr.Read
            Cb_Tajaan.Items.Add(Rdr(0))
            Cb_Tajaan.Items.Item(Cb_Tajaan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub Bt4_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt4.Click
        If Cb_Kpts1.SelectedIndex < 1 And Cb_Kpts2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #1!") : Exit Sub
        Cb_Sbj5.Visible = True
        Cb_Kpts5.Visible = True
        Bt5.Visible = True
        Bt4.Visible = False
        Textbox8.Visible = True
    End Sub

    Private Sub Bt5_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Bt5.Click
        If Cb_Sbj5.SelectedIndex < 1 Or Cb_Kpts5.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #5!") : Exit Sub
        Cb_Sbj6.Visible = True
        Cb_Kpts6.Visible = True
        Bt6.Visible = True
        Bt5.Visible = False
        Textbox9.Visible = True
    End Sub

    Private Sub Bt6_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Bt6.Click
        If Cb_Sbj6.SelectedIndex < 1 Or Cb_Kpts6.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #6!") : Exit Sub
        Cb_Sbj7.Visible = True
        Cb_Kpts7.Visible = True
        Bt7.Visible = True
        Bt6.Visible = False
        Textbox10.Visible = True
    End Sub

    Private Sub Bt7_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Bt7.Click
        If Cb_Sbj7.SelectedIndex < 1 Or Cb_Kpts7.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #7!") : Exit Sub
        Cb_Sbj8.Visible = True
        Cb_Kpts8.Visible = True
        Bt8.Visible = True
        Bt7.Visible = False
        Textbox11.Visible = True
    End Sub

    Private Sub Bt8_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Bt8.Click
        If Cb_Sbj8.SelectedIndex < 1 Or Cb_Kpts8.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #8!") : Exit Sub
        Cb_Sbj9.Visible = True
        Cb_Kpts9.Visible = True
        Bt8.Enabled = False
        Textbox12.Visible = True
    End Sub

    Protected Sub Cb_Kursus_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kursus.SelectedIndexChanged
        Cb_Kelayakan.Enabled = True : Cb_Kelayakan.Items.Clear()
        Cb_Kelayakan0.Enabled = True : Cb_Kelayakan0.SelectedIndex = 0
        Cb_Thn_Kelayakan.Enabled = True : Cb_Thn_Kelayakan.SelectedIndex = 0
        Panel3.Enabled = False
        Cb_Sbj10.SelectedIndex = 0 : Tx_Kpts10.Text = ""
        Cb_Sbj11.SelectedIndex = 0 : Cb_Kpts11.SelectedIndex = 0

        Panel1.Visible = False
        cmd_Semak.Enabled = True
        cmd_Semak.Visible = True
        Bt4.Enabled = True
        Cb_Kpts1.Enabled = True : Cb_Kpts1.Items.Clear()
        Cb_Kpts2.Enabled = True : Cb_Kpts2.Items.Clear()
        Cb_Kpts3.Enabled = True : Cb_Kpts3.Items.Clear()
        Cb_Kpts4.Enabled = True : Cb_Kpts4.Items.Clear()

        If Cb_Kursus.SelectedIndex = 1 Or Cb_Kursus.SelectedIndex = 5 Then
            Cb_Kelayakan.Items.Add("SPM") : Cb_Kelayakan.Items.Add("STPM")
        ElseIf Cb_Kursus.SelectedIndex = 2 Then
            Cb_Kelayakan.Items.Add("SRP") : Cb_Kelayakan.Items.Add("PMR") : Cb_Kelayakan.Items.Add("SPM")
        ElseIf Cb_Kursus.SelectedIndex = 3 Then
            Cb_Kelayakan.Items.Add("SRP") : Cb_Kelayakan.Items.Add("PMR") : Cb_Kelayakan.Items.Add("SPM")
        ElseIf Cb_Kursus.SelectedIndex = 4 Then
            Panel1.Visible = True
            Bt4.Enabled = False
            Cb_Kelayakan.Enabled = False
            Cb_Kelayakan0.Enabled = False
            Cb_Thn_Kelayakan.Enabled = False
            Cb_Kpts1.Enabled = False
            Cb_Kpts2.Enabled = False
            Cb_Kpts3.Enabled = False
            Cb_Kpts4.Enabled = False
            cmd_Semak.Enabled = False
        ElseIf Cb_Kursus.SelectedIndex > 5 Then
            Panel1.Visible = True
            Bt4.Enabled = False
            Cb_Kelayakan.Enabled = False
            Cb_Kelayakan0.Enabled = False
            Cb_Thn_Kelayakan.Enabled = False
            Cb_Kpts1.Enabled = False
            Cb_Kpts2.Enabled = False
            Cb_Kpts3.Enabled = False
            Cb_Kpts4.Enabled = False
            cmd_Semak.Enabled = False
        End If

        If Cb_Kursus.SelectedIndex = 8 Then cmd_Cari.Visible = True Else cmd_Cari.Visible = False
    End Sub

    Protected Sub Cb_Kelayakan_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kelayakan.SelectedIndexChanged
        Cb_Kelayakan0.SelectedIndex = 0
        Cb_Thn_Kelayakan.SelectedIndex = 0
        Panel3.Enabled = False
        Cb_Sbj10.SelectedIndex = 0 : Tx_Kpts10.Text = ""
        Cb_Sbj11.SelectedIndex = 0 : Cb_Kpts11.SelectedIndex = 0
        
        Cb_Kpts1.Items.Clear()
        Cb_Kpts2.Items.Clear()
        Cb_Kpts3.Items.Clear()
        Cb_Kpts4.Items.Clear()

        If Cb_Kelayakan.SelectedItem.Text = "SRP" Then
            Panel1.Visible = True
            Bt4.Enabled = False
            Cb_Kpts1.Enabled = False
            Cb_Kpts2.Enabled = False
            Cb_Kpts3.Enabled = False
            Cb_Kpts4.Enabled = False
            cmd_Semak.Enabled = False
        End If
        If Cb_Kelayakan.SelectedItem.Text = "PMR" Then
            Panel1.Visible = False
            cmd_Semak.Enabled = True
            cmd_Semak.Visible = True
            Bt4.Enabled = True
            Cb_Kpts1.Enabled = True
            Cb_Kpts2.Enabled = True
            Cb_Kpts3.Enabled = True
            Cb_Kpts4.Enabled = True
        End If
        If Cb_Kelayakan.SelectedItem.Text = "SPM" Then
            Panel1.Visible = False
            cmd_Semak.Enabled = True
            cmd_Semak.Visible = True
            Bt4.Enabled = True
            Cb_Kpts1.Enabled = True
            Cb_Kpts2.Enabled = True
            Cb_Kpts3.Enabled = True
            Cb_Kpts4.Enabled = True
        End If
        If Cb_Kelayakan.SelectedItem.Text = "STPM" Then
            Panel1.Visible = False
            cmd_Semak.Enabled = True
            cmd_Semak.Visible = True
            Bt4.Enabled = True
            Cb_Kpts1.Enabled = True
            Cb_Kpts2.Enabled = True
            Cb_Kpts3.Enabled = True
            Cb_Kpts4.Enabled = True
        End If
    End Sub

    Protected Sub Cb_Thn_Kelayakan_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Thn_Kelayakan.SelectedIndexChanged
        If Cb_Kelayakan.SelectedItem.Text = "SRP" Then
            Panel1.Visible = True
            Bt4.Enabled = False
            Cb_Kpts1.Enabled = False
            Cb_Kpts2.Enabled = False
            Cb_Kpts3.Enabled = False
            Cb_Kpts4.Enabled = False
            cmd_Semak.Enabled = False
        End If
        If Cb_Kelayakan.SelectedItem.Text = "PMR" Then Isi_Subjek(1)
        If Cb_Kelayakan.SelectedItem.Text = "SPM" And CInt(Cb_Thn_Kelayakan.SelectedItem.Text) < 2009 Then
            Isi_Subjek(2)
        ElseIf Cb_Kelayakan.SelectedItem.Text = "SPM" And CInt(Cb_Thn_Kelayakan.SelectedItem.Text) >= 2009 Then
            Isi_Subjek(4)
        End If
        If Cb_Kelayakan.SelectedItem.Text = "STPM" Then Isi_Subjek(3)
    End Sub

    Protected Sub cmdHantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar.Click
        'If Chk_Subjek() = True Then Exit Sub
        If Cb_Kursus.SelectedValue = 8 Then If Chk_Staff() = True Then Exit Sub

        'Medan Mandatori...
        Dim X As String = ""
        ' Comment ori 06022013
        'If Tx_Nama.Text.Trim = "" Then X += "Nama, "
        'If Tx_NoKP.Text.Trim = "" Then X += "No. KP/Tentera/Pasport, "
        'If Cb_Warga.Text.Trim = "" Then X += "Warganegara, "
        'If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi Latihan, "
        'If Cb_Sesi_Bulan.Text.Trim = "" Or Cb_Sesi_Bulan.Text.Trim = "(BULAN)" Or Cb_Sesi_Tahun.Text.Trim = "" Or Cb_Sesi_Tahun.Text.Trim = "(TAHUN)" Then X += "Sesi Pengambilan, "
        'If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "
        'If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:  " & X) : Exit Sub

        'Add sponsorship mandotory field 06022013
        If Tx_Nama.Text.Trim = "" Then X += "Nama, "
        If Tx_NoKP.Text.Trim = "" Then X += "No. KP/Tentera/Pasport, "
        If Cb_Warga.Text.Trim = "" Then X += "Warganegara, "
        If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi Latihan, "
        If Cb_Tajaan.Text.Trim = "" Then X += "Tajaan, "
        If Cb_Sesi_Bulan.Text.Trim = "" Or Cb_Sesi_Bulan.Text.Trim = "(BULAN)" Or Cb_Sesi_Tahun.Text.Trim = "" Or Cb_Sesi_Tahun.Text.Trim = "(TAHUN)" Then X += "Sesi Pengambilan, "
        If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "
        If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:  " & X) : Exit Sub

        'Check No KP length
        X = ""
        With Cb_NoKP
            If .SelectedIndex = 0 Then If Tx_NoKP.Text.Length < 12 Then X = "lengkap"
            If .SelectedIndex = 0 Then If Not IsNumeric(Tx_NoKP.Text) Then X = "tepat"
        End With
        If X.Trim = "" Then  Else Msg(Me, "Maklumat No Kad Pengenalan tidak " & X) : Tx_NoKP.Focus() : Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Pelatih Ini Telah Ada!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()

        Dim SQL As String
        Try
            SQL = "insert pelatih (j_kursus, kelayakan, setaraf, tahun_lyk, nama, nokp, warganegara, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
            SQL += Cb_Kursus.SelectedItem.Value & ","
            If Cb_Kursus.SelectedValue = 4 Or Cb_Kursus.SelectedValue > 5 Then SQL += "''," Else SQL += "'" & Cb_Kelayakan.SelectedItem.Value & "',"
            SQL += "" & Cb_Kelayakan0.SelectedIndex & ","
            SQL += "" & Cb_Thn_Kelayakan.SelectedItem.Text & ","
            SQL += "'" & Apo(Tx_Nama.Text.Trim.ToUpper) & "',"
            SQL += "'" & Replace(Tx_NoKP.Text.Trim, "-", "") & "',"
            SQL += Cb_Warga.SelectedItem.Value & ","
            SQL += Cb_Jantina.SelectedIndex & ","
            SQL += Cb_Bangsa.SelectedIndex & ","
            SQL += Cb_Agama.SelectedIndex & ","
            SQL += Cb_Kahwin.SelectedIndex & ","
            SQL += "'" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "',"
            SQL += "'" & Tx_TP_Poskod.Text.Trim & "',"
            SQL += "'" & Tx_TP_Bandar.Text.Trim.ToUpper & "',"
            SQL += "'" & Cb_TP_Negeri.SelectedItem.Value & "',"
            SQL += "'" & Tx_Tel.Text.Trim & "',"
            SQL += "'" & Tx_Emel.Text.Trim & "',"
            SQL += "'" & Apo(Tx_W_Nama.Text.Trim.ToUpper) & "',"
            SQL += "'" & Apo(Tx_W_Alamat.Text.Trim.ToUpper) & "',"
            SQL += "'" & Tx_W_Poskod.Text.Trim & "',"
            SQL += "'" & Tx_W_Bandar.Text.Trim.ToUpper & "',"
            SQL += "'" & Cb_W_Negeri.SelectedItem.Value & "',"
            SQL += "'" & Cb_Negara.SelectedItem.Value & "',"
            SQL += "'" & Tx_W_Tel.Text.Trim & "',"
            SQL += "'" & Cb_Kolej.SelectedItem.Value & "',"
            SQL += "'" & Cb_Tajaan.SelectedItem.Value & "',"
            SQL += Cb_Sesi_Bulan.SelectedIndex & ","
            SQL += "'" & Cb_Sesi_Tahun.SelectedItem.Text & "',"
            SQL += Chk_Tkh(Tx_M_Latihan.Text) & ","
            SQL += SSemak(0) & ","
            SQL += SSemak(1) & ","
            SQL += SSemak(2) & ","
            SQL += SSemak(3) & ","
            SQL += SSemak(4) & ","
            SQL += SSemak(5) & ","
            SQL += SSemak(6) & ","
            SQL += SSemak(7) & ","
            SQL += "'" & Session("Id_PG") & "',"
            SQL += "getdate()"
            SQL += ")"
            'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal, "Daftar Pelatih")
            'Cmd.CommandText = SQL
            'Cmd.ExecuteNonQuery()

            If Cb_Kursus.SelectedValue = 4 Or Cb_Kursus.SelectedValue > 5 Then
            Else
                'SQL = ""
                If Cb_Kpts1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-1','" & Cb_Kpts1.SelectedItem.Text & "')" & vbCrLf
                If Cb_Kpts2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-2','" & Cb_Kpts2.SelectedItem.Text & "')" & vbCrLf
                If Cb_Kpts3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-3','" & Cb_Kpts3.SelectedItem.Text & "')" & vbCrLf
                If Cb_Kpts4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-4','" & Cb_Kpts4.SelectedItem.Text & "')" & vbCrLf
                If Cb_Kpts5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj5.SelectedItem.Value & "','" & Cb_Kpts5.SelectedItem.Text & "')" & vbCrLf
                If Cb_Kpts6.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj6.SelectedItem.Value & "','" & Cb_Kpts6.SelectedItem.Text & "')" & vbCrLf
                If Cb_Kpts7.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj7.SelectedItem.Value & "','" & Cb_Kpts7.SelectedItem.Text & "')" & vbCrLf
                If Cb_Kpts8.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj8.SelectedItem.Value & "','" & Cb_Kpts8.SelectedItem.Text & "')" & vbCrLf
                If Cb_Kpts9.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj9.SelectedItem.Value & "','" & Cb_Kpts9.SelectedItem.Text & "')" & vbCrLf
                If Cb_Sbj10.SelectedIndex = 1 Then
                    If Tx_Kpts10.Text <> "" Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-5','" & Tx_Kpts10.Text & "')" & vbCrLf
                End If
                If Cb_Sbj10.SelectedIndex = 2 Then
                    If Tx_Kpts10.Text <> "" Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-6','" & Tx_Kpts10.Text & "')" & vbCrLf
                End If                
                If Cb_Kpts11.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','15','" & Cb_Kpts11.SelectedItem.Text & "')" & vbCrLf
            End If
            If SQL = "" Then
            Else
                'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal, "Daftar Pelatih")
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
            End If

            Cn.Close()
            Session("Msg_Tajuk") = "Pendaftaran Pelatih Baru"
            Session("Msg_Isi") = "Rekod Telah Dihantar..."
            Response.Redirect("p0_Mesej.aspx")
            'Msg(Me, "Rekod Telah Dihantar...")
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
    End Sub

    Function SSemak(ByVal X As Int16)
        If chk_Semak.Items(X).Selected = True Then X = 1 Else X = 0
        Return X
    End Function

    Function Chk_Tkh(ByVal X As String)
        If IsDate(X) Then X = Format(CDate(X), "MM/dd/yyyy") : X = "'" & X & "'" Else X = "NULL"
        Return X
    End Function

    Function Chk_Subjek(ByVal cb As DropDownList)
        Chk_Subjek = False

        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Subjek = True : Exit Function
        If cb.SelectedItem.Text = "KIMIA" Then Chk_Subjek = True : Exit Function
        If cb.SelectedItem.Text = "FIZIK" Then Chk_Subjek = True : Exit Function

        Return Chk_Subjek
    End Function

    Function Chk_Staff()
        Dim A, T As Integer

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'PENDAFTARAN PENUH
        Cmd.CommandText = "SELECT nokp, apc_tahun FROM jt_penuh_apc where ret=0 and apc_tahun <= year(getdate()) and nokp='" & Tx_NoKP.Text & "' ORDER BY apc_tahun"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            A += 1
            T = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        If A > 4 And T = CInt(Year(Now)) Then
            Chk_Staff = False
        Else
            Msg(Me, "Pengalaman bekerja tidak melepasi syarat minimum untuk pendaftaran! Sila semak maklumat kelayakan.")
            Chk_Staff = True
        End If
        Return Chk_Staff
    End Function

    Protected Sub cmd_Semak_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Semak.Click
        Dim X As String = ""
        If Cb_Sesi_Bulan0.SelectedIndex < 1 Then X += "Sesi Pengambilan(Bulan), "
        If Cb_Sesi_Tahun0.SelectedIndex < 1 Then X += "Sesi Pengambilan(Tahun), "
        If Cb_Kursus.SelectedIndex < 1 Then X += "Kursus, "
        If Cb_Thn_Kelayakan.SelectedIndex < 1 Then X += "Tahun Kelayakan, "
        If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila pilih " & X) : Exit Sub
        If CInt(Cb_Thn_Kelayakan.SelectedItem.Text) > CInt(Cb_Sesi_Tahun0.SelectedItem.Text) Then Msg(Me, "Tahun Kelayakan Melebihi Tahun(Sesi Pengambilan)") : Exit Sub

        If Cb_Kursus.SelectedValue = 4 Then Panel1.Visible = True : Exit Sub

        If Cb_Kelayakan.Items.Count = 0 Then Exit Sub
        Dim L, AL, Total As Int16, semak As String = ""
        Dim AL2, Total2 As Double

        If Cb_Kelayakan.SelectedItem.Text = "SRP" Then
            L = 3
            If L > 2 Then semak = "L" Else semak = "G"
        End If

        If Cb_Kelayakan.SelectedItem.Text = "PMR" Then
            If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts5.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts6.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts7.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts8.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts9.SelectedIndex > 0 Then L = L + 1
            If L > 2 Then semak = "L" Else semak = "G"
        End If

        If Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan0.SelectedIndex = 0 Then
            If CInt(Cb_Thn_Kelayakan.SelectedItem.Text) <= 2009 Then
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 7 Then L = L + 1
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 7 Then L = L + 1
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 7 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 7 Then AL = AL + 1

                If CInt(Cb_Sesi_Tahun0.SelectedItem.Text) > 2010 Then
                    If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 7 Then
                        If Chk_Subjek(Cb_Sbj5) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 7 Then
                        If Chk_Subjek(Cb_Sbj6) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 7 Then
                        If Chk_Subjek(Cb_Sbj7) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 7 Then
                        If Chk_Subjek(Cb_Sbj8) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 7 Then
                        If Chk_Subjek(Cb_Sbj9) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If AL = 0 Then semak = "G" Else Total = L + AL
                    If Total > 6 Then semak = "L"

                ElseIf CInt(Cb_Sesi_Tahun0.SelectedItem.Text) = 2010 Then
                    If Cb_Sesi_Bulan0.SelectedIndex > 7 Then
                        If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 7 Then
                            If Chk_Subjek(Cb_Sbj5) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 7 Then
                            If Chk_Subjek(Cb_Sbj6) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 7 Then
                            If Chk_Subjek(Cb_Sbj7) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 7 Then
                            If Chk_Subjek(Cb_Sbj8) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 7 Then
                            If Chk_Subjek(Cb_Sbj9) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If AL = 0 Then semak = "G" Else Total = L + AL
                        If Total > 6 Then semak = "L"
                    Else
                        If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 9 Then L = L + 1
                        If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 9 Then L = L + 1
                        If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 9 Then L = L + 1
                        If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 9 Then L = L + 1
                        If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 9 Then L = L + 1
                        If L > 2 Then semak = "L"
                    End If

                ElseIf CInt(Cb_Sesi_Tahun0.SelectedItem.Text) < 2010 Then
                    If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 9 Then L = L + 1
                    If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 9 Then L = L + 1
                    If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 9 Then L = L + 1
                    If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 9 Then L = L + 1
                    If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 9 Then L = L + 1
                    If L > 2 Then semak = "L"
                End If
                '----------
            Else
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex <= 7 Then L = L + 1
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex <= 7 Then L = L + 1
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex <= 7 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex <= 7 Then AL = AL + 1

                If CInt(Cb_Sesi_Tahun0.SelectedItem.Text) > 2010 Then
                    If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex <= 7 Then
                        If Chk_Subjek(Cb_Sbj5) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex <= 7 Then
                        If Chk_Subjek(Cb_Sbj6) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex <= 7 Then
                        If Chk_Subjek(Cb_Sbj7) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex <= 7 Then
                        If Chk_Subjek(Cb_Sbj8) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex <= 7 Then
                        If Chk_Subjek(Cb_Sbj9) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If AL = 0 Then semak = "G" Else Total = L + AL
                    If Total > 6 Then semak = "L"

                ElseIf CInt(Cb_Sesi_Tahun0.SelectedItem.Text) = 2010 Then
                    If Cb_Sesi_Bulan0.SelectedIndex > 7 Then
                        If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex <= 7 Then
                            If Chk_Subjek(Cb_Sbj5) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex <= 7 Then
                            If Chk_Subjek(Cb_Sbj6) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex <= 7 Then
                            If Chk_Subjek(Cb_Sbj7) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex <= 7 Then
                            If Chk_Subjek(Cb_Sbj8) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex <= 7 Then
                            If Chk_Subjek(Cb_Sbj9) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If AL = 0 Then semak = "G" Else Total = L + AL
                        If Total > 6 Then semak = "L"
                    Else
                        If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex <= 9 Then L = L + 1
                        If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex <= 9 Then L = L + 1
                        If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex <= 9 Then L = L + 1
                        If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex <= 9 Then L = L + 1
                        If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex <= 9 Then L = L + 1
                        If L > 2 Then semak = "L"
                    End If

                ElseIf CInt(Cb_Sesi_Tahun0.SelectedItem.Text) < 2010 Then
                    If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex <= 9 Then L = L + 1
                    If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex <= 9 Then L = L + 1
                    If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex <= 9 Then L = L + 1
                    If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex <= 9 Then L = L + 1
                    If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex <= 9 Then L = L + 1
                    If L > 2 Then semak = "L"
                End If
            End If
        End If

        If Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan0.SelectedIndex = 1 Then 'spm setaraf (polisi baru shj)
            If CInt(Cb_Thn_Kelayakan.SelectedItem.Text) <= 2009 Then
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 7 Then L = L + 1
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 7 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 7 Then AL = AL + 1
                If CInt(Cb_Sesi_Tahun0.SelectedItem.Text) > 2010 Then
                    If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 7 Then
                        If Chk_Subjek(Cb_Sbj5) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 7 Then
                        If Chk_Subjek(Cb_Sbj6) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 7 Then
                        If Chk_Subjek(Cb_Sbj7) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 7 Then
                        If Chk_Subjek(Cb_Sbj8) = True Then AL = AL + 1 Else L = L + 1
                    End If
                    If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 7 Then
                        If Chk_Subjek(Cb_Sbj9) = True Then AL = AL + 1 Else L = L + 1
                    End If
                ElseIf CInt(Cb_Sesi_Tahun0.SelectedItem.Text) = 2010 Then
                    If Cb_Sesi_Bulan0.SelectedIndex > 7 Then
                        If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 7 Then
                            If Chk_Subjek(Cb_Sbj5) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 7 Then
                            If Chk_Subjek(Cb_Sbj6) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 7 Then
                            If Chk_Subjek(Cb_Sbj7) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 7 Then
                            If Chk_Subjek(Cb_Sbj8) = True Then AL = AL + 1 Else L = L + 1
                        End If
                        If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 7 Then
                            If Chk_Subjek(Cb_Sbj9) = True Then AL = AL + 1 Else L = L + 1
                        End If
                    End If
                Else
                    semak = "G"
                End If
                '-------------------------------
            Else
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex <= 7 Then L = L + 1
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex <= 7 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex <= 7 Then AL = AL + 1
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex <= 7 Then
                    If Chk_Subjek(Cb_Sbj5) = True Then AL = AL + 1 Else L = L + 1
                End If
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex <= 7 Then
                    If Chk_Subjek(Cb_Sbj6) = True Then AL = AL + 1 Else L = L + 1
                End If
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex <= 7 Then
                    If Chk_Subjek(Cb_Sbj7) = True Then AL = AL + 1 Else L = L + 1
                End If
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex <= 7 Then
                    If Chk_Subjek(Cb_Sbj8) = True Then AL = AL + 1 Else L = L + 1
                End If
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex <= 7 Then
                    If Chk_Subjek(Cb_Sbj9) = True Then AL = AL + 1 Else L = L + 1
                End If
            End If

            If AL = 0 Then semak = "G" Else Total = L + AL
            If Total > 4 Then semak = "L"
        End If

        If Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan0.SelectedIndex = 0 Then
            If CInt(Cb_Sesi_Tahun0.SelectedItem.Text) > 2010 Then
                If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts1.SelectedValue)
                If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts2.SelectedValue)
                If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts3.SelectedValue)
                If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts4.SelectedValue)
                If Cb_Kpts5.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts5.SelectedValue)
                If Cb_Kpts6.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts6.SelectedValue)
                If Cb_Kpts7.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts7.SelectedValue)
                If Cb_Kpts8.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts8.SelectedValue)
                If Cb_Kpts9.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts9.SelectedValue)
                Total2 = AL2 / L
                If Total2 >= 2.5 Then semak = "L" Else semak = "G"

            ElseIf CInt(Cb_Sesi_Tahun0.SelectedItem.Text) = 2010 Then
                If Cb_Sesi_Bulan0.SelectedIndex > 7 Then
                    If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts1.SelectedValue)
                    If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts2.SelectedValue)
                    If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts3.SelectedValue)
                    If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts4.SelectedValue)
                    If Cb_Kpts5.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts5.SelectedValue)
                    If Cb_Kpts6.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts6.SelectedValue)
                    If Cb_Kpts7.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts7.SelectedValue)
                    If Cb_Kpts8.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts8.SelectedValue)
                    If Cb_Kpts9.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts9.SelectedValue)
                    Total2 = AL2 / L
                    If Total2 >= 2.5 Then semak = "L" Else semak = "G"
                Else
                    If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1
                    If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1
                    If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1
                    If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1
                    If Cb_Kpts5.SelectedIndex > 0 Then L = L + 1
                    If Cb_Kpts6.SelectedIndex > 0 Then L = L + 1
                    If Cb_Kpts7.SelectedIndex > 0 Then L = L + 1
                    If Cb_Kpts8.SelectedIndex > 0 Then L = L + 1
                    If Cb_Kpts9.SelectedIndex > 0 Then L = L + 1
                    If L > 2 Then semak = "L" Else semak = "G"
                End If
            ElseIf CInt(Cb_Sesi_Tahun0.SelectedItem.Text) < 2010 Then
                If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts5.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 Then L = L + 1
                If L > 2 Then semak = "L" Else semak = "G"
            End If
        End If

        If Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan0.SelectedIndex = 1 Then
            If CInt(Cb_Sesi_Tahun0.SelectedItem.Text) > 2010 Then
                If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts1.SelectedValue)
                If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts2.SelectedValue)
                If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts3.SelectedValue)
                If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts4.SelectedValue)
                If Cb_Kpts5.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts5.SelectedValue)
                If Cb_Kpts6.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts6.SelectedValue)
                If Cb_Kpts7.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts7.SelectedValue)
                If Cb_Kpts8.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts8.SelectedValue)
                If Cb_Kpts9.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts9.SelectedValue)
                Total2 = AL2 / L
                If Total2 >= 2.5 Then semak = "L" Else semak = "G"

                If Cb_Sbj10.SelectedIndex = 1 Then
                    If Tx_Kpts10.Text >= 2.5 Then semak = "L" Else semak = "G" 'matrik
                End If
                If Cb_Sbj10.SelectedIndex = 2 Then
                    If Tx_Kpts10.Text >= 3.0 Then semak = "L" Else semak = "G"
                End If
            End If
            If CInt(Cb_Sesi_Tahun0.SelectedItem.Text) = 2010 Then
                If Cb_Sesi_Bulan0.SelectedIndex > 7 Then
                    If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts1.SelectedValue)
                    If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts2.SelectedValue)
                    If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts3.SelectedValue)
                    If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts4.SelectedValue)
                    If Cb_Kpts5.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts5.SelectedValue)
                    If Cb_Kpts6.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts6.SelectedValue)
                    If Cb_Kpts7.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts7.SelectedValue)
                    If Cb_Kpts8.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts8.SelectedValue)
                    If Cb_Kpts9.SelectedIndex > 0 Then L = L + 1 : AL2 = AL2 + CDbl(Cb_Kpts9.SelectedValue)
                    Total2 = AL2 / L
                    If Total2 >= 2.5 Then semak = "L" Else semak = "G"

                    If Cb_Sbj10.SelectedIndex = 1 Then
                        If Tx_Kpts10.Text >= 2.5 Then semak = "L" Else semak = "G"
                    End If
                    If Cb_Sbj10.SelectedIndex = 2 Then
                        If Tx_Kpts10.Text >= 3.0 Then semak = "L" Else semak = "G"
                    End If
                End If
            End If
        End If

        If Cb_Kursus.SelectedIndex = 4 Then
            L = 2
            If Cb_Sbj5.SelectedItem.Text = "MUET" Then L = L + 1
            If Cb_Sbj6.SelectedItem.Text = "MUET" Then L = L + 1
            If Cb_Sbj7.SelectedItem.Text = "MUET" Then L = L + 1
            If Cb_Sbj8.SelectedItem.Text = "MUET" Then L = L + 1
            If Cb_Sbj9.SelectedItem.Text = "MUET" Then L = L + 1
        End If

        If Panel3.Enabled = True Then
            If Cb_Kpts11.SelectedIndex = 0 Then Msg(Me, "Sila Pilih Tahap MUET") : Exit Sub
        End If

        If semak = "L" Then
            Panel1.Visible = True
            cmd_Semak.Visible = False
            Bt4.Enabled = False
            Bt5.Enabled = False
            Bt6.Enabled = False
            Bt7.Enabled = False
            Bt8.Enabled = False
            Cb_Sbj5.Visible = False : Cb_Sbj5x.Visible = True : Cb_Sbj5x.Text = Cb_Sbj5.SelectedItem.Text
            Cb_Sbj6.Visible = False : Cb_Sbj6x.Visible = True : Cb_Sbj6x.Text = Cb_Sbj6.SelectedItem.Text
            Cb_Sbj7.Visible = False : Cb_Sbj7x.Visible = True : Cb_Sbj7x.Text = Cb_Sbj7.SelectedItem.Text
            Cb_Sbj8.Visible = False : Cb_Sbj8x.Visible = True : Cb_Sbj8x.Text = Cb_Sbj8.SelectedItem.Text
            Cb_Sbj9.Visible = False : Cb_Sbj9x.Visible = True : Cb_Sbj9x.Text = Cb_Sbj9.SelectedItem.Text
            Cb_Sbj10.Visible = False : Cb_Sbj10x.Visible = True : Cb_Sbj10x.Text = Cb_Sbj10.SelectedItem.Text
            Cb_Sbj11.Visible = False : Cb_Sbj11x.Visible = True : Cb_Sbj11x.Text = Cb_Sbj11.SelectedItem.Text

            Cb_Kelayakan.Enabled = False
            Cb_Kelayakan0.Enabled = False
            Cb_Thn_Kelayakan.Enabled = False
            Cb_Kpts1.Enabled = False
            Cb_Kpts2.Enabled = False
            Cb_Kpts3.Enabled = False
            Cb_Kpts4.Enabled = False
            Cb_Kpts5.Enabled = False
            Cb_Kpts6.Enabled = False
            Cb_Kpts7.Enabled = False
            Cb_Kpts8.Enabled = False
            Cb_Kpts9.Enabled = False
            Tx_Kpts10.Enabled = False
            Cb_Kpts11.Enabled = False
        Else
            Msg(Me, "Kelayakan pelatih tidak melepasi syarat minimum untuk pendaftaran! Sila semak maklumat kelayakan.")
            Exit Sub
        End If
        'If Chk_Subjek() = True Then Exit Sub        
    End Sub

    Protected Sub Cb_NoKP_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_NoKP.SelectedIndexChanged
        Tx_NoKP.Text = ""
        With Cb_NoKP
            If .SelectedIndex = 0 Then Tx_NoKP.MaxLength = 12
            If .SelectedIndex = 1 Then Tx_NoKP.MaxLength = 8
            If .SelectedIndex = 2 Then Tx_NoKP.MaxLength = 15
            If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        End With
    End Sub

    Protected Sub RadioButtonList1_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles RadioButtonList1.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ WHERE JENIS=" & RadioButtonList1.SelectedValue & " ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Pelatih Ini Telah Ada!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()

        Try
            Cmd.CommandText = "select * from jt_penuh where nokp = '" & Tx_NoKP.Text & "'"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                If Not IsDBNull(Rdr("nama")) Then Tx_Nama.Text = Rdr("nama")
                If Not IsDBNull(Rdr("warganegara")) Then If Rdr("warganegara") > 0 Then Cb_Warga.SelectedValue = Rdr("warganegara")
                If Not IsDBNull(Rdr("jantina")) Then If Rdr("jantina") > 0 Then Cb_Jantina.SelectedValue = Rdr("jantina")
                If Not IsDBNull(Rdr("bangsa")) Then If Rdr("bangsa") > 0 Then Cb_Bangsa.SelectedValue = Rdr("bangsa")
                If Not IsDBNull(Rdr("agama")) Then If Rdr("agama") > 0 Then Cb_Agama.SelectedValue = Rdr("agama")
                If Not IsDBNull(Rdr("t_kahwin")) Then If Rdr("t_kahwin") > 0 Then Cb_Kahwin.SelectedValue = Rdr("t_kahwin")
                If Not IsDBNull(Rdr("tp_alamat")) Then Tx_TP_Alamat.Text = Rdr("tp_alamat")
                If Not IsDBNull(Rdr("tp_poskod")) Then Tx_TP_Poskod.Text = Rdr("tp_poskod")
                If Not IsDBNull(Rdr("tp_bandar")) Then Tx_TP_Bandar.Text = Rdr("tp_bandar")
                If Not IsDBNull(Rdr("tp_negeri")) Then If Rdr("tp_negeri") > 0 Then Cb_TP_Negeri.SelectedValue = Rdr("tp_negeri")
                If Not IsDBNull(Rdr("tel_hp")) Then Tx_Tel.Text = Rdr("tel_hp")
                If Not IsDBNull(Rdr("emel")) Then Tx_Emel.Text = Rdr("emel")
            Else
                Msg(Me, "Rekod Tidak Wujud")
            End If
            Rdr.Close()
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
        Cn.Close()
    End Sub

    Protected Sub Cb_Kelayakan0_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kelayakan0.SelectedIndexChanged
        If Cb_Kelayakan0.SelectedIndex = 0 Then
            Panel3.Enabled = False
            Cb_Sbj10.SelectedIndex = 0 : Tx_Kpts10.Text = ""
            Cb_Sbj11.SelectedIndex = 0 : Cb_Kpts11.SelectedIndex = 0
        Else
            Panel3.Enabled = True
            Cb_Sbj10.SelectedIndex = 0 : Tx_Kpts10.Text = ""
            Cb_Sbj11.SelectedIndex = 0 : Cb_Kpts11.SelectedIndex = 0
        End If
    End Sub

    Protected Sub Cb_Sesi_Bulan0_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Sesi_Bulan0.SelectedIndexChanged
        Cb_Sesi_Bulan.SelectedIndex = Cb_Sesi_Bulan0.SelectedIndex
    End Sub

    Protected Sub Cb_Sesi_Tahun0_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Sesi_Tahun0.SelectedIndexChanged
        Cb_Sesi_Tahun.SelectedIndex = Cb_Sesi_Tahun0.SelectedIndex
    End Sub
End Class