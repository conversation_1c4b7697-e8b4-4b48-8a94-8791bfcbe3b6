﻿

Partial Public Class Vw_Pelatih
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Button1.Click

        Response.Clear()
        Response.Charset = ""
        Response.ContentType = "application/msword"
        'Response.ContentType = "application/msexcel"

        Dim strFileName As String = "GenerateDocument" + ".doc"
        Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        'StringBuilder()
        Dim IsiHTML As New StringBuilder

        'IsiHTML.Append("<h1 title='Heading' align='Center' style='font-family: arial; font-size:8pt; color:black;'><u>Tajuk Sini...</u></h1>".ToString())
        'IsiHTML.Append("<br>".ToString())

        '' Table start
        'IsiHTML.Append(TextBox1.Text)

        '' First Row Data
        'IsiHTML.Append("<tr>".ToString())
        'IsiHTML.Append("<td style='width:100px'>a</td>".ToString())
        'IsiHTML.Append("<td style='width:100px'>b</td>".ToString())
        'IsiHTML.Append("<td style='width:100px'>c</td>".ToString())
        'IsiHTML.Append("</tr>".ToString())

        'Table End
        IsiHTML.Append(TextBox1.Text)


        '' Second Row Data
        'IsiHTML.Append("<tr>".ToString())
        'IsiHTML.Append("<td style='width:100px'>d</td>".ToString())
        'IsiHTML.Append("<td style='width:100px'>e</td>".ToString())
        'IsiHTML.Append("<td style='width:100px'>f</td>".ToString())
        'IsiHTML.Append("</tr>".ToString())

        ''Table End
        'IsiHTML.Append("</table>".ToString())

        'IsiHTML.Append("<br><br>".ToString())
        'IsiHTML.Append("<p align='Center'>Note: This is adynamically generated word document</p>".ToString())
        Response.Write(IsiHTML)
        Response.End()
        Response.Flush()
    End Sub
End Class