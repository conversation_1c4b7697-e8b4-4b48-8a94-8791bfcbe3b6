﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_TPC_Lulus_PJ
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'TAHUN APC
        For i = Now.Year To Now.Year - 2 Step -1
            Cb_Tahun.Items.Add(i)
        Next

        'Pilihan
        With Cb_Pilihan
            .Items.Clear()
            .Items.Add("PERMOHONAN")
            .Items.Item(.Items.Count - 1).Value = "1"
            .Items.Add("")
            .Items.Item(.Items.Count - 1).Value = "2"
        End With
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim tahun As Integer = Cb_Tahun.Text
        Dim pilihan As Integer = Cb_Pilihan.SelectedValue
        Dim A, B, C, D, T As Long
        <PERSON>, Tajuk2 As String

        Tajuk = "Laporan Statistik Permohonan Kelulusan Pendaftaran TPC bagi Pengambilan Jururawat Terlatih Warganegara Asing"
        Tajuk2 = "Sebagai Pengajar Jururawat di Kolej/Universiti pada Tahun " + Cb_Tahun.Text

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "laporan" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:100%;'>"
        Header += "<tr>"
        Header += "    <td colspan='6' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='6' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>MAJIKAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>BIL. PERMOHONAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>BIL. DIBATALKAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>BIL. DILULUSKAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>BIL. TIDAK DILULUSKAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>BAKI</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet


        'NEGERI
        Cmd.CommandText = "SELECT Id_AMALAN, Dc_AMALAN FROM PN_TPT_AMALAN WHERE ID_AMALAN IN (SELECT TPT_AMALAN FROM JT_TPC_MAJIKAN WHERE NOKP IN (SELECT NOKP FROM JT_TPC WHERE J_DAFTAR=3)AND NOKP IN(SELECT NOKP FROM JT_TPC_TPC WHERE TPC_TAHUN=" & tahun & "))ORDER BY Dc_AMALAN"
        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "Amalan")
        Rdr.Close()

        Header = ""
        For Each dr As DataRow In Ds.Tables(0).Rows
            Header += "<tr>"
            Header += "    <td>" + dr.Item(1) + "</td> "

            If pilihan = 1 Then
                A = 0 : B = 0 : C = 0 : D = 0 : T = 0
                Cmd.CommandText = "select count(nokp) from jt_tpc where j_daftar=3 and nokp in (select nokp from jt_tpc_majikan where tpt_amalan =" & dr.Item(0) & ") and nokp in (select nokp from jt_tpc_tpc where tpc_tahun =" & tahun & ")"
                Rdr = Cmd.ExecuteReader()
                If Rdr.Read Then A = Rdr(0)
                Rdr.Close()
                Cmd.CommandText = "select count(nokp) from jt_tpc where j_daftar=3 and log_status=6 and nokp in (select nokp from jt_tpc_majikan where tpt_amalan =" & dr.Item(0) & ") and nokp in (select nokp from jt_tpc_tpc where tpc_tahun =" & tahun & ")"
                Rdr = Cmd.ExecuteReader()
                If Rdr.Read Then B = Rdr(0)
                Rdr.Close()
                Cmd.CommandText = "select count(nokp) from jt_tpc where j_daftar=3 and log_status=4 and nokp in (select nokp from jt_tpc_majikan where tpt_amalan =" & dr.Item(0) & ") and nokp in (select nokp from jt_tpc_tpc where tpc_tahun =" & tahun & ")"
                Rdr = Cmd.ExecuteReader()
                If Rdr.Read Then C = Rdr(0)
                Rdr.Close()
                Cmd.CommandText = "select count(nokp) from jt_tpc where j_daftar=3 and log_status=5 and nokp in (select nokp from jt_tpc_majikan where tpt_amalan =" & dr.Item(0) & ") and nokp in (select nokp from jt_tpc_tpc where tpc_tahun =" & tahun & ")"
                Rdr = Cmd.ExecuteReader()
                If Rdr.Read Then D = Rdr(0)
                Rdr.Close()
                T = A - B - C - D
                Header += "    <td>" & A & "</td><td>" & B & "</td><td>" & C & "</td><td>" & D & "</td><td>" & T & "</td>"
            End If

            Header += "</tr>"
        Next
        Cn.Close()
        Response.Write(Header)
        Response.Write("</table>")

        Response.End()
        Response.Flush()

    End Sub
End Class