﻿Public Partial Class WebForm48
    Inherits System.Web.UI.Page

    Public x As String

   

    Public Sub Surat_KPT_Baru()
        x = ""
        x += Header_Surat(1, 1)

        'Rujukan
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 12pt;'>"
        x += "<tr>"
        x += "<td style='width:58%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Ruj. Tuan : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'> </td>"
        x += "</tr><tr>"
        x += "<td style='width:58%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Ruj. Kami : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'> KKM 87/A3/1/133() </td></td>"
        x += "</tr><tr>"
        x += "<td style='width:58%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Tarikh &nbsp;&nbsp;&nbsp;&nbsp; : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'> " + Tarikh(1) + " </td>"
        x += "</tr></table>"

        'Penerima
        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt;'>"
        x += "<br/>Sektor Pengurusan IPTS"
        x += "<br/>Jabatan Pengajian Tinggi"
        x += "<br/>Kementerian Pengajian Tinggi Malaysia"
        x += "<br/>Aras Bawah & Aras 5"
        x += "<br/>Pusat Pentadbiran Kerajaan Persekutuan"
        x += "<br/>62550 Putrajaya."
        x += "<br/>(u/p: Unit Expatriate Pelajar Antarabangsa)"
        x += "</div>"

        x += "<br/>"
        x += "<br/>"
        x += "<div  style='font-family: Arial; font-size: 12pt;'>Tuan,"
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'><b>KEPUTUSAN MESYUARAT PENGAMBILAN PERKHIDMATAN JURURAWAT TERLATIH WARGANEGARA ASING SEBAGAI INSTRUKTOR KLINIKAL DAN PENGAJAR JURURAWAT."
        x += "</b></div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'>Dengan segala hormatnya merujuk perkara di atas."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "2.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Dengan ini dimaklumkan bahawa Mesyuarat Jawatankuasa Kelulusan Pengambilan Perkhidmatan Jururawat Terlatih Warganegara Asing sebagai Instruktor Klinikal dan Pengajar Jururawat telah diadakan pada -------------- "
        x += "Berikut adalah senarai keputusan nama Instruktor Klinikal dan Pengajar Jururawat Warganegara Asing mengikut institusi yang memohon perkhidmatan mereka seperti di lampiran 1."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "3.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Sehubungan dengan itu, Jururawat Warganegara Asing yang diluluskan sebagai Instruktor Klinikal dan Pengajar Jururawat perlu melengkapkan perkara berikut :-"
        x += "</div>"

        x += "<br/>"
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 12pt;'>"
        
        x += "<tr><td style='width:9.5%;'></td><td style='vertical-align:top;' >3.1</td><td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "Majikan perlu <b>memperolehi Perakuan Pengamalan Sementara (TPC)</b> dari Lembaga Jururawat Malaysia (LJM)  terlebih dahulu <b>sebelum</b> memohon <b>kelulusan pas pengajian</b> dari pihak tuan. Selepas itu, majikan perlu memohon "
        x += "<b>kelulusan <i>Multiple Entry Visa</i></b> dari Jabatan Imigresen.</td></tr>"
        x += "<tr></tr>"
        x += "<tr></tr>"
        x += "<tr><td style='width:9.5%;'></td><td style='vertical-align:top;' >3.2</td><td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "Setelah majikan <b>mendapat TPC, kelulusan pas pengajian </b>dan <b><i>Multiple Entry Visa</i></b> dari Jabatan Imigresen Malaysia, Jururawat Terlatih Warganegara Asing yang diluluskan sebagai Instruktor Klinikal dan Pengajar Jururawat tersebut "
        x += "<b>dibenarkan berkhidmat</b> di Kolej / Universiti yang mengambil perkhidmatan mereka.</td></tr>"
        x += "</table>"
        x += "<br/>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "4.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Tawaran kelulusan TPC ini hanya sah laku dalam tempoh <b>enam (6) bulan</b> dari tarikh kelulusan dan <b>tamat pada -------------.</b> Permohonan Perakuan Pengamalan Sementara (TPC) perlu dibuat sebelum "
        x += "tamat tempoh tawaran kelulusan. <b>Kelewatan / kegagalan majikan berbuat demikian menyebabkan kelulusan TPC terbatal dan permohonan pendaftaran perakuan pengamalan sementara (TPC) perlu dibuat semula.</b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "Sekian, terima kasih."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "<b> &quot;BERKHIDMAT UNTUK NEGARA&quot; </b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "Saya yang menurut perintah,"
        x += "</div>"

        x += "<br>"
        x += "<br>"
        x += "</br>"


        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "<b> (DATO' HJH. FATHILAH BINTI HJ. ABD. WAHAB) </b>"
        x += "<br>Pendaftar,"
        x += "<br>Lembaga Jururawat Malaysia,"
        x += "<br>Kementerian Kesihatan Malaysia."

        x += "<br/><br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "<br>s.k-"
        x += "<br>"
        x += "<br>"
        x += "<br/>1.&nbsp;&nbsp; Ketua Pengarah"
        x += "<br/> &nbsp;&nbsp;&nbsp;&nbsp;   Jabatan Imigresen Malaysia"
        x += "<br/>  &nbsp;&nbsp;&nbsp;&nbsp;  Tingkat 3, (Podium Blok 2G4), Presint 2"
        x += "<br/> &nbsp;&nbsp;&nbsp;&nbsp;   Pusat Pentadbiran Kerajaan Persekutuan"
        x += "<br/> &nbsp;&nbsp;&nbsp;&nbsp;   62550 Putrajaya."
        x += "<br/> &nbsp;&nbsp;&nbsp;&nbsp;   (u/p: Pengarah Bahagian Pas Penggajian)"
        x += "<br>"
        x += "<br>"
        x += "<br>2. &nbsp;&nbsp;  Fail Program"

        x += "</div>"

        x += Footer_Surat()

        Dim fileName As String = "Surat.doc"
        Response.Clear()
        Response.Buffer = True
        ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)
    End Sub


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        Surat_KPT_Baru()
    End Sub
End Class