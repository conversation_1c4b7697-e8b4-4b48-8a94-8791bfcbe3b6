﻿Imports System.Data.OleDb
Imports CrystalDecisions.Shared
'Imports CrystalDecisions.CrystalReports.Engine
'Imports System.Data.DataSet

Partial Public Class p6_Laporan
    Inherits System.Web.UI.Page



    'Sub setTblCon(ByRef rep As CrystalDecisions.Web.Report)
    '    Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
    '    Dim Tloginfo As CrystalDecisions.Shared.TableLogOnInfo
    '    For Each tbCurrent In rep.Database.Tables
    '        Tloginfo = tbCurrent.LogOnInfo
    '        With Tloginfo.ConnectionInfo

    '            .DatabaseName = ConfigurationManager.AppSettings("dB")
    '            .UserID = "sa"
    '            .Password = "aljaroom5621"
    '            .ServerName = ConfigurationManager.AppSettings("IP_App")

    '        End With
    '        tbCurrent.ApplyLogOnInfo(Tloginfo)
    '    Next
    'End Sub
    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim tbCurrent As CrystalDecisions.CrystalReports.Engine.Table
        Dim Tloginfo As CrystalDecisions.Shared.TableLogOnInfo

        Select Case Session("Lpr_Nama")
            Case "Calon_Baru_L"
                Dim report As Calon_Baru_L = New Calon_Baru_L
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Calon_Baru_G"
                Dim report As Calon_Baru_G = New Calon_Baru_G
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Calon_Markah_Tinggi"
                Dim report As Calon_Markah_Tinggi = New Calon_Markah_Tinggi
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Calon_Markah_Rendah"
                Dim report As Calon_Markah_Rendah = New Calon_Markah_Rendah
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Calon_Ulang_L"
                Dim report As Calon_Ulang_L = New Calon_Ulang_L
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Calon_Ulang_G"
                Dim report As Calon_Ulang_G = New Calon_Ulang_G
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Calon_Tumpang"
                Dim report As Calon_Tumpang = New Calon_Tumpang
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Stat_XM"
                Dim report As Stat_XM = New Stat_XM
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Daftar_Penuh"
                Dim report As Daftar_Penuh = New Daftar_Penuh
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Daftar_Penuh_JM"
                Dim report As Daftar_Penuh_JM = New Daftar_Penuh_JM
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Daftar_Penuh_PJ"
                Dim report As Daftar_Penuh_PJ = New Daftar_Penuh_PJ
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Daftar_Penuh_KB1"
                Dim report As Daftar_Penuh_KB1 = New Daftar_Penuh_KB1
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Gazet_KB1"
                Dim report As Gazet_KB1 = New Gazet_KB1
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Gazet_JM"
                Dim report As Gazet_JM = New Gazet_JM
                report.SetParameterValue(0, Session("Var_0"))
                report.SetParameterValue(1, Session("Var_1"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621", ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"), True)
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
            Case "Stat_APC"
                Dim report As Stat_APC = New Stat_APC
                report.SetParameterValue(0, Session("Var_2"))
                report.SetParameterValue(1, Session("Var_3"))
                report.SetParameterValue(2, "DARI " & Format(CDate(Session("Var_2")), "d.M.yyyy") & " SEHINGGA " & Format(CDate(Session("Var_3")), "d.M.yyyy") & "  (" & Session("Var_4") & ")")
                report.SetParameterValue(3, Session("Var_1"))
                report.SetParameterValue(4, Session("Var_5"))
                report.SetParameterValue(5, Session("Var_6"))

                For Each tbCurrent In report.Database.Tables
                    Tloginfo = tbCurrent.LogOnInfo
                    With Tloginfo.ConnectionInfo

                        .DatabaseName = ConfigurationManager.AppSettings("dB")
                        .UserID = "sa"
                        .Password = "aljaroom5621"
                        .ServerName = ConfigurationManager.AppSettings("IP_App")

                    End With
                    tbCurrent.ApplyLogOnInfo(Tloginfo)
                Next
                'report.SetDatabaseLogon("sa", "aljaroom5621") ',  ConfigurationManager.AppSettings("IP_App"), ConfigurationManager.AppSettings("dB"))
                report.ExportToHttpResponse(ExportFormatType.PortableDocFormat, Response, False, "ExportedReport")
        End Select
    End Sub
End Class