﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_XM_Analys
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Laporan", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'KATEGORI
        Cb_Kategori.Items.Clear()
        Cb_Kategori.Items.Add("(SEMUA SEKTOR)")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = ""
        Cb_Kategori.Items.Add("KERAJAAN")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "1"
        Cb_Kategori.Items.Add("IPTA")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "3"
        Cb_Kategori.Items.Add("SWASTA")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "2"

        'KOLEJ
        Cmd.CommandText = "select dc_kolej, id_kolej from pn_kolej where (jenis < 3) order by dc_kolej"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("(SEMUA KOLEJ)")
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        'KURSUS
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("(SEMUA JENIS PENDAFTARAN)")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = ""
        Cb_Kursus.Items.Add("JURURAWAT BERDAFTAR")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "1"
        Cb_Kursus.Items.Add("JURURAWAT MASYARAKAT")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "2"
        Cb_Kursus.Items.Add("PENOLONG JURURAWAT")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "3"
        Cb_Kursus.Items.Add("KEBIDANAN I")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "4"

        'KERTAS
        With Cb_Kertas
            .Items.Clear()
            .Items.Add("OBJEKTIF I")
            .Items.Item(.Items.Count - 1).Value = "1"
            .Items.Add("OBJEKTIF II")
            .Items.Item(.Items.Count - 1).Value = "2"
        End With
    End Sub

    Protected Sub Cb_Kursus_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kursus.SelectedIndexChanged
        If Cb_Kursus.SelectedIndex < 4 Then Cb_Kertas.Enabled = False Else Cb_Kertas.Enabled = True
    End Sub

    Protected Sub Cb_Kategori_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kategori.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Dim SQL As String = ""

        'update by azidi 13/08/2012
        'If Cb_Kategori.SelectedIndex < 1 Then SQL = "< 3" Else SQL = "=" & Cb_Kategori.SelectedValue
        If Cb_Kategori.SelectedIndex < 1 Then
            SQL = "jenis< 3"
        ElseIf Cb_Kategori.SelectedIndex = 1 Then
            SQL += " ( id_KOLEJ>=0 and  id_KOLEJ<=100) " ' & Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 2 Then
            SQL += " ( id_KOLEJ>=101 and  id_KOLEJ<=199) " '& Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 3 Then
            SQL += " ( id_KOLEJ>=200  and  JENIS<3) " ' Cb_Kategori.SelectedValue
        End If

        'KOLEJ
        Cmd.CommandText = "select dc_kolej, id_kolej from pn_kolej where " & SQL & " order by dc_kolej" 'jenis
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("(SEMUA)")
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Dim SQL, Id_XM, Tahun, Siri, Subjek, X As String
        Tahun = "0" : Siri = "0" : Subjek = "0" : Id_XM = "0" : X = ""

        If Cb_Kolej.SelectedIndex > 0 Then X += " where p.id_kolej=" & Cb_Kolej.SelectedValue
        If Cb_Kursus.SelectedIndex < 1 Then
            Cb_Kursus.Focus() : Msg(Me, "Sila Pilih Jenis Kursus") : Exit Sub
        Else
            If Cb_Kolej.SelectedIndex < 1 Then X += " where " Else X += " and "
            X += " j_xm=" & Cb_Kursus.SelectedIndex
        End If
        If Tx_Tahun.Text <> "" And Tx_Siri.Text <> "" Then
            If X.Length = 0 Then
                X += " where tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text
            Else
                X += " and tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text
            End If
        End If

        If X = "" Then
        Else
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            SQL = "select top 1 px.id_xm, tahun, siri, j_xm from xm_calon xc inner join pn_xm px on xc.id_xm=px.id_xm inner join PELATIH p on p.NoKP=xc.nokp " & _
                        " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM " & _
                         "left join pn_kolej pk on pk.id_kolej=xc.id_pusat " & X & " order by tahun desc, siri desc, j_xm desc"
            Cmd.CommandText = SQL : Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then Id_XM = Rdr(0) : Tahun = Rdr(1) : Siri = Rdr(2) : Subjek = Rdr(3)
            Rdr.Close() : Cn.Close()
        End If

        If CInt(Subjek) > 2 Then
            Subjek = CInt(Subjek) + 1
            Subjek = Subjek & Cb_Kertas.SelectedValue
        Else
            Subjek = Subjek & Cb_Kertas.SelectedValue
        End If

        SQL = ""

        'update by azidi 13/08/2012
        'update by azidi 13/08/2012
        'If Cb_Kategori.SelectedIndex < 1 Then SQL += "" Else SQL_pilih += " and pnk.jenis=" & Cb_Kategori.SelectedIndex 'Kategori
        If Cb_Kategori.SelectedIndex < 1 Then
            SQL += ""
        ElseIf Cb_Kategori.SelectedIndex = 1 Then
            SQL += " and ( pn.id_KOLEJ>=0 and  pn.id_KOLEJ<=100) " ' & Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 2 Then
            SQL += "  and ( pn.id_KOLEJ>=101 and  pn.id_KOLEJ<=199) " '& Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 3 Then
            SQL += "  and ( pn.id_KOLEJ>=200  and  pn.JENIS<3) " ' Cb_Kategori.SelectedValue
        End If
        If Cb_Kolej.SelectedIndex < 1 Then SQL += "" Else SQL += " and p.id_kolej=" & Cb_Kolej.SelectedValue

        Jana(SQL, Id_XM, Tahun, Siri, Subjek)
    End Sub


    
    Public Sub Jana(ByVal X As String, ByVal id_xm As String, ByVal tahun As String, ByVal siri As String, ByVal subjek As String)

        Dim Tajuk, Tajuk2, Tajuk3, Master, Master2, Answer As String, Total, A, B, C, D, G, T As Integer
        Dim Jawatan, Jawatan2, Jawatan3 As Integer
        If Cb_Kursus.SelectedValue = 1 Then Jawatan = 1 : Jawatan2 = 5 : Jawatan3 = 8
        If Cb_Kursus.SelectedValue = 2 Then Jawatan = 2 : Jawatan2 = 2 : Jawatan3 = 2
        If Cb_Kursus.SelectedValue = 3 Then Jawatan = 3 : Jawatan2 = 3 : Jawatan3 = 3
        If Cb_Kursus.SelectedValue = 4 Then Jawatan = 4 : Jawatan2 = 4 : Jawatan3 = 4

        Tajuk = "STATISTIK ANALISA SOALAN PEPERIKSAAN BAGI " & Cb_Kategori.SelectedItem.Text & ", " & Cb_Kursus.SelectedItem.Text
        Tajuk2 = Cb_Kolej.SelectedItem.Text
        Tajuk3 = "KERTAS " & Cb_Kertas.SelectedItem.Text & ", TAHUN " & tahun & ", SIRI " & siri

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='11' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='11' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='11' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk3 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>NO</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JAWAPAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>A</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>B</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>C</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>D</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>GANDA</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>TIDAK MENJAWAB</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>DIFFICULTY INDEX</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>DISCRIMINATION INDEX</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>P-VALUE</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        Header = "" : Master = "" : Total = 0
        'Comment Ori 15092015 - OSH
        'Cmd.CommandText = "select distinct(cast(master as nvarchar(120))) as master from xm_calon xc inner join xm_markah xm on xc.ag=xm.ag " & _
        '            "inner join pelatih p on xc.nokp=p.nokp left join pn_kolej pn on p.id_kolej=pn.id_kolej where (p.j_kursus = " & Jawatan & " " & _
        '            "or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and tahun=" & tahun & " and " & _
        '            "siri=" & siri & " and subjek=" & subjek & " " & X


        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "select distinct(cast(master as nvarchar(120))) as master from xm_calon xc inner join xm_markah xm on xc.ag=xm.ag " & _
                   "inner join pelatih p on xc.nokp=p.nokp left join pn_kolej pn on p.id_kolej=pn.id_kolej where (p.j_kursus = " & Jawatan & " " & _
                   "or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and tahun=" & tahun & " and " & _
                   "siri=" & siri & " and subjek=" & subjek & " and p.status is null " & X
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If Not IsDBNull(Rdr(0)) Then Master = Rdr(0) : Exit While Else 
        End While
        Rdr.Close()
        'Comment Ori 15092015 - OSH
        'Cmd.CommandText = "select count(*) from xm_calon xc inner join xm_markah xm on xc.ag=xm.ag inner join pelatih p on xc.nokp=p.nokp left join " & _
        '                "pn_kolej pn on p.id_kolej=pn.id_kolej where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & _
        '                "" & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and tahun=" & tahun & " and siri=" & siri & " and subjek=" & subjek & " " & X

        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "select count(*) from xm_calon xc inner join xm_markah xm on xc.ag=xm.ag inner join pelatih p on xc.nokp=p.nokp left join " & _
                       "pn_kolej pn on p.id_kolej=pn.id_kolej where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & _
                       "" & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and tahun=" & tahun & " and siri=" & siri & " and subjek=" & subjek & " and p.status is null " & X
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If Not IsDBNull(Rdr(0)) Then Total = Rdr(0)
        End While
        Rdr.Close()
        'Comment Ori 15092015 - OSH
        'Cmd.CommandText = "delete from temp_xm; " & _
        '                "insert temp_xm select jawapan, xm.markah, xm.ag from " & _
        '                "pelatih p inner join xm_calon xc on p.nokp=xc.nokp inner join xm_markah xm on xc.ag=xm.ag left join " & _
        '                "pn_kolej pn on p.id_kolej=pn.id_kolej where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and " & _
        '                "tahun=" & tahun & " and siri=" & siri & " and subjek=" & subjek & " " & X & " order by cast(xc.ag as integer)"

        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "delete from temp_xm; " & _
                        "insert temp_xm select jawapan, xm.markah, xm.ag from " & _
                        "pelatih p inner join xm_calon xc on p.nokp=xc.nokp inner join xm_markah xm on xc.ag=xm.ag left join " & _
                        "pn_kolej pn on p.id_kolej=pn.id_kolej where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and " & _
                        "tahun=" & tahun & " and siri=" & siri & " and subjek=" & subjek & " and p.status is null " & X & " order by cast(xc.ag as integer)"
        Cmd.ExecuteNonQuery()
        
        For i As Integer = 0 To Master.Length - 1
            Header += "<tr>"
            Header += "    <td>" & i + 1 & "</td> "
            Master2 = Master.Substring(i, 1)
            Header += "    <td style='vertical-align: middle; text-align:right;'>" & Master2 & "</td> " ' papar jwpn btl

            Answer = "" : A = 0 : B = 0 : C = 0 : D = 0 : G = 0 : T = 0

            'Comment Ori 15092015 - OSH
            'Cmd.CommandText = "select substring(jawapan," & i + 1 & ",1) from " & _
            '                "pelatih p inner join xm_calon xc on p.nokp=xc.nokp inner join xm_markah xm on xc.ag=xm.ag left join " & _
            '                "pn_kolej pn on p.id_kolej=pn.id_kolej where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and " & _
            '                "tahun=" & tahun & " and siri=" & siri & " and subjek=" & subjek & " " & X & " order by cast(xc.ag as integer)"


            'Fixing multiple records display per canidate 15092015 - OSH
            Cmd.CommandText = "select substring(jawapan," & i + 1 & ",1) from " & _
                            "pelatih p inner join xm_calon xc on p.nokp=xc.nokp inner join xm_markah xm on xc.ag=xm.ag left join " & _
                            "pn_kolej pn on p.id_kolej=pn.id_kolej where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and " & _
                            "tahun=" & tahun & " and siri=" & siri & " and subjek=" & subjek & " and p.status is null " & X & " order by cast(xc.ag as integer)"
            Rdr = Cmd.ExecuteReader()
            While Rdr.Read
                If Not IsDBNull(Rdr(0)) Then
                    Answer = Rdr(0)
                    If Answer = "A" Then A += 1
                    If Answer = "B" Then B += 1
                    If Answer = "C" Then C += 1
                    If Answer = "D" Then D += 1
                    If Answer = "*" Then G += 1
                    If Answer <> "A" And Answer <> "B" And Answer <> "C" And Answer <> "D" And Answer <> "*" Then T += 1
                End If
            End While
            Rdr.Close()

            ''Comment Ori 15092015 - OSH
            'Cmd.CommandText = " select (( " & _
            '" (cast((select count(AG) from(select top 27 percent xm.master,xm.jawapan,xm.ag " & _
            '" from  xm_markah xm inner join xm_calon xc  on xc.ag=xm.ag " & _
            '" inner join pn_xm px on xc.id_xm=px.id_xm " & _
            '" inner join pelatih p on p.nokp=xc.nokp " & _
            '" and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end )=px.j_XM " & _
            '" left join pn_kolej pn on p.id_kolej=pn.id_kolej " & _
            '" where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and " & _
            '"xm.tahun=" & tahun & " and xm.siri=" & siri & " and xm.subjek=" & subjek & " " & X & " order by xm.markah desc ) as grop " & _
            '"where SUBSTRING(grop.master, " & i + 1 & ", 1)= SUBSTRING(grop.jawapan, " & i + 1 & ", 1)) as decimal(4))) " & _
            '"- " & _
            '"(cast((select count(AG) from (select top 27 percent xm.master,xm.jawapan,xm.ag  " & _
            '" from  xm_markah xm inner join xm_calon xc  on xc.ag=xm.ag " & _
            '" inner join pn_xm px on xc.id_xm=px.id_xm " & _
            '" inner join pelatih p on p.nokp=xc.nokp " & _
            '" and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end )=px.j_XM " & _
            '" left join pn_kolej pn on p.id_kolej=pn.id_kolej " & _
            '" where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and " & _
            '"xm.tahun=" & tahun & " and xm.siri=" & siri & " and xm.subjek=" & subjek & " " & X & "  order by xm.markah asc ) as grop " & _
            '"where SUBSTRING(grop.master, " & i + 1 & ", 1)= SUBSTRING(grop.jawapan, " & i + 1 & ", 1)) as decimal(4)))" & _
            '") " & _
            '" / " & _
            '" cast( (select count(AG) from (select top 27 percent xm.master,xm.jawapan,xm.ag  " & _
            '" from  xm_markah xm inner join xm_calon xc  on xc.ag=xm.ag " & _
            '" inner join pn_xm px on xc.id_xm=px.id_xm " & _
            '" inner join pelatih p on p.nokp=xc.nokp " & _
            '" and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end )=px.j_XM " & _
            '" left join pn_kolej pn on p.id_kolej=pn.id_kolej " & _
            '" where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and " & _
            '" xm.tahun=" & tahun & " and xm.siri=" & siri & " and xm.subjek=" & subjek & " " & X & " " & _
            '"order by xm.markah desc ) as grop  ) as decimal(4)))"

            'Fixing multiple records display per canidate 15092015 - OSH
            Cmd.CommandText = " select (( " & _
            " (cast((select count(AG) from(select top 27 percent xm.master,xm.jawapan,xm.ag " & _
            " from  xm_markah xm inner join xm_calon xc  on xc.ag=xm.ag " & _
            " inner join pn_xm px on xc.id_xm=px.id_xm " & _
            " inner join pelatih p on p.nokp=xc.nokp " & _
            " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end )=px.j_XM " & _
            " left join pn_kolej pn on p.id_kolej=pn.id_kolej " & _
            " where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and " & _
            "xm.tahun=" & tahun & " and xm.siri=" & siri & " and xm.subjek=" & subjek & " and  p.status is null " & X & " order by xm.markah desc ) as grop " & _
            "where SUBSTRING(grop.master, " & i + 1 & ", 1)= SUBSTRING(grop.jawapan, " & i + 1 & ", 1)) as decimal(4))) " & _
            "- " & _
            "(cast((select count(AG) from (select top 27 percent xm.master,xm.jawapan,xm.ag  " & _
            " from  xm_markah xm inner join xm_calon xc  on xc.ag=xm.ag " & _
            " inner join pn_xm px on xc.id_xm=px.id_xm " & _
            " inner join pelatih p on p.nokp=xc.nokp " & _
            " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end )=px.j_XM " & _
            " left join pn_kolej pn on p.id_kolej=pn.id_kolej " & _
            " where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and " & _
            "xm.tahun=" & tahun & " and xm.siri=" & siri & " and xm.subjek=" & subjek & " and  p.status is null " & X & "  order by xm.markah asc ) as grop " & _
            "where SUBSTRING(grop.master, " & i + 1 & ", 1)= SUBSTRING(grop.jawapan, " & i + 1 & ", 1)) as decimal(4)))" & _
            ") " & _
            " / " & _
            " cast( (select count(AG) from (select top 27 percent xm.master,xm.jawapan,xm.ag  " & _
            " from  xm_markah xm inner join xm_calon xc  on xc.ag=xm.ag " & _
            " inner join pn_xm px on xc.id_xm=px.id_xm " & _
            " inner join pelatih p on p.nokp=xc.nokp " & _
            " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end )=px.j_XM " & _
            " left join pn_kolej pn on p.id_kolej=pn.id_kolej " & _
            " where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & " and " & _
            " xm.tahun=" & tahun & " and xm.siri=" & siri & " and xm.subjek=" & subjek & "and  p.status is null " & X & " " & _
            "order by xm.markah desc ) as grop  ) as decimal(4)))"
            Dim discIndx As Decimal
            Rdr = Cmd.ExecuteReader()
            While Rdr.Read
                If Not IsDBNull(Rdr(0)) Then
                    discIndx = Rdr(0)
                End If
            End While
            Rdr.Close()


            Header += "    <td>" & A & "</td> "
            Header += "    <td>" & B & "</td> "
            Header += "    <td>" & C & "</td> "
            Header += "    <td>" & D & "</td> "
            Header += "    <td>" & G & "</td> "
            Header += "    <td>" & T & "</td> "
            Header += "    <td>" & Percent(Master2, Total, A, B, C, D) & "</td> "
            Header += "    <td>" & Math.Round(discIndx, 3) & "</td> " 'Math.Round(Discrement(Master2, i), 2) 
            Header += "    <td>" & Math.Round((Percent(Master2, Total, A, B, C, D) / 100), 2) & "</td> "
            Header += "</tr>"
        Next

        Response.Write(Header)
        Response.Write("</table>")
        Response.End()
        Response.Flush()
    End Sub

    Function Percent(ByVal X As String, ByVal Y As Long, ByVal A As Long, ByVal B As Long, ByVal C As Long, ByVal D As Long)
        Dim Z As Double
        If X = "A" Then If A <> 0 Then Z = Math.Round((CDbl(A) / CDbl(Y)) * 100, 2) Else Z = 0
        If X = "B" Then If B <> 0 Then Z = Math.Round((CDbl(B) / CDbl(Y)) * 100, 2) Else Z = 0
        If X = "C" Then If C <> 0 Then Z = Math.Round((CDbl(C) / CDbl(Y)) * 100, 2) Else Z = 0
        If X = "D" Then If D <> 0 Then Z = Math.Round((CDbl(D) / CDbl(Y)) * 100, 2) Else Z = 0
        Return Z
    End Function

    Function Discrement(ByVal Y As String, ByVal i As Integer)
        Dim ans1, ans2, D2 As String
        Dim ans, A, B, J As Double

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        J = 0 : A = 0 : B = 0 : ans = 0 : i = i + 1
        D2 = Y

        
       
        Cmd.CommandText = "select top 27 percent cast(jawapan as nvarchar(150)) from temp_xm order by markah"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If Not IsDBNull(Rdr(0)) Then J = J + 1
        End While
        Rdr.Close()

        Cmd.CommandText = "select top 27 percent substring(cast(jawapan as nvarchar(150))," & i & ",1) from temp_xm order by markah"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If Not IsDBNull(Rdr(0)) Then
                ans1 = Rdr(0)
                If ans1 = D2 Then A += 1
            End If
        End While
        Rdr.Close()

        Cmd.CommandText = "select top 27 percent substring(cast(jawapan as nvarchar(150))," & i & ",1) from temp_xm order by markah desc"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If Not IsDBNull(Rdr(0)) Then
                ans2 = Rdr(0)
                If ans2 = D2 Then B += 1
            End If
        End While
        Rdr.Close()

        ans = (A - B) / J

        Return ans
    End Function
End Class