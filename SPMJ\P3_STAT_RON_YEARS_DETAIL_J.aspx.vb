﻿Imports System.Globalization
Imports System.IO
Imports System.Data
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.Text
Imports iTextSharp.text
Imports iTextSharp.text.pdf
Imports iTextSharp.text.html
Imports iTextSharp.text.html.simpleparser

Partial Public Class P3_STAT_RON_YEARS_DETAIL_J
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        'Comment Original 08072020 - OSH 
        'Add Secure Check Point 06072020 - OSH 
        'If Not Akses_Pg("P1", "Laporan", Session("AKSES"), Session("MODUL")) Then
        '    Session("Msg_Tajuk") = "APC"
        '    Session("Msg_Isi") = "Akses <PERSON>had"
        '    Response.Redirect("p0_Mesej.aspx")
        '    Exit Sub
        'End If
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()
        Dim Requestor As String = "" 'Add name varible 06072020 - OSH 
        'Dim tahun As Int16  'Comment Original 13112018 - OSH

        Dim Cn3 As New OleDbConnection : Dim Cmd3 As New OleDbCommand : Dim Rdr3 As OleDbDataReader
        Cn3.ConnectionString = ServerId : Cn3.Open() : Cmd3.Connection = Cn3

        Cmd3.CommandText = "select Nama from pn_pengguna where id_pg = '" & Session("Id_PG") & "' and status ='1' order by nama"
        Rdr3 = Cmd3.ExecuteReader()
        While Rdr3.Read
            Requestor = Rdr3(0)
        End While
        Rdr3.Close()
        Cn3.Close()

        Dim ch As New Chunk("DIJANA OLEH : " & Requestor.ToString & "  PADA  " & DateAndTime.Today & "  MASA " & DateAndTime.TimeOfDay)
        Dim ph As New Phrase(ch)
        Dim p As New Paragraph()
        p.Add(ph)

        'Monthly Report
        If Tx_Tkh_M.Text <> "" And Tx_Tkh_A.Text <> "" Then

            ''FIXING DATE FORMAT 24072018 - OSH
            Dim a1, a2 As DateTime
            Dim b1, b2 As String
            Dim provider As CultureInfo = CultureInfo.InvariantCulture

            a1 = Date.ParseExact(Tx_Tkh_M.Text, "dd/MM/yyyy", provider)
            a2 = Date.ParseExact(Tx_Tkh_A.Text, "dd/MM/yyyy", provider)

            b1 = a1.ToString("yyyy-MM-dd") 'Tarikh Mula
            b2 = a2.ToString("yyyy-MM-dd") 'Tarih Tamat

            cmd.CommandType = CommandType.StoredProcedure
            cmd.CommandText = "SPMJ_Monthly_RON_Details"

            'Value of report title 07072020 - OSH  
            Session("RP_ID") = "RET2"

            'Varible for dates 06072020 - OSH
            Session("BEGIN_date") = Tx_Tkh_M.Text.Trim
            Session("END_date") = Tx_Tkh_A.Text.Trim



            'Comment Original 13112018 - OSH
            'cmd.Parameters.Add("@tahun_apc", SqlDbType.Int, 4).Value = tahun
            'comment Ori 24072018 -OSH
            'cmd.Parameters.Add("@tkh_ron_mula", SqlDbType.Date).Value = Tx_Tkh_M.Text
            'cmd.Parameters.Add("@tkh_ron_tamat", SqlDbType.Date).Value = Tx_Tkh_A.Text

            cmd.Parameters.Add("@tkh_ron_mula", SqlDbType.Date).Value = b1
            cmd.Parameters.Add("@tkh_ron_tamat", SqlDbType.Date).Value = b2
            cmd.Connection = con
            Try
                con.Open()
                Dim GridView1 = New GridView()
                GridView1.EmptyDataText = "No Records Found"
                GridView1.DataSource = cmd.ExecuteReader()
                GridView1.DataBind()

                'Dump to PDF
                Response.ContentType = "application/pdf"
                Response.AddHeader("content-disposition", "attachment;filename=RON_MONTHLY_CATEGEORY_" & DateAndTime.Now & "_" & DateAndTime.TimeOfDay & ".pdf")



                Response.Cache.SetCacheability(HttpCacheability.NoCache)
                Dim sw As New StringWriter()
                Dim hw As New HtmlTextWriter(sw)
                GridView1.RenderControl(hw)
                Dim sr As New StringReader(sw.ToString())
                Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)
                Dim htmlparser As New HTMLWorker(pdfDoc)
                'OPEN PDF writer for dumping 
                Dim pdfWrite As PdfWriter = PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
                'Add header
                Dim MonthlyHeader As New HeaderRONmonthly
                pdfWrite.PageEvent = MonthlyHeader

                'Add Footer 06072020 - OSH
                Dim footerPDF As New PageFooter
                pdfWrite.PageEvent = footerPDF

                'PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
                pdfDoc.Open()
                htmlparser.Parse(sr)
                'Add generate user and datetime details 06072020 - OSH   
                pdfDoc.Add(New Paragraph(" "))
                pdfDoc.Add(p)
                pdfDoc.Close()
                Response.Write(pdfDoc)
                Response.End()
            Catch ex As Exception
                Throw ex
            Finally
                con.Close()
                con.Dispose()
            End Try
        Else
            'Show jQuery dialog telling user that their line items total is out of range
            'Page.ClientScript.RegisterClientScriptBlock(this.GetType(), "dlgOutOfRange","ShowRangeDialog();", true);
            Page.ClientScript.RegisterClientScriptBlock(Me.GetType, "dlgInfo", "ShowInfoDialog();", True)
        End If
    End Sub

End Class