<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="PasswordMigration.aspx.vb" Inherits="SPMJ.PasswordMigration" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Password Migration Utility</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .btn { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background-color: #0056b3; }
        .btn-danger { background-color: #dc3545; }
        .btn-danger:hover { background-color: #c82333; }
        .info-box { background-color: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container">
            <h1>Password Migration Utility</h1>
            
            <div class="warning">
                <strong>Warning:</strong> This utility will migrate all plain text passwords to secure hashed passwords. 
                Please ensure you have a database backup before proceeding.
            </div>
            
            <div class="info-box">
                <h3>What this utility does:</h3>
                <ul>
                    <li>Scans all user accounts for plain text passwords</li>
                    <li>Converts plain text passwords to secure SHA256 hashes with salt</li>
                    <li>Maintains backward compatibility during transition</li>
                    <li>Provides detailed migration report</li>
                </ul>
            </div>
            
            <div>
                <asp:Button ID="btnCheckStatus" runat="server" Text="Check Migration Status" CssClass="btn" />
                <asp:Button ID="btnMigrate" runat="server" Text="Start Migration" CssClass="btn btn-danger" 
                    OnClientClick="return confirm('Are you sure you want to migrate all passwords? This action cannot be undone.');" />
            </div>
            
            <div style="margin-top: 20px;">
                <asp:Label ID="lblResults" runat="server" />
            </div>
            
            <div style="margin-top: 20px;">
                <asp:GridView ID="gvMigrationResults" runat="server" AutoGenerateColumns="false" 
                    CssClass="table" Width="100%" CellPadding="5" GridLines="Both">
                    <Columns>
                        <asp:BoundField DataField="UserId" HeaderText="User ID" />
                        <asp:BoundField DataField="Status" HeaderText="Status" />
                        <asp:BoundField DataField="PasswordType" HeaderText="Password Type" />
                        <asp:BoundField DataField="LastUpdated" HeaderText="Last Updated" />
                    </Columns>
                    <HeaderStyle BackColor="#007bff" ForeColor="White" />
                    <AlternatingRowStyle BackColor="#f8f9fa" />
                </asp:GridView>
            </div>
            
            <div style="margin-top: 30px; border-top: 1px solid #ccc; padding-top: 20px;">
                <h3>Migration Log</h3>
                <asp:TextBox ID="txtLog" runat="server" TextMode="MultiLine" Rows="10" Width="100%" 
                    ReadOnly="true" style="font-family: monospace; font-size: 12px;" />
            </div>
        </div>
    </form>
</body>
</html>
