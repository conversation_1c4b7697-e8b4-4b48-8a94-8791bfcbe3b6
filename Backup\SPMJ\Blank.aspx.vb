﻿
Partial Public Class WebForm1
    Inherits System.Web.UI.Page

    Private Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 08072020 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        Select Case Session("SPMJ_PRINT")
            Case "arsb_penuhs"
                Response.Write(Ex1 & "c:/progra~1/ljm/spmj_print1.exe arsb_penuhs" & Ex2)
            Case "arsb_penuhfoto"
                Response.Write(Ex1 & "c:/progra~1/ljm/spmj_print1.exe arsb_penuhfoto" & Ex2)
            Case "arsb_tpcfoto"
                Response.Write(Ex1 & "c:/progra~1/ljm/spmj_print1.exe arsb_tpcfoto" & Ex2)
            Case "arsb_penuht"
                Response.Write(Ex1 & "c:/progra~1/ljm/spmj_print1.exe arsb_penuht" & Ex2)
            Case "arsb_apc"
                Response.Write(Ex1 & "c:/progra~1/ljm/spmj_print1.exe arsb_apc" & Ex2)
            Case "arsb_2apc"
                Response.Write(Ex1 & "c:/progra~1/ljm/spmj_print1.exe arsb_2apc" & Ex2)
            Case "arsb_ag"
                Response.Write(Ex1 & "c:/progra~1/ljm/spmj_print1.exe arsb_ag" & Ex2)
            Case "arsb_kpts"
                Response.Write(Ex1 & "c:/progra~1/ljm/spmj_print1.exe arsb_kpts" & Ex2)
            Case "arsb_tpc"
                Response.Write(Ex1 & "c:/progra~1/ljm/spmj_print1.exe arsb_tpc" & Ex2)
            Case "apc_akuan"
                Dim x As String = ""
                x += Header_Surat_Blank(1)
                x += "<div  style='font-family: Arial; font-size: 12pt; text-align:center;'>"
                x += "<br><b>SURAT AKUAN PENERIMAAN SIJIL PERAKUAN PENGAMALAN TAHUNAN"
                x += "<br>JURURAWAT BERDAFTAR (KERAJAAN)</b>"
                x += "<br>"
                x += "</div>"

                x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 10pt;'><tr>"
                x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 10pt;'></td>"
                x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 10pt;'> Rujukan Tuan : KKM 87/A3/5/5 JLD(&nbsp;&nbsp;)/(&nbsp;&nbsp;) </td>"
                x += "</tr><tr>"
                x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 10pt;'></td>"
                x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 10pt;'> Tarikh :</td>"
                x += "</tr></table>"


                x += "<div style='font-family: Arial; font-size: 12pt;'>"
                x += "<br>........................................."
                x += "<br>........................................."
                x += "<br>........................................."
                x += "<br>........................................."
                x += "<br>........................................."
                x += "<br>"
                x += "<br>Kepada :"
                x += "<br>"
                x += "<br>Pendaftar,"
                x += "<br>Lembaga Jururawat Malaysia,"
                x += "<br>Aras 3, Blok E1, Kompleks E, Presint 1,"
                x += "<br>Pusat Pentadbiran Kerajaan Persekutuan,"
                x += "<br><u>62250 Putrajaya.</u>"
                x += "</div>"

                x += "<br/>"
                x += "<div  style='font-family: Arial; font-size: 12pt;'>Tuan/Puan,"
                x += "</div>"

                x += "<br/>"
                x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'><b>SIJIL PERAKUAN PENGAMALAN TAHUNAN JURURAWAT BERDAFTAR TAHUN ................."
                x += "</b></div>"

                x += "<br/>"
                x += "<br/>"
                x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
                x += "Merujuk kepada surat tuan/puan bil. ......................................... bertarikh ...................... dimaklumkan bahawa saya telah menerima sijil tersebut sebanyak ............ keping."
                x += "</div>"

                x += "<br/>"
                x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
                x += "Sekian, terima kasih."
                x += "</div>"

                x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
                x += "<br/>"
                x += "<br/>"
                x += "<br/>"
                x += "<br/>"
                x += "<br/>"
                x += "........................................."
                x += "<br>(&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)"
                x += "<br></div><b><div style='font-family: Arial; font-size: 9pt;'>* <i>SURAT JAWAPAN INI HENDAKLAH DIKEMBALIKAN KE LEMBAGA JURURAWAT MALAYSIA"
                x += "</b></div>"
                x += Footer_Surat_Blank()

                Dim fileName As String = "Surat.doc"
                Response.Clear()
                Response.Buffer = True
                ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
                Response.AppendHeader("Content-Type", "application/msword")
                Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
                Response.Write(x)
        End Select
    End Sub
End Class