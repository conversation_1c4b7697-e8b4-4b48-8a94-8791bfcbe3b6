﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_TPC_Jantina
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'TAHUN APC
        For i = Now.Year To Now.Year - 2 Step -1
            Cb_Tahun.Items.Add(i)
        Next

        'Status
        With Cb_Status
            .Items.Clear()
            .Items.Add("BERDAFTAR")
            .Items.Item(.Items.Count - 1).Value = "1"
            .Items.Add("BELUM BERDAFTAR")
            .Items.Item(.Items.Count - 1).Value = "2"
        End With
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim <PERSON>, tpc, tkh As String
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        tpc = "" : tkh = ""
        If Cb_Status.SelectedValue = 1 Then tpc = "jt_tpc" : tkh = "tkh_daftar" Else tpc = "tmp_tpc" : tkh = "mohon_tkh"

        SQL = "delete from z; "
        SQL += "insert into z "
        SQL += "select warganegara as kolej, count(warganegara) as ag, null as lulus, null as gagal, null as tumpang, null as m80, null as m45, null as m0 "
        SQL += "from " & tpc & " t left outer join pn_negara pn on t.warganegara=pn.id_negara where year(" & tkh & ")=" & Cb_Tahun.Text & " and t.jantina=1 group by warganegara "
        SQL += "union select warganegara as kolej, null as ag, count(warganegara) as lulus, null as gagal, null as tumpang, null as m80, null as m45, null as m0 "
        SQL += "from " & tpc & " t left outer join pn_negara pn on t.warganegara=pn.id_negara where year(" & tkh & ")=" & Cb_Tahun.Text & " and t.jantina=2 group by warganegara "
        SQL += "union select warganegara as kolej, null as ag, null as lulus, count(warganegara) as gagal, null as tumpang, null as m80, null as m45, null as m0 "
        SQL += "from " & tpc & " t left outer join pn_negara pn on t.warganegara=pn.id_negara where year(" & tkh & ")=" & Cb_Tahun.Text & " group by warganegara "
        SQL += ";"
        Cmd.CommandText = SQL
        Cmd.ExecuteNonQuery()
        Cn.Close()

        Jana()
    End Sub

    Public Sub Jana()
        Dim Tajuk, Tajuk2 As String, m, f, x, xx, total As Integer

        Tajuk = "LAPORAN STATISTIK PENDAFTARAN TPC " & Cb_Status.SelectedItem.Text & ", TAHUN " & Cb_Tahun.Text
        Tajuk2 = "MENGIKUT NEGARA DAN JANTINA"

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "laporan" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='5' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='5' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>NEGARA</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>LELAKI</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>PEREMPUAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>X</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        Cmd.CommandText = "select dc_negara, isnull(sum(ag),'0'), isnull(sum(lulus),'0'), isnull(sum(gagal),'0'), " & _
                          "isnull(sum(tumpang),'0'), isnull(sum(m80),'0'), isnull(sum(m45),'0'), isnull(sum(m0),'0') " & _
                          "from z z left outer join pn_negara pn on z.kolej=pn.id_negara group by dc_negara order by dc_negara"
        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "z")
        Rdr.Close()

        Header = ""
        For Each dr As DataRow In Ds.Tables(0).Rows
            Header += "<tr>"
            Header += "    <td>" & dr.Item(0) & "</td> "
            Header += "    <td>" & dr.Item(1) & "</td> " : m += CInt(dr.Item(1))
            Header += "    <td>" & dr.Item(2) & "</td> " : f += CInt(dr.Item(2))
            x = CInt(dr.Item(3)) - CInt(dr.Item(2)) - CInt(dr.Item(1))
            Header += "    <td>" & x & "</td> " : xx += x
            Header += "    <td>" & dr.Item(3) & "</td> " : total += dr.Item(3)
            Header += "</tr>"
        Next
        Header += "<tr>"
        Header += "    <td>JUMLAH KESELURUHAN</td> "
        Header += "    <td>" & m & "</td> "
        Header += "    <td>" & f & "</td> "
        Header += "    <td>" & xx & "</td> "
        Header += "    <td>" & total & "</td> "
        Header += "</tr>"

        Cn.Close()
        Header += "</table>"
        Response.Write(Header)

        Response.End()
        Response.Flush()
    End Sub

End Class