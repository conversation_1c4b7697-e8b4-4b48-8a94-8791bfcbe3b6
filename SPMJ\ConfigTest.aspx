<%@ Page Language="vb" AutoEventWireup="false" CodeBehind="ConfigTest.aspx.vb" Inherits="SPMJ.ConfigTest" %>

<!DOCTYPE html>
<html>
<head runat="server">
    <title>Configuration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; margin: 10px 0; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; margin: 10px 0; }
        .info { background-color: #e7f3ff; border: 1px solid #b3d9ff; color: #0c5460; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <form id="form1" runat="server">
        <h1>SPMJ Configuration Test</h1>
        
        <div class="info">
            <h3>Web.config Test</h3>
            <p>This page tests if the Web.config file is being read correctly.</p>
        </div>
        
        <asp:Button ID="btnTest" runat="server" Text="Test Configuration" />
        
        <div style="margin-top: 20px;">
            <asp:Label ID="lblResults" runat="server" />
        </div>
        
        <div style="margin-top: 20px;">
            <h3>Configuration Details:</h3>
            <asp:TextBox ID="txtResults" runat="server" TextMode="MultiLine" Rows="15" Width="100%" 
                ReadOnly="true" style="font-family: monospace; font-size: 12px;" />
        </div>
    </form>
</body>
</html>
