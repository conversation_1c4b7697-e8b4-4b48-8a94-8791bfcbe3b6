﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="LP_STAT_APC_Kekal_Nama.aspx.vb" Inherits="SPMJ.LP_STAT_APC_Kekal_Nama" 
    title="Untitled Page" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style2
        {
            height: 7px;
        }
        .style7
        {
            width: 427px;
        }
        .style9
        {
            height: 7px;
            width: 427px;
        }
        .style10
        {
            height: 14px;
        }
        .style11
        {
            height: 14px;
            width: 427px;
        }
        .style12
        {
            height: 37px;
        }
        .style13
        {
            width: 427px;
            height: 37px;
        }
        .style14
        {
            height: 41px;
        }
        .style15
        {
            width: 427px;
            height: 41px;
        }
        .style16
        {
            margin-top: 0px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td></td>
            <td class="style7"></td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td class="style7">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style14"></td>
            <td align="center" 
                style="border-style: solid; border-width: 1px; border-color: #000000 #000000 #808055 #000000; font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold; background-color: #999966;" 
                bgcolor="#EEEEEE" class="style15">Laporan&nbsp;Statistik pengekalan nama
                <br />
                mengikut bulan dan jantina</td>
            <td class="style14"></td>
        </tr>
        <tr>
            <td></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style7">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style9" align="left">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj10" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="91px" 
                            CssClass="style16" Enabled="False" ReadOnly="True">TAHUN PROSES</asp:TextBox>
                        <asp:DropDownList ID="Cb_Tahun" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="100px">
                        </asp:DropDownList>
                    </td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style9" align="left">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj11" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="91px" Enabled="False" 
                            ReadOnly="True">JAWATAN</asp:TextBox>
                        <asp:DropDownList ID="Cb_Jawatan" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="150px">
                        </asp:DropDownList>
                    </td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style10"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style11">
                </td>
            <td class="style10"></td>
        </tr>
        <tr>
            <td class="style12"></td>
            <td 
                
                style="border-style: none solid solid solid; border-width: 1px; border-color: #000000 #003300 #003300 #003300; background-color: #D7D7C4;" 
                bgcolor="White" class="style13" align="right">
                &nbsp;&nbsp;<asp:Button ID="cmd_Cari1" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="JANA" Width="80px" />
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </td>
            <td class="style12"></td>
        </tr>
        <tr>
            <td></td>
            <td class="style7">&nbsp;</td>
            <td></td>
        </tr>
    
    
        <tr>
            <td>&nbsp;</td>
            <td class="style7">
                <br />
                        <br />
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>
    
    
    </table>
    
    
    </div></asp:Content>
