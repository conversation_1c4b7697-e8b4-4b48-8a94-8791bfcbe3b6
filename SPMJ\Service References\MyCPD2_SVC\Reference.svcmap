<?xml version="1.0" encoding="utf-8"?>
<ReferenceGroup xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ID="09517737-8ab2-4ff4-b3d7-b831cf5e8ae9" xmlns="urn:schemas-microsoft-com:xml-wcfservicemap">
  <ClientOptions>
    <GenerateAsynchronousMethods>false</GenerateAsynchronousMethods>
    <EnableDataBinding>true</EnableDataBinding>
    <ExcludedTypes />
    <ImportXmlTypes>false</ImportXmlTypes>
    <GenerateInternalTypes>false</GenerateInternalTypes>
    <GenerateMessageContracts>false</GenerateMessageContracts>
    <NamespaceMappings />
    <CollectionMappings />
    <GenerateSerializableTypes>true</GenerateSerializableTypes>
    <Serializer>Auto</Serializer>
    <ReferenceAllAssemblies>true</ReferenceAllAssemblies>
    <ReferencedAssemblies />
    <ReferencedDataContractTypes />
    <ServiceContractMappings />
  </ClientOptions>
  <MetadataSources>
    <MetadataSource Address="https://www.mycpd2.moh.gov.my/ws/service1.svc?wsdl" Protocol="http" SourceId="1" />
  </MetadataSources>
  <Metadata>
    <MetadataFile FileName="Service1.xsd" MetadataType="Schema" ID="2a90ad9c-c079-4d4b-a67b-a6b60d261484" SourceId="1" SourceUrl="https://www.mycpd2.moh.gov.my/ws/Service1.svc?xsd=xsd1" />
    <MetadataFile FileName="Service11.xsd" MetadataType="Schema" ID="494df5fc-ffb7-4c75-803c-507b2e0acc3f" SourceId="1" SourceUrl="https://www.mycpd2.moh.gov.my/ws/Service1.svc?xsd=xsd0" />
    <MetadataFile FileName="Service12.xsd" MetadataType="Schema" ID="a4bdc74d-a5e1-4c10-84a3-a82f59270650" SourceId="1" SourceUrl="https://www.mycpd2.moh.gov.my/ws/Service1.svc?xsd=xsd3" />
    <MetadataFile FileName="Service13.xsd" MetadataType="Schema" ID="6d2f2df7-8b17-4cb9-8caa-b87886848da6" SourceId="1" SourceUrl="https://www.mycpd2.moh.gov.my/ws/Service1.svc?xsd=xsd2" />
    <MetadataFile FileName="Service1.wsdl" MetadataType="Wsdl" ID="fe7d026f-3179-4d50-ad00-f3d0c2bf6004" SourceId="1" SourceUrl="https://www.mycpd2.moh.gov.my/ws/service1.svc?wsdl" />
  </Metadata>
  <Extensions>
    <ExtensionFile FileName="configuration.svcinfo" Name="configuration.svcinfo" />
  </Extensions>
</ReferenceGroup>