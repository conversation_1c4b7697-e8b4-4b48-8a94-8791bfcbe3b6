﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm6
    Inherits System.Web.UI.Page

    'Public Sub Cari(ByVal X As String, ByVal Jwtn As Integer)
    '    If Jwtn = 4 Then  Else Jwtn = 3

    '    Dim List_Data As New DataSet
    '    Dim Cn As New SqlConnection(ServerId_SQL)
    '    Dim SQL As String = "select p.nama 'NAMA CALON', p.nokp as 'NO. KP/PASPORT', pnk.dc_kolej as 'KOLEJ',cast(p.sesi_bulan as varchar(4)) + '/' + cast(p.sesi_tahun as varchar(4)) 'SESI PENGAMBILAN', " & _
    '                        "'' as 'ANGKA GILIRAN', p.markah as 'MARKAH BERTERUSAN', isnull(p.cuti,0)-isnull(p.cuti_ganti,0) as 'LEBIHAN CUTI SAKIT', p.SARING, " & _
    '                        "case p.TATATERTIB when 1 then 'YA' when 2 then 'TIDAK' else '' end 'TATATERTIB', case isnull(xm.ulangan,'0') when '0' then 'BARU' when '1' then 'PERTAMA' when '2' then 'KEDUA' when '3' then 'KETIGA' end as 'ULANGAN', status_ulang " & _
    '                        "from pelatih p left outer join pn_kolej pnk on p.id_kolej = pnk.id_kolej left outer join xm_calon xm on p.nokp = xm.nokp where " & X & " and isnull(xm.ulangan,'0')<" & Jwtn & " and status_ulang is null order by p.j_kursus, p.sesi_tahun ,p.sesi_bulan, p.nama"
    '    Dim List_Adp As New SqlDataAdapter(SQL, Cn)

    '    List_Adp.Fill(List_Data, "pelatih")
    '    Gd.DataSource = List_Data.Tables("pelatih")
    '    DataBind()
    '    Gd.Visible = True
    'End Sub

    Public Sub Cari2(ByVal X As Integer, ByVal K As Integer)

        Dim Cn As New SqlConnection(ServerId_SQL)
        Dim List_Data As New DataSet
        Dim List_Adp As SqlDataAdapter

        List_Adp = New SqlDataAdapter("GetCanList_Sec", Cn)

        List_Adp.SelectCommand.CommandType = CommandType.StoredProcedure
        List_Adp.SelectCommand.Parameters.Add(New SqlParameter("@id_kolej", _
                                                                    SqlDbType.Int))
        List_Adp.SelectCommand.Parameters("@id_kolej").Value = X

        List_Adp.SelectCommand.Parameters.Add(New SqlParameter("@j_xm", _
                                                                   SqlDbType.Int))
        List_Adp.SelectCommand.Parameters("@j_xm").Value = K

        List_Adp.SelectCommand.Parameters.Add(New SqlParameter("@RowCount", _
                                                                   SqlDbType.Int, 4))
        List_Adp.SelectCommand.Parameters("@RowCount").Direction = ParameterDirection.Output

        List_Data = New DataSet()
        List_Adp.Fill(List_Data, "Canlist")

        Gd.DataSource = List_Data.Tables("Canlist")
        Gd.DataBind()
        Gd.Visible = True

        List_Adp.Dispose()
        Cn.Close()

    End Sub


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Jana_Calon", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Comment Original 7092013 - OSH 
        'Cmd.CommandText = "select id_kolej, dc_kolej from pn_kolej where jenis < 3 order by dc_kolej"        

        'Load Active College Only 090092023 - OSH
        Cmd.CommandText = "select id_kolej, dc_kolej from pn_kolej where jenis < 3 and aktif = '1' order by dc_kolej"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Add("")
        Cb_Pusat.Items.Add("")
        While Rdr.Read
            Dim x As New ListItem()
            x.Text = Rdr(1)
            x.Value = Rdr(0)
            Cb_Kolej.Items.Add(x)
            Cb_Pusat.Items.Add(x)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        If e.Row.RowIndex = -1 Then  Else e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
      

        e.Row.Cells(4).Visible = False 'kolej
        e.Row.Cells(9).Visible = False 'saring     
        e.Row.Cells(3).HorizontalAlign = HorizontalAlign.Center 'ic number column
        e.Row.Cells(5).HorizontalAlign = HorizontalAlign.Center 'session taking
        e.Row.Cells(7).HorizontalAlign = HorizontalAlign.Center 'pointer value
        e.Row.Cells(8).HorizontalAlign = HorizontalAlign.Center 'sick leave
        e.Row.Cells(10).HorizontalAlign = HorizontalAlign.Center 'repeat status
        e.Row.Cells(6).Width = Unit.Pixel(60)
        e.Row.Cells(7).Width = Unit.Pixel(60)
        e.Row.Cells(8).Width = Unit.Pixel(60)
        'Dim x As CheckBox = e.Row.FindControl("checkbox1")
        'x.UniqueID.
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Dim Jawatan, Jawatan2, Jawatan3, ULG As Integer
        If Cb_Kolej.SelectedIndex = 0 Then Cb_Kolej.Focus() : Exit Sub
        If Cb_Kursus.SelectedIndex = 0 Then Cb_Kursus.Focus() : Exit Sub

        'Replace change policy repeat 3 times, seating 4 07092012
        If Cb_Kursus.SelectedValue = 1 Then Jawatan = 1 : Jawatan2 = 5 : Jawatan3 = 8 : ULG = 4
        'Comment old policy repeat and seating Diploma, Degree and Covention 07092012 
        'If Cb_Kursus.SelectedValue = 1 Then Jawatan = 1 : Jawatan2 = 5 : Jawatan3 = 8 : ULG = 3
        If Cb_Kursus.SelectedValue = 2 Then Jawatan = 2 : Jawatan2 = 2 : Jawatan3 = 2 : ULG = 3
        If Cb_Kursus.SelectedValue = 3 Then Jawatan = 3 : Jawatan2 = 3 : Jawatan3 = 3 : ULG = 3
        If Cb_Kursus.SelectedValue = 4 Then Jawatan = 4 : Jawatan2 = 4 : Jawatan3 = 4 : ULG = 4

        'Comment Ori 22032016 -OSH
        'Cari("p.saring = 1 and p.id_kolej = " & Cb_Kolej.SelectedItem.Value & " and (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & " or p.j_kursus = " & Jawatan3 & ")", ULG)
        'Fixing query list via store procedure 22032016 - OSH
        Cari2(Trim(Cb_Kolej.SelectedItem.Value), Trim(Cb_Kursus.SelectedValue))
        If Gd.Rows.Count > 0 Then Panel1.Visible = True : Cb_Pusat.SelectedIndex = Cb_Kolej.SelectedIndex
    End Sub

    Protected Sub cmd_Jana_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Jana.Click
        Dim i As Int16, SQL As String = "", AG As Integer, Id_XM As Integer, Status As Integer, chk As CheckBox, sbb As DropDownList
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        If Cb_Kursus.SelectedValue = 5 Or Cb_Kursus.SelectedValue = 8 Then
            Cmd.CommandText = "select ag, id_xm from pn_xm where j_xm = 1 and status = 1"
        Else
            Cmd.CommandText = "select ag, id_xm from pn_xm where j_xm = '" & Cb_Kursus.SelectedItem.Value & "' and status = 1"
        End If
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            AG = Rdr(0)
            Id_XM = Rdr(1)
        Else
            Msg(Me, "Sila semak Siri Peperiksaan!")
            Exit Sub
        End If
        Rdr.Close()

        For i = 0 To Gd.Rows.Count - 1
            SQL += "delete from xm_calon_tolak where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "'; " & vbCrLf
        Next

        For i = 0 To Gd.Rows.Count - 1
            chk = Gd.Rows.Item(i).FindControl("CheckBox1")
            If Gd.Rows.Item(i).Cells(6).Text.Trim = "&nbsp;" And chk.Checked Then
                AG += 1
                Gd.Rows.Item(i).Cells(6).Text = Format(AG, "0000")
                If Gd.Rows.Item(i).Cells(11).Text = "BARU" Then Status = 0
                If Gd.Rows.Item(i).Cells(11).Text = "PERTAMA" Then Status = 1
                If Gd.Rows.Item(i).Cells(11).Text = "KEDUA" Then Status = 2
                If Gd.Rows.Item(i).Cells(11).Text = "KETIGA" Then Status = 3
                SQL += "insert xm_calon (nokp, id_xm, ag, id_pusat,ulangan) values ('" & Gd.Rows.Item(i).Cells(3).Text & "'," & Id_XM & ",'" & AG & "'," & Cb_Pusat.SelectedValue & "," & Status & "); "
                SQL += "update xm_calon set status_ulang='0' where nokp='" & Gd.Rows.Item(i).Cells(3).Text & "'; " & vbCrLf
            Else
                sbb = Gd.Rows.Item(i).FindControl("cb_sebab")
                SQL += "insert xm_calon_tolak (nokp, id_xm, sebab) values ('" & Gd.Rows.Item(i).Cells(3).Text & "'," & Id_XM & ",'" & sbb.SelectedItem.Text & "'); " & vbCrLf
            End If
        Next
        'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal)
        If SQL = "" Then Exit Sub

        SQL += "update pn_xm set ag = " & AG & " where id_xm = " & Id_XM
        'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal)
        'Update pn_xm set ag = ag"
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Cn.Close()
            Session("Msg_Tajuk") = "Jana Calon Peperiksaan."
            Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
            Response.Redirect("p0_Mesej.aspx")
            'Msg(Me, "Rekod Telah Dikemaskini...")
        Catch ex As Exception
            Msg(Me, ex.Message)
            Cn.Close()
        End Try
    End Sub

    Protected Sub CheckBox1_CheckedChanged(ByVal sender As Object, ByVal e As EventArgs)
        Dim i As Integer, x As DropDownList
        i = CInt(Mid(sender.uniqueid, InStr(sender.uniqueid, "$ctl") + 4, InStr(sender.uniqueid, "$CheckBox1") - InStr(sender.uniqueid, "$ctl") - 4))
        x = Gd.Rows.Item(i - 2).FindControl("cb_sebab")
        Gd.Rows.Item(i - 2).Cells(1).Wrap = False
        If sender.checked Then
            x.Visible = False
        Else
            x.Visible = True
        End If
    End Sub
End Class