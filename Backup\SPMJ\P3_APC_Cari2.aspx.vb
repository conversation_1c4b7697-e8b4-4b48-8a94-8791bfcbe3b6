﻿Imports System.Data.OleDb
Imports System.Data.SqlClient
Partial Public Class P3_APC_Cari2
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)

        'Comment Original 10042020 - OSH 
        'SQL = "select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', cast(jpa2.apc_no as varchar(6)) + '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN HADAPAN', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.j_daftar=jp.j_daftar and jpa.ret = 0 and jpa.apc_tahun = year(getdate()) and jpa.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_tahun = year(getdate())+1 and jpa2.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_tahun = year(getdate()) where jp.warganegara=1 and jp.j_daftar = " & Cb_Jenis.SelectedIndex & " and " & X & " order by jp.nama"
        'SQL = "select NAMA, jp.NOKP as 'NO. KP/PASPORT', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA'  from jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.apc_tahun = year(getdate()) where jp.j_daftar = " & Cb_Jenis.SelectedIndex & " and " & X & " order by jp.nama"

        'Fix query without cancel APC 10042020 - OSH 
        SQL = "select NAMA, jp.NOKP as 'NO. KP/PASPORT', case jp.j_daftar when 1 then 'JB-'+ cast(nopd as varchar(6)) when 2 then 'JM-'+ cast(nopd as varchar(6)) when 3 then 'PJ-'+ cast(nopd as varchar(6)) when 4 then 'B-'+ cast(nopd as varchar(6)) end as 'NO. PD', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) as 'NO. APC TAHUN SEMASA', cast(jpa2.apc_no as varchar(6)) + '/' + cast(jpa2.apc_tahun as varchar(4)) as 'NO. APC TAHUN HADAPAN', case jpa3.ret when 1 then 'RET ' + cast(jpa3.apc_tahun  as varchar(4)) end as 'PGKL. NAMA' from  jt_penuh jp left outer join jt_penuh_apc jpa on jp.nokp=jpa.nokp and jpa.j_daftar=jp.j_daftar and jpa.ret = 0 and jpa.apc_batal = 0 and jpa.apc_tahun = year(getdate()) and jpa.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa2 on jp.nokp=jpa2.nokp and jpa2.ret = 0 and jpa2.apc_batal = 0 and jpa2.apc_tahun = year(getdate())+1 and jpa2.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa3 on jp.nokp=jpa3.nokp and jpa3.ret = 1 and jpa3.apc_tahun = year(getdate()) where jp.warganegara=1 and jp.j_daftar = " & Cb_Jenis.SelectedIndex & " and " & X & " order by jp.nama"

        Tb = "jt_penuh"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        Gd.DataBind()
    End Sub

    Public Sub Memori()
        '***********************************
        '  Kena Tambah Variable untuk Id_PG
        '***********************************
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)

        'Comment Original 04012019 - OSH
        SQL = "select jp.NAMA, ta.nokp as 'No. KP', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD'  from tmp_apc ta inner join jt_penuh jp on ta.nokp = jp.nokp and ta.id_pg = '" & Session("Id_PG") & "' order by nama"

        'Add Previous APC on gridview 04012019 - OSH 
        'SQL = "select jp.NAMA, ta.nokp as 'No. KP', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) 'NO. APC TAHUN TERDAHULU',cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa2.apc_tahun as varchar(4)) 'NO. APC TAHUN SEMASA', cast(jpa2.apc_no as varchar(6)) + '/' + cast(jpa3.apc_tahun as varchar(4)) 'NO APC TAHUN HADAPAN', case jpa3.apc_no when 0 then 'RET ' + cast(jpa4.apc_tahun as varchar(4)) end as 'PGKL. NAMA' from tmp_apc ta inner join jt_penuh jp on ta.nokp = jp.nokp and ta.id_pg='" & Session("Id_PG") & "' left outer join jt_penuh_apc jpa on ta.nokp = jpa.nokp and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate())-1 and jpa.ret = 0 and jpa.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa2 on ta.nokp = jpa2.nokp and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate()) and jpa2.ret = 0 and jpa2.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa3 on ta.nokp = jpa3.nokp and jpa3.apc_tahun = year(getdate())+1 and jpa3.ret = 0 and jpa3.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa4 on ta.nokp = jpa4.nokp and jpa4.ret=1 and jpa4.apc_tahun=year(getdate()) order by nama"

        'Fix Query 17062019 - OSH 
        'SQL = "select jp.NAMA, ta.nokp as 'No. KP', case jp.j_daftar when 1 then 'JB-' + cast(jp.NOPD as varchar(6)) when 2 then 'JM-' + cast(jp.NOPD as varchar(6)) when 3 then 'PJ-' + cast(jp.NOPD as varchar(6)) when 4 then 'B-' + cast(jp.NOPD as varchar(6)) end as 'No. PD', cast(jpa.apc_no as varchar(6)) + '/' + cast(jpa.apc_tahun as varchar(4)) 'NO. APC TAHUN TERDAHULU',cast(jpa2.apc_no as varchar(6)) + '/' + cast(jpa2.apc_tahun as varchar(4)) 'NO. APC TAHUN SEMASA', cast(jpa3.apc_no as varchar(6)) + '/' + cast(jpa3.apc_tahun as varchar(4)) 'NO APC TAHUN HADAPAN', case jpa4.apc_no when 0 then 'RET ' + cast(jpa4.apc_tahun as varchar(4)) end as 'PGKL. NAMA' from tmp_apc ta inner join jt_penuh jp on ta.nokp = jp.nokp and ta.id_pg='" & Session("Id_PG") & "' left outer join jt_penuh_apc jpa on ta.nokp = jpa.nokp and jpa.j_daftar=jp.j_daftar and jpa.apc_tahun = year(getdate())-1 and jpa.ret = 0 and jpa.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa2 on ta.nokp = jpa2.nokp and jpa2.j_daftar=jp.j_daftar and jpa2.apc_tahun = year(getdate()) and jpa2.ret = 0 and jpa2.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa3 on ta.nokp = jpa3.nokp and jpa3.apc_tahun = year(getdate())+1 and jpa3.ret = 0 and jpa3.j_daftar = jp.j_daftar left outer join jt_penuh_apc jpa4 on ta.nokp = jpa4.nokp and jpa4.ret=1 and jpa4.apc_tahun=year(getdate()) order by nama"

        Tb = "tmp_apc"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd_Pilih.DataSource = List_Data.Tables(Tb)
        Gd_Pilih.DataBind()
        If Gd_Pilih.Rows.Count > 0 Then cmd_proses.visible = True Else cmd_proses.visible = False
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If IsPostBack Then Exit Sub
        Memori()
    End Sub

    Private Sub Gd_RowCommand(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewCommandEventArgs) Handles Gd.RowCommand


        'Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        'Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'If e.CommandName = "Pilih" Then
        '    Try
        '        Cmd.CommandText = "insert tmp_apc (id_pg,nokp) values ('" & Session("Id_PG") & "','" & Gd.SelectedRow.Cells(3).Text & "')"
        '        Cmd.ExecuteNonQuery()
        '        Cn.Close()
        '        'System.Threading.Thread.Sleep(3000)
        '        Memori()
        '    Catch ex As Exception
        '        Cn.Close()
        '        Msg(Me, "Rekod Sudah Dipilih!")
        '    End Try
        'ElseIf e.CommandName = "Pinda" Then
        '    Session("NOKP") = Gd.SelectedRow.Cells(4).Text
        '    Response.Redirect("P2_Penuh.aspx")
        'End If


    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(0).Width = Unit.Pixel(1)
        e.Row.Cells(1).Width = Unit.Pixel(1)
        e.Row.Height = Unit.Pixel(1)
        e.Row.Cells(4).Width = Unit.Pixel(60)
        e.Row.Cells(5).Width = Unit.Pixel(60)
        e.Row.Cells(6).Width = Unit.Pixel(60)
        e.Row.Cells(7).Width = Unit.Pixel(50)
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(2).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Cb_Jenis.SelectedIndex < 1 Then Exit Sub
        Cari("nama like '" & Tx_Nama.Text & "%' and jp.nokp like '" & Tx_NoKP.Text & "%' and nopd like '" & Tx_NoPd.Text & "%'")
    End Sub

    Private Sub Gd_Pilih_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd_Pilih.RowCreated
        e.Row.Cells(0).Width = Unit.Pixel(1)
        e.Row.Cells(1).Width = Unit.Pixel(20)
        e.Row.Cells(3).Width = Unit.Pixel(50)
        e.Row.Cells(4).Width = Unit.Pixel(50)
        e.Row.VerticalAlign = VerticalAlign.Top
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
        e.Row.Cells(3).ForeColor = Drawing.Color.Maroon
        e.Row.Cells(3).Font.Bold = True
        e.Row.Font.Size = FontUnit.Point(7)

    End Sub

    Protected Sub Gd_Pilih_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd_Pilih.SelectedIndexChanged
        'If Msg(Me, "Padam Rekod Ini - " & Gd_Pilih.SelectedRow.Cells(3).Text & "?") = MsgBoxResult.Yes Then
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "delete tmp_apc where nokp = '" & Gd_Pilih.SelectedRow.Cells(3).Text & "'"
        Cmd.ExecuteNonQuery()
        Cn.Close()
        Memori()
        'End If
    End Sub

    Protected Sub cmd_Proses_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Proses.Click
        'Response.Redirect("p3_apc_proses.aspx")
        Response.Redirect("p3_apc_proses_j.aspx")
    End Sub

    Protected Sub Button4_Click(ByVal sender As Object, ByVal e As EventArgs)
        Dim i As Integer
        i = CInt(Mid(sender.uniqueid, InStr(sender.uniqueid, "$ctl") + 4, InStr(sender.uniqueid, "$B") - InStr(sender.uniqueid, "$ctl") - 4)) - 2
        Session("PINDA") = True
        Session("NOKP") = Gd.Rows(i).Cells(4).Text
        Response.Redirect("P2_Penuh.aspx")
    End Sub

    Protected Sub Button3_Click(ByVal sender As Object, ByVal e As EventArgs)
        Dim i As Integer
        i = CInt(Mid(sender.uniqueid, InStr(sender.uniqueid, "$ctl") + 4, InStr(sender.uniqueid, "$B") - InStr(sender.uniqueid, "$ctl") - 4)) - 2

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Check current or future APC exist before add tray 22052020 - OSH 
        Try
            Cmd.CommandText = "select * from jt_penuh_apc where apc_tahun = year(getdate()) or apc_tahun = year(getdate())+1 and  nokp = '" & Gd.Rows(i).Cells(4).Text & "'"
            Rdr = Cmd.ExecuteReader
            If Rdr.Read Then
                Msg(Me, "APC Tahun Semasa atau Hadapan Telah Wujud !")
                Rdr.Close()
                Cn.Close()
            Else
                Cmd.CommandText = "select * from tmp_apc where nokp = '" & Gd.Rows(i).Cells(4).Text & "'"
                Rdr = Cmd.ExecuteReader
                If Rdr.Read Then
                    Msg(Me, "Rekod telah wujud")
                    Rdr.Close()
                    Cn.Close()
                Else
                    Cmd.CommandText = "insert tmp_apc (id_pg,nokp) values ('" & Session("Id_PG") & "','" & Gd.Rows(i).Cells(4).Text & "')"
                    Cmd.ExecuteNonQuery()
                    Cn.Close()
                    Memori()
                End If
            End If
        Catch ex As Exception
            Cn.Close()
            Msg(Me, ex.Message)
        End Try

        'Comment Original 22052020 - OSH 
        'Try
        '    Cmd.CommandText = "select * from tmp_apc where nokp = '" & Gd.Rows(i).Cells(4).Text & "'"
        '    Rdr = Cmd.ExecuteReader
        '    If Rdr.Read Then
        '        Msg(Me, "Rekod telah wujud")
        '        Rdr.Close()
        '        Cn.Close()
        '    Else
        '        Rdr.Close()
        '        Cmd.CommandText = "insert tmp_apc (id_pg,nokp) values ('" & Session("Id_PG") & "','" & Gd.Rows(i).Cells(4).Text & "')"
        '        Cmd.ExecuteNonQuery()
        '        Cn.Close()
        '        Memori()
        '    End If
        'Catch ex As Exception
        '    Cn.Close()
        '    Msg(Me, ex.Message)
        'End Try

        'Comment Ori 18072018 -OSH
        'Try
        '    Cmd.CommandText = "insert tmp_apc (id_pg,nokp) values ('" & Session("Id_PG") & "','" & Gd.Rows(i).Cells(4).Text & "')"
        '    Cmd.ExecuteNonQuery()
        '    Cn.Close()
        '    'System.Threading.Thread.Sleep(3000)
        '    Memori()
        'Catch ex As Exception
        '    Cn.Close()
        '    Msg(Me, "Rekod Sudah Dipilih!")
        'End Try
    End Sub
End Class
