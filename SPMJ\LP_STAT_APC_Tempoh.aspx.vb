﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_APC_Tempoh
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'TAHUN APC
        For i = Now.Year To Now.Year - 5 Step -1
            Cb_Tahun.Items.Add(i)
        Next

        'JAWATAN
        With Cb_Jawatan
            .Items.Clear()
            .Items.Add("Jururawat Berdaftar")
            .Items.Item(.Items.Count - 1).Value = "1"
            .Items.Add("Jururawat Masyarakat")
            .Items.Item(.Items.Count - 1).Value = "2"
            .Items.Add("Penolong Jururawat")
            .Items.Item(.Items.Count - 1).Value = "3"
        End With
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim tahun As Integer = Cb_Tahun.Text
        Dim j_daftar As Integer = Cb_Jawatan.SelectedValue
        Dim L, P, X, T1, T2, T3 As Long
        Dim Tajuk, Tajuk2 As String

        Tajuk = "Laporan Statistik Pengeluaran APC Bagi " & Cb_Jawatan.SelectedItem.Text & ", " & tahun
        Tajuk2 = "mengikut Tempoh Tahun dan Jantina"

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='11' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='11' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td rowspan='3' style='vertical-align: middle; text-align: left;'>TEMPOH PENGEKALAN NAMA</td>"
        For i As Integer = 0 To 2
            Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & tahun + i & "</td>"
        Next
        Header += "    <td rowspan='3' style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>L</td><td>P</td><td>X</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>L</td><td>P</td><td>X</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>L</td><td>P</td><td>X</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        Header = ""
        Header += "<tr>"

        L = 0 : P = 0 : X = 0
        'Improve query based on operation year 01062020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        'Comment Original 01062020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()

        'Improve query based on operation year 01062020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        'Comment Original 01062020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()

        'Improve query based on operation year 01062020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        'Comment Original 01062020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T1 = L + P + X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

        L = 0 : P = 0 : X = 0
        'Improve query based on operation year 01062020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and apc_tahun=" & tahun + 1 & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        'Comment Original 01062020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun + 1 & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()
        'Improve query based on operation year 01062020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and apc_tahun=" & tahun + 1 & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        'Comment Original 01062020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun + 1 & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()

        'Improve query based on operation year 01062020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and apc_tahun=" & tahun + 1 & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        'Comment Original 01062020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun + 1 & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T2 = L + P + X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"

        L = 0 : P = 0 : X = 0
        'Improve query based on operation year 01062020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and apc_tahun=" & tahun + 2 & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        'Comment Original 01062020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun + 2 & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=1)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then L = Rdr(0)
        Rdr.Close()

        'Improve query based on operation year 01062020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and apc_tahun=" & tahun + 2 & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        'Comment Original 01062020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun + 2 & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh where jantina=2)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then P = Rdr(0)
        Rdr.Close()

        'Improve query based on operation year 01062020 - OSH 
        Cmd.CommandText = "select count(distinct nokp) from jt_penuh_apc where year(log_tkh)=" & tahun & " and apc_tahun=" & tahun + 2 & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        'Comment Original 01062020 - OSH 
        'Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun + 2 & " and j_daftar=" & j_daftar & " and ret=1 and nokp in (select nokp from jt_penuh)"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then X = Rdr(0) : X = X - L - P
        Rdr.Close()
        T3 = L + P + X
        Header += "    <td>" & L & "</td><td>" & P & "</td><td>" & X & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>JUMLAH KESELURUHAN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T1 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T2 & "</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>" & T3 & "</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>" & T1 + T2 + T3 & "</td>"
        Header += "</tr>"

        Response.Write(Header)
        Response.Write("</table>")

        'Add Footnote 01062020 - OSH 
        Header = ""
        Header = "<table>"
        Header += "<tr></tr>"
        Header += "<tr></tr>"
        Header += "<tr>"
        Header += "    <td colspan='15'>X dan (space): Rekod yang didaftarkan tidak lengkap</td> "
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='15'>Laporan ini berdasarkan jumlah individu telah mengekalkan nama bagi tahun operasi, diatas mengikut jantina dan tahun pengekalan daftar jururawat </td> "
        Header += "</tr>"
        Response.Write(Header)
        Response.Write("</table>")
        Response.End()
        Response.Flush()


    End Sub


End Class