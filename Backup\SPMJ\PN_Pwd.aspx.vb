﻿Imports System.Data.OleDb

Partial Public Class WebForm13
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Tx_Pwd.Text = "" Then Tx_Pwd.Focus() : Exit Sub
        If Tx_Pwd.Text.ToUpper = Session("pwd").ToString.ToUpper Then
            If Tx_Pwd2.Text = "" Then Tx_Pwd2.Focus() : Exit Sub
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

            Cmd.CommandText = "update pn_pengguna set pwd = '" & Tx_Pwd2.Text & "' where id_pg='" & Session("Id_PG") & "'"
            Cmd.ExecuteNonQuery()
            Cn.Close()
            Msg(Me, "Rekod Telah Disimpan.")
        End If
    End Sub
End Class