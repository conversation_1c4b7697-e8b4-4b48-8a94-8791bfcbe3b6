﻿Public Partial Class WebForm47
    Inherits System.Web.UI.Page

    Public x As String

    Public Sub Surat_Imigresen_Ulangan()
        x = ""
        x += Header_Surat(1, 1)

        'Rujukan
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 12pt;'><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Ruj. Tuan : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'> </td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Ruj. Kami :</td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'> KKM 87/A3/1/132() </td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 12pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#151B8D;'> Tarikh &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 12pt; color:#000000;'> " + Tarikh(1) + "</td>"
        x += "</tr></table>"

        'Penerima
        x += "<div style='font-family: Arial; font-size: 12pt;'>"
        x += "<br/>Urusetia"
        x += "<br/>Jawatankuasa Pegawai Dagang"
        x += "<br/>Bahagian Pas Penggajian"
        x += "<br/>Jabatan Imigresen Malaysia"
        x += "<br/>No. 15, Tingkat 3, Persiaran Perdana, Presint 2"
        x += "<br/>Pusat Pentadbiran Kerajaan Persekutuan"
        x += "<br/>62550 Putrajaya."
        x += "</div>"

        x += "<br/>"
        x += "<div  style='font-family: Arial; font-size: 12pt;'>Tuan,"
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'><b>KELULUSAN PERLANJUTAN PERKHIDMATAN PENGAJAR JURURAWAT, INSTRUKTOR KLINIKAL DAN JURURAWAT TERLATIH WARGANEGARA ASING"
        x += "</b></div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'>Dengan segala hormatnya merujuk perkara di atas."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "2.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Dimaklumkan Lembaga Jururawat Malaysia meluluskan perlanjutan perkhidmatan pengajar kejururawatan,instruktor klinikal dan jururawat terlatih "
        x += "warganegara asing untuk berkhidmat di institusi seperti disenaraikan di lampiran 1. Pihak Majikan dinasihatkan untuk memohon Perakuan Pengamalan Sementara pada setiap tahun mengikut keperluan."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "Sekian, terima kasih."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "<b> &quot;BERKHIDMAT UNTUK NEGARA&quot; </b>"
        x += "</div>"
        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "Saya yang menurut perintah,"
        x += "</div>"

        x += "<br>"
        x += "<br>"
        x += "</br>"


        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "<b> (DATO' HJH. FATHILAH BINTI HJ. ABD. WAHAB) </b>"
        x += "<br>Pendaftar,"
        x += "<br>Lembaga Jururawat Malaysia,"
        x += "<br>Kementerian Kesihatan Malaysia."

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "<br>s.k"
        x += "<br>"
        x += "<br>  Ketua Penolong Pengarah"
        x += "<br>  Cawangan Kawalan Amalan Perubatan Swasta"
        x += "<br>  Bahagian Amalan Perubatan"
        x += "<br>  Kementerian Kesihatan Malaysia"
        x += "<br>  62590 Putrajaya"
        x += "<br>"
        x += "<br>  Pengurus Sumber Manusia"
        x += "<br>  Institusi yang berkenaan"
        x += "<br>"
        x += "<br>"
        x += "<br>  Fail"
        x += "</div>"

        x += Footer_Surat()

        'Response.Write(x)
        'Exit Sub

        Dim fileName As String = "Surat.doc"
        Response.Clear()
        Response.Buffer = True
        ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)
    End Sub


    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        Surat_Imigresen_Ulangan()
    End Sub
End Class