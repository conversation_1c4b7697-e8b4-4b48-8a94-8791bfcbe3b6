﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class P3_Cetak
    Inherits System.Web.UI.Page
    Public x As String

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Public Sub Slip()
        Dim x, cb As String
        If Cb_Jenis.SelectedValue = 1 Then cb = "87/A3/5/5" Else cb = "87/A3/5/6-(&nbsp;&nbsp;)"

        x = ""
        x += Header_Surat_Blank(1)
        x += "<div  style='font-family: Arial; font-size: 12pt; text-align:center;'>"
        x += "<br><b>SURAT AKUAN PENERIMAAN SIJIL PERAKUAN PENGAMALAN TAHUNAN"
        x += "<br>JURURAWAT BERDAFTAR (" & Cb_Jenis.SelectedItem.Text & ")</b>"
        x += "<br>"
        x += "</div>"

        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 10pt;'><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 10pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 10pt;'> Rujukan Tuan : " & cb & " JLD(&nbsp;&nbsp;)/(&nbsp;&nbsp;) </td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 10pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 10pt;'> Tarikh :</td>"
        x += "</tr></table>"


        x += "<div style='font-family: Arial; font-size: 12pt;'>"
        x += "<br>........................................."
        x += "<br>........................................."
        x += "<br>........................................."
        x += "<br>........................................."
        x += "<br>........................................."
        x += "<br>"
        x += "<br>Kepada :"
        x += "<br>"
        x += "<br>Pendaftar,"
        x += "<br>Lembaga Jururawat Malaysia,"
        x += "<br>Aras 3, Blok E1, Kompleks E, Presint 1,"
        x += "<br>Pusat Pentadbiran Kerajaan Persekutuan,"
        x += "<br><u>62250 Putrajaya.</u>"
        x += "</div>"

        x += "<br/>"
        x += "<div  style='font-family: Arial; font-size: 12pt;'>Tuan/Puan,"
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'><b>SIJIL PERAKUAN PENGAMALAN TAHUNAN JURURAWAT BERDAFTAR TAHUN ................."
        x += "</b></div>"

        x += "<br/>"
        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 12pt; text-align:justify;'>"
        x += "Merujuk kepada surat tuan/puan bil. ......................................... bertarikh ...................... dimaklumkan bahawa saya telah menerima sijil tersebut sebanyak ............ keping."
        x += "</div>"

        x += "<br/>"
        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "Sekian, terima kasih."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 12pt;'>"
        x += "<br/>"
        x += "<br/>"
        x += "<br/>"
        x += "<br/>"
        x += "<br/>"
        x += "........................................."
        x += "<br>(&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)"
        x += "<br></div><b><div style='font-family: Arial; font-size: 9pt;'>* <i>SURAT JAWAPAN INI HENDAKLAH DIKEMBALIKAN KE LEMBAGA JURURAWAT MALAYSIA"
        x += "</b></div>"
        x += Footer_Surat_Blank()

        Dim fileName As String = "Surat.doc"
        Response.Clear()
        Response.Buffer = True
        ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)

    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Slip()
    End Sub
End Class