Imports System.Data.OleDb
Imports System.Text

Public Partial Class PasswordMigration
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Security check - only allow access from localhost or specific IPs
        If Not IsAuthorizedAccess() Then
            Response.StatusCode = 403
            Response.End()
        End If
        
        If Not IsPostBack Then
            CheckMigrationStatus()
        End If
    End Sub

    Private Function IsAuthorizedAccess() As Boolean
        Dim clientIP As String = Request.UserHostAddress
        ' Allow localhost access only - modify as needed for your environment
        Return clientIP = "127.0.0.1" OrElse clientIP = "::1" OrElse clientIP.StartsWith("192.168.") OrElse clientIP.StartsWith("10.")
    End Function

    Protected Sub btnCheckStatus_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnCheckStatus.Click
        CheckMigrationStatus()
    End Sub

    Protected Sub btnMigrate_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnMigrate.Click
        PerformMigration()
    End Sub

    Private Sub CheckMigrationStatus()
        Dim Cn As New OleDbConnection
        Dim Cmd As New OleDbCommand
        Dim Rdr As OleDbDataReader
        Dim results As New List(Of MigrationResult)
        Dim logText As New StringBuilder()

        Try
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn

            logText.AppendLine("=== Migration Status Check ===")
            logText.AppendLine("Timestamp: " & DateTime.Now.ToString())
            logText.AppendLine()

            ' Get all active users
            Cmd.CommandText = "SELECT id_pg, pwd, tkh_daftar FROM pn_pengguna WHERE status = 1 ORDER BY id_pg"
            Rdr = Cmd.ExecuteReader()

            Dim totalUsers As Integer = 0
            Dim hashedUsers As Integer = 0
            Dim plainTextUsers As Integer = 0

            While Rdr.Read()
                totalUsers += 1
                Dim userId As String = Rdr("id_pg").ToString()
                Dim password As String = Rdr("pwd").ToString()
                Dim lastUpdated As String = If(Rdr("tkh_daftar") IsNot DBNull.Value, Rdr("tkh_daftar").ToString(), "N/A")

                Dim result As New MigrationResult()
                result.UserId = userId
                result.LastUpdated = lastUpdated

                ' Check if password is already hashed
                If password.Length = 64 AndAlso System.Text.RegularExpressions.Regex.IsMatch(password, "^[a-fA-F0-9]+$") Then
                    result.PasswordType = "Hashed (Secure)"
                    result.Status = "Already Migrated"
                    hashedUsers += 1
                Else
                    result.PasswordType = "Plain Text"
                    result.Status = "Needs Migration"
                    plainTextUsers += 1
                End If

                results.Add(result)
            End While

            Rdr.Close()

            ' Display summary
            logText.AppendLine("Migration Status Summary:")
            logText.AppendLine("Total Users: " & totalUsers.ToString())
            logText.AppendLine("Already Migrated (Hashed): " & hashedUsers.ToString())
            logText.AppendLine("Need Migration (Plain Text): " & plainTextUsers.ToString())
            logText.AppendLine("Migration Progress: " & Math.Round((hashedUsers / totalUsers) * 100, 2).ToString() & "%")

            If plainTextUsers = 0 Then
                lblResults.Text = "<div class='success'><strong>Migration Complete!</strong> All " & totalUsers.ToString() & " user passwords are already securely hashed.</div>"
                btnMigrate.Enabled = False
            Else
                lblResults.Text = "<div class='warning'><strong>Migration Needed:</strong> " & plainTextUsers.ToString() & " out of " & totalUsers.ToString() & " users still have plain text passwords.</div>"
                btnMigrate.Enabled = True
            End If

        Catch ex As Exception
            logText.AppendLine("Error during status check: " & ex.Message)
            lblResults.Text = "<div class='error'><strong>Error:</strong> " & ex.Message & "</div>"
        Finally
            If Not Rdr Is Nothing AndAlso Not Rdr.IsClosed Then Rdr.Close()
            If Cn.State = ConnectionState.Open Then Cn.Close()
        End Try

        ' Bind results to grid
        gvMigrationResults.DataSource = results
        gvMigrationResults.DataBind()

        txtLog.Text = logText.ToString()
    End Sub

    Private Sub PerformMigration()
        Dim Cn As New OleDbConnection
        Dim Cmd As New OleDbCommand
        Dim Rdr As OleDbDataReader
        Dim updateCmd As New OleDbCommand
        Dim logText As New StringBuilder()

        Try
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn
            updateCmd.Connection = Cn

            logText.AppendLine("=== Password Migration Process ===")
            logText.AppendLine("Started: " & DateTime.Now.ToString())
            logText.AppendLine()

            ' Get all users with plain text passwords
            Cmd.CommandText = "SELECT id_pg, pwd FROM pn_pengguna WHERE status = 1"
            Rdr = Cmd.ExecuteReader()

            Dim usersToUpdate As New List(Of KeyValuePair(Of String, String))

            While Rdr.Read()
                Dim userId As String = Rdr("id_pg").ToString()
                Dim password As String = Rdr("pwd").ToString()

                ' Check if password is not already hashed
                If Not (password.Length = 64 AndAlso System.Text.RegularExpressions.Regex.IsMatch(password, "^[a-fA-F0-9]+$")) Then
                    usersToUpdate.Add(New KeyValuePair(Of String, String)(userId, password))
                    logText.AppendLine("Found plain text password for user: " & userId)
                End If
            End While

            Rdr.Close()

            If usersToUpdate.Count = 0 Then
                logText.AppendLine("No users found with plain text passwords. Migration not needed.")
                lblResults.Text = "<div class='success'><strong>No Migration Needed:</strong> All passwords are already hashed.</div>"
                txtLog.Text = logText.ToString()
                Return
            End If

            logText.AppendLine()
            logText.AppendLine("Starting migration for " & usersToUpdate.Count.ToString() & " users...")
            logText.AppendLine()

            Dim successCount As Integer = 0
            Dim errorCount As Integer = 0

            ' Update passwords to hashed versions
            For Each user In usersToUpdate
                Try
                    Dim hashedPassword As String = HashPassword(user.Value)
                    updateCmd.Parameters.Clear()
                    updateCmd.CommandText = "UPDATE pn_pengguna SET pwd = ? WHERE id_pg = ?"
                    updateCmd.Parameters.Add("@pwd", OleDbType.VarChar).Value = hashedPassword
                    updateCmd.Parameters.Add("@id_pg", OleDbType.VarChar).Value = user.Key
                    updateCmd.ExecuteNonQuery()

                    logText.AppendLine("✓ Successfully migrated password for user: " & user.Key)
                    successCount += 1

                Catch ex As Exception
                    logText.AppendLine("✗ Failed to migrate password for user: " & user.Key & " - " & ex.Message)
                    errorCount += 1
                End Try
            Next

            logText.AppendLine()
            logText.AppendLine("Migration completed: " & DateTime.Now.ToString())
            logText.AppendLine("Successful migrations: " & successCount.ToString())
            logText.AppendLine("Failed migrations: " & errorCount.ToString())

            If errorCount = 0 Then
                lblResults.Text = "<div class='success'><strong>Migration Successful!</strong> All " & successCount.ToString() & " passwords have been securely hashed.</div>"
            Else
                lblResults.Text = "<div class='warning'><strong>Migration Partially Complete:</strong> " & successCount.ToString() & " successful, " & errorCount.ToString() & " failed. Check log for details.</div>"
            End If

            ' Log security event
            SecurityUtility.LogSecurityEvent("PASSWORD_MIGRATION", "SYSTEM", "Migrated " & successCount.ToString() & " passwords to secure hash")

        Catch ex As Exception
            logText.AppendLine("Critical error during migration: " & ex.Message)
            lblResults.Text = "<div class='error'><strong>Migration Failed:</strong> " & ex.Message & "</div>"
        Finally
            If Not Rdr Is Nothing AndAlso Not Rdr.IsClosed Then Rdr.Close()
            If Cn.State = ConnectionState.Open Then Cn.Close()
        End Try

        txtLog.Text = logText.ToString()
        
        ' Refresh status after migration
        CheckMigrationStatus()
    End Sub

    Public Class MigrationResult
        Public Property UserId As String
        Public Property Status As String
        Public Property PasswordType As String
        Public Property LastUpdated As String
    End Class

End Class
