﻿Imports iTextSharp.text.pdf
Imports iTextSharp.text

Public Class PageFooter
    Inherits PdfPageEventHelper

    Public Overrides Sub OnEndPage(ByVal writer As iTextSharp.text.pdf.PdfWriter, ByVal document As iTextSharp.text.Document)
        'Add Footer Paragraph - Center 30102019 - OSH 
        'Dim ch <PERSON>("Mukasurat " & writer.PageNumber )

        Dim ph As New Phrase()
        Dim p As New Paragraph()
        Dim ch As New Chunk("Mukasurat " & writer.CurrentPageNumber & "/" & writer.PageNumber)
        ph.Add(ch)
        p.Add(ph)
        p.SpacingBefore = 20
        p.SpacingAfter = 20
        p.Alignment = 1 'center
        document.Add(p)

    End Sub

End Class
