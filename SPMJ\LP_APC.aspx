﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="LP_APC.aspx.vb" Inherits="SPMJ.WebForm42" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 23px;
        }
        .style2
        {
            height: 7px;
        }
        .style3
        {
            height: 24px;
            width: 243px;
        }
        .style4
        {
            height: 24px;
            width: 507px;
        }
        .style5
        {
            width: 507px;
        }
        .style6
        {
            width: 243px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td></td>
            <td width="600"></td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style1"></td>
            <td width="600" align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#719548" class="style1">Laporan&nbsp; - Modul Pembaharuan Apc</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td></td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">
                <asp:UpdatePanel ID="UpdatePanel9" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj17" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">JAWATAN</asp:TextBox>
                        <asp:DropDownList ID="Cb_Jawatan" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="250px">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">
                <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj3" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">NEGERI</asp:TextBox>
                        <asp:DropDownList ID="Cb_Negeri" runat="server" Font-Names="Arial" 
            Font-Size="8pt" Width="250px" 
    AutoPostBack="True">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">
                <asp:UpdatePanel ID="UpdatePanel3" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj10" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">TEMPAT AMALAN</asp:TextBox>
                        <asp:DropDownList ID="Cb_Tpt_Amalan" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="250px">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">
                <asp:UpdatePanel ID="UpdatePanel4" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj11" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">TAHUN APC</asp:TextBox>
                        <asp:DropDownList ID="Cb_Tahun" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="250px">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">
                <asp:UpdatePanel ID="UpdatePanel8" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj15" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">NO. APC</asp:TextBox>
                        <asp:TextBox ID="Tx_NoAPC1" runat="server" CssClass="std" Width="80px" 
                            Wrap="False"></asp:TextBox>
                        <asp:TextBox ID="Cb_Sbj16" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="10px">-</asp:TextBox>
                        <asp:TextBox ID="Tx_NoAPC2" runat="server" CssClass="std" Width="80px" 
                            Wrap="False"></asp:TextBox>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">
                <asp:UpdatePanel ID="UpdatePanel10" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj18" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">TARIKH APC</asp:TextBox>
                        <asp:TextBox ID="Tx_Tkh_APC" runat="server" CssClass="std" Width="80px"></asp:TextBox>
                        <cc1:MaskedEditExtender ID="Tx_Tkh_APC_MaskedEditExtender" runat="server" 
                            CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                            CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                            CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                            Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_APC" 
                            UserDateFormat="DayMonthYear">
                        </cc1:MaskedEditExtender>
                        <cc1:CalendarExtender ID="Tx_Tkh_APC_CalendarExtender" runat="server" 
                            Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                            TargetControlID="Tx_Tkh_APC">
                        </cc1:CalendarExtender>
                        <asp:TextBox ID="Cb_Sbj20" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="10px">-</asp:TextBox>
                        <asp:TextBox ID="Tx_Tkh_APC2" runat="server" CssClass="std" Width="80px"></asp:TextBox>
                        <cc1:MaskedEditExtender ID="Tx_Tkh_APC2_MaskedEditExtender" runat="server" 
                            CultureAMPMPlaceholder="" CultureCurrencySymbolPlaceholder="" 
                            CultureDateFormat="" CultureDatePlaceholder="" CultureDecimalPlaceholder="" 
                            CultureName="en-GB" CultureThousandsPlaceholder="" CultureTimePlaceholder="" 
                            Enabled="True" Mask="99/99/9999" MaskType="Date" TargetControlID="Tx_Tkh_APC2" 
                            UserDateFormat="DayMonthYear">
                        </cc1:MaskedEditExtender>
                        <cc1:CalendarExtender ID="Tx_Tkh_APC2_CalendarExtender" runat="server" 
                            Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                            TargetControlID="Tx_Tkh_APC2">
                        </cc1:CalendarExtender>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">
                <asp:UpdatePanel ID="UpdatePanel11" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj19" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">TARIKH RET</asp:TextBox>
                        <asp:TextBox ID="Tx_Tahun_RET" runat="server" CssClass="std" Width="80px" 
                            Wrap="False"></asp:TextBox>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">
                <asp:UpdatePanel ID="UpdatePanel7" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj14" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">JANTINA</asp:TextBox>
                        <asp:DropDownList ID="Cb_Jantina" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="250px">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">
                <asp:UpdatePanel ID="UpdatePanel6" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj13" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">BANGSA</asp:TextBox>
                        <asp:DropDownList ID="Cb_Bangsa" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="250px">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">
                <asp:UpdatePanel ID="UpdatePanel12" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj21" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">SEKTOR</asp:TextBox>
                        <asp:DropDownList ID="Cb_Sektor" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="250px">
                            <asp:ListItem>(SEMUA)</asp:ListItem>
                            <asp:ListItem Value="1">KERAJAAN</asp:ListItem>
                            <asp:ListItem Value="2">SWASTA</asp:ListItem>
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">
                <asp:UpdatePanel ID="UpdatePanel13" runat="server" Visible="False">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj22" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">STATUS MOHON</asp:TextBox>
                        <asp:DropDownList ID="Cb_Status" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="250px">
                            <asp:ListItem>(SEMUA)</asp:ListItem>
                            <asp:ListItem Value="1">LENGKAP</asp:ListItem>
                            <asp:ListItem Value="2">TIDAK LENGKAP</asp:ListItem>
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style2">
                &nbsp;</td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style2">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <table style="border-width: 1px; border-color: #000000; width:100%; border-top-style: solid; border-bottom-style: solid;">
                            <tr>
                                <td class="style3">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                </td>
                                <td align="char" class="style4" width="10%">
                                    <asp:CheckBoxList ID="Chk_Pilih" runat="server" Font-Names="Arial" 
                                        Font-Size="8pt" RepeatColumns="2" Width="80%" Height="168px">
                                        <asp:ListItem Value="nama">NAMA</asp:ListItem>
                                        <asp:ListItem Value="nokp">NO. KP</asp:ListItem>
                                        <asp:ListItem Value="nopd">NO. PENDAFTARAN</asp:ListItem>
                                        <asp:ListItem Value="j_daftar">JAWATAN</asp:ListItem>
                                        <asp:ListItem Value="bangsa">BANGSA</asp:ListItem>
                                        <asp:ListItem Value="apc_no">NO. APC</asp:ListItem>
                                        <asp:ListItem Value="id_amalan">TEMPAT AMALAN</asp:ListItem>
                                        <asp:ListItem>NEGERI TEMPAT LATIHAN</asp:ListItem>
                                        <asp:ListItem>SEKTOR</asp:ListItem>
                                        <asp:ListItem>CPD</asp:ListItem>
                                        <asp:ListItem>PENGEKALAN NAMA</asp:ListItem>
                                        <asp:ListItem>GRED JAWATAN</asp:ListItem>
                                        <asp:ListItem>TAHUN APC</asp:ListItem>
                                        <asp:ListItem>CATATAN</asp:ListItem>
                                    </asp:CheckBoxList>
                                </td>
                            </tr>
                            <tr>
                                <td class="style6">
                                    &nbsp;</td>
                                <td class="style5">
                                    &nbsp;</td>
                            </tr>
                </table>
            </td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style2">
                &nbsp;</td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px"></asp:TextBox>
                <asp:Button ID="cmd_Cari1" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="JANA" Width="80px" />
            &nbsp;<asp:Button ID="cmd_Cari2" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="EXCEL" Width="80px" />
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td width="600" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; border-bottom-style: solid; border-bottom-width: 1px;" 
                bgcolor="White">
                &nbsp;&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td></td>
            <td width="600">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td width="600">
                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                    <ContentTemplate>
                        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="600px" GridLines="Horizontal" BorderColor="#719548">
                            <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                            <RowStyle BackColor="#F4F4F4" Height="21px" />
                            <Columns>
                                <asp:TemplateField HeaderText="#">
                                    <HeaderStyle HorizontalAlign="Center" />
                                    <ItemStyle HorizontalAlign="Center" Width="30px" />
                                </asp:TemplateField>
                            </Columns>
                            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                            <SelectedRowStyle Font-Bold="True" />
                            <HeaderStyle BackColor="#7EA851" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                            <AlternatingRowStyle BackColor="White" />
                        </asp:GridView>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td></td>
        </tr>
    
    
        <tr>
            <td>&nbsp;</td>
            <td width="600">
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>
    
    
    </table>
    
    
    </div></asp:Content>
