﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm41
    Inherits System.Web.UI.Page


    Public Sub Cari(ByVal X As String)
        Dim Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Tb = "jt_penuh"

        Dim List_Adp As New SqlDataAdapter(X, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Add verify login 06122019 -OSH 
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'Fix security access 23072018 - OSH 
        If Session("ORIGIN") = "yes" Then

            If IsPostBack Then Exit Sub

            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

            'KOLEJ
            Cmd.CommandText = "select dc_kolej, id_kolej from pn_kolej order by dc_kolej"
            Rdr = Cmd.ExecuteReader()
            Cb_Kolej.Items.Clear()
            Cb_Kolej.Items.Add("(SEMUA)")
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = ""
            While Rdr.Read
                Cb_Kolej.Items.Add(Rdr(0))
                Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
            End While
            Rdr.Close()
            Cn.Close()

            'JAWATAN
            Cb_Kursus.Items.Clear()
            Cb_Kursus.Items.Add("(SEMUA)")
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = ""
            Cb_Kursus.Items.Add("JURURAWAT BERDAFTAR")
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "1"
            Cb_Kursus.Items.Add("JURURAWAT MASYARAKAT")
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "2"
            Cb_Kursus.Items.Add("PENOLONG JURURAWAT")
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "3"
            Cb_Kursus.Items.Add("BIDAN")
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "4"
        Else
            Response.Redirect("p0_Login.aspx")
        End If

        'Comment Ori 23072018 -OSH
        'If IsPostBack Then Exit Sub

        'Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        'Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        ''KOLEJ
        'Cmd.CommandText = "select dc_kolej, id_kolej from pn_kolej order by dc_kolej"
        'Rdr = Cmd.ExecuteReader()
        'Cb_Kolej.Items.Clear()
        'Cb_Kolej.Items.Add("(SEMUA)")
        'Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = ""
        'While Rdr.Read
        '    Cb_Kolej.Items.Add(Rdr(0))
        '    Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        'End While
        'Rdr.Close()
        'Cn.Close()

        ''JAWATAN
        'Cb_Kursus.Items.Clear()
        'Cb_Kursus.Items.Add("(SEMUA)")
        'Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = ""
        'Cb_Kursus.Items.Add("JURURAWAT BERDAFTAR")
        'Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "1"
        'Cb_Kursus.Items.Add("JURURAWAT MASYARAKAT")
        'Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "2"
        'Cb_Kursus.Items.Add("PENOLONG JURURAWAT")
        'Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "3"
        'Cb_Kursus.Items.Add("BIDAN")
        'Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "4"
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim SQL, SQL_pilih As String
        SQL = "select"
        SQL_pilih = ""
        If Chk_Pilih.Items(0).Selected Then SQL_pilih += " Nama,"
        If Chk_Pilih.Items(1).Selected Then SQL_pilih += " jp.nokp as 'NO.KP',"
        'Comment Original 16102018 - OSH
        'If Chk_Pilih.Items(2).Selected Then SQL_pilih += " jp.nopd as 'NO. DAFTAR',"
        'If Chk_Pilih.Items(9).Selected Then SQL_pilih += " CONVERT(char(12), jp.tkh_daftar, 103) 'TKH DAFTAR',"
        'Fix Midwife 16102018 - OSH
        If Chk_Pilih.Items(2).Selected And Cb_Kursus.SelectedValue < 4 Then SQL_pilih += " jp.nopd as 'NO. DAFTAR',"
        If Chk_Pilih.Items(2).Selected And Cb_Kursus.SelectedValue = 4 Then SQL_pilih += " jp.nopd_b1 as 'NO. DAFTAR',"
        If Chk_Pilih.Items(9).Selected And Cb_Kursus.SelectedValue < 4 Then SQL_pilih += " CONVERT(char(12), jp.tkh_daftar, 103) 'TKH DAFTAR',"
        If Chk_Pilih.Items(9).Selected And Cb_Kursus.SelectedValue = 4 Then SQL_pilih += " CONVERT(char(12), jp.tkh_daftar_b1, 103) 'TKH DAFTAR',"
        If Chk_Pilih.Items(8).Selected Then SQL_pilih += " case jantina when 1 then 'L' when 2 then 'P' else '' end as 'JANTINA',"
        If Chk_Pilih.Items(4).Selected Then SQL_pilih += " pw.dc_negara 'WARGANEGARA',"
        If Chk_Pilih.Items(5).Selected Then SQL_pilih += " pk.dc_kolej as 'TEMPAT LATIHAN',"
        If Chk_Pilih.Items(6).Selected Then SQL_pilih += " dc_tajaan 'TAJAAN',"
        If Chk_Pilih.Items(7).Selected Then SQL_pilih += " case jp.j_daftar when 1 then 'JB' when 2 then 'JM' when 3 then 'PJ' when 4 then 'B' end as 'JENIS',"
        If Chk_Pilih.Items(3).Selected Then SQL_pilih += " case bangsa when 1 then 'M' when 2 then 'C' when 3 then 'I' else 'L' end as 'BANGSA' ,"
        If Len(SQL_pilih) > 0 Then SQL_pilih = Mid(SQL_pilih, 1, Len(SQL_pilih) - 1) : SQL += SQL_pilih

        'Fix Midwife 16102018 - OSH
        If Cb_Kursus.SelectedValue < 4 Then
            SQL += " from jt_penuh jp left outer join pn_kolej pk on jp.id_kolej=pk.id_kolej "
        Else
            SQL += " from jt_penuh jp left outer join pn_kolej pk on jp.id_kolej_b1=pk.id_kolej "
        End If
        SQL += "left outer join pn_tajaan pt on jp.tajaan=pt.id_tajaan "
        SQL += "left outer join pn_negara pw on jp.warganegara=pw.id_negara "
        SQL += "where jp.nokp is not null "
        'If Cb_Negeri.SelectedIndex < 1 Then
        '    SQL += ""
        'Else
        '    SQL += " and pta.negeri = " & Cb_Negeri.SelectedValue
        'End If

        If Cb_Kolej.SelectedIndex < 1 Then
        Else
            SQL += " and jp.id_kolej = " & Cb_Kolej.SelectedValue
        End If

        'Comment Original 16102018 - OSH
        'If Cb_Kursus.SelectedIndex < 1 Then
        'Else
        '    SQL += " and jp.j_daftar = " & Cb_Kursus.SelectedValue
        'End If

        'Fix Midwife 16102018 - OSH 
        If Cb_Kursus.SelectedValue = 4 Then
            SQL += " and jp.j_daftar = 1"
        Else
            SQL += " and jp.j_daftar = " & Cb_Kursus.SelectedValue
        End If

        'SQL += " and jpa.apc_tahun = " & Cb_Tahun.SelectedValue

        If Tx_TkhDaftar.Text.Trim = "" Then
        Else
            If Tx_TkhDaftar2.Text.Trim = "" Then
                'Fix date time 07102019 - OSH 
                'SQL += " and jp.tkh_daftar = " & Chk_Tkh(Tx_TkhDaftar.Text) + "00:00:00" 
                'Comment Original 07102019 - OSH 
                SQL += " and jp.tkh_daftar = " & Chk_Tkh(Tx_TkhDaftar.Text)
            Else
                'SQL += " and jp.tkh_daftar between " & Chk_Tkh(Tx_TkhDaftar.Text) & +"00:00:00" + "and " & Chk_Tkh(Tx_TkhDaftar2.Text) + "23:59:59"

                'Comment Original 07102019 - OSH 
                SQL += " and jp.tkh_daftar between " & Chk_Tkh(Tx_TkhDaftar.Text) & " and " & Chk_Tkh(Tx_TkhDaftar2.Text)
            End If
        End If

        'Comment Original 16102018 - OSH 
        'If Tx_NoPd1.Text = "" Then
        'Else
        '    If Not IsNumeric(Tx_NoPd1.Text) Then Tx_NoPd1.Focus() : Exit Sub
        '    If Not IsNumeric(Tx_NoPd2.Text) Then Tx_NoPd2.Focus() : Exit Sub
        '    SQL += " and nopd between " & Tx_NoPd1.Text & " and " & Tx_NoPd2.Text & " order by nopd"
        'End If

        'Fix Midwife 16102018 - OSH 
        If Cb_Kursus.SelectedValue = 4 Then
            If Not IsNumeric(Tx_NoPd1.Text) Then Tx_NoPd1.Focus() : Exit Sub
            If Not IsNumeric(Tx_NoPd2.Text) Then Tx_NoPd2.Focus() : Exit Sub
            SQL += " and nopd_b1 between " & Tx_NoPd1.Text & " and " & Tx_NoPd2.Text & " order by nopd_b1"
        Else
            If Not IsNumeric(Tx_NoPd1.Text) Then Tx_NoPd1.Focus() : Exit Sub
            If Not IsNumeric(Tx_NoPd2.Text) Then Tx_NoPd2.Focus() : Exit Sub
            SQL += " and nopd between " & Tx_NoPd1.Text & " and " & Tx_NoPd2.Text & " order by nopd"
        End If


        'Msg(Me, SQL)
        Try
            Cari(SQL)
        Catch ex As Exception

        End Try

    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari2.Click
        GridViewExportUtil.Export("Laporan.xls", Gd)
    End Sub


    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
    End Sub


End Class