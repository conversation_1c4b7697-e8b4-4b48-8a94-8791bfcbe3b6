<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RestSharp.WindowsPhone</name>
    </assembly>
    <members>
        <member name="T:RestSharp.Authenticators.OAuth1Authenticator">
            <seealso href="http://tools.ietf.org/html/rfc5849"/>
        </member>
        <member name="T:RestSharp.OAuth2Authenticator">
            <summary>
            Base class for OAuth 2 Authenticators.
            </summary>
            <remarks>
            Since there are many ways to authenticate in OAuth2,
            this is used as a base class to differentiate between 
            other authenticators.
            
            Any other OAuth2 authenticators must derive from this
            abstract class.
            </remarks>
        </member>
        <member name="F:RestSharp.OAuth2Authenticator._accessToken">
            <summary>
            Access token to be used when authenticating.
            </summary>
        </member>
        <member name="M:RestSharp.OAuth2Authenticator.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:RestSharp.OAuth2Authenticator"/> class.
            </summary>
            <param name="accessToken">
            The access token.
            </param>
        </member>
        <member name="P:RestSharp.OAuth2Authenticator.AccessToken">
            <summary>
            Gets the access token.
            </summary>
        </member>
        <member name="T:RestSharp.OAuth2UriQueryParameterAuthenticator">
            <summary>
            The OAuth 2 authenticator using URI query parameter.
            </summary>
            <remarks>
            Based on http://tools.ietf.org/html/draft-ietf-oauth-v2-10#section-5.1.2
            </remarks>
        </member>
        <member name="M:RestSharp.OAuth2UriQueryParameterAuthenticator.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:RestSharp.OAuth2UriQueryParameterAuthenticator"/> class.
            </summary>
            <param name="accessToken">
            The access token.
            </param>
        </member>
        <member name="T:RestSharp.OAuth2AuthorizationRequestHeaderAuthenticator">
            <summary>
            The OAuth 2 authenticator using the authorization request header field.
            </summary>
            <remarks>
            Based on http://tools.ietf.org/html/draft-ietf-oauth-v2-10#section-5.1.1
            </remarks>
        </member>
        <member name="F:RestSharp.OAuth2AuthorizationRequestHeaderAuthenticator._authorizationValue">
            <summary>
            Stores the Authoriztion header value as "OAuth accessToken". used for performance.
            </summary>
        </member>
        <member name="M:RestSharp.OAuth2AuthorizationRequestHeaderAuthenticator.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:RestSharp.OAuth2AuthorizationRequestHeaderAuthenticator"/> class.
            </summary>
            <param name="accessToken">
            The access token.
            </param>
        </member>
        <member name="F:RestSharp.Authenticators.OAuth.OAuthTools._encoding">
            <summary>
            All text parameters are UTF-8 encoded (per section 5.1).
            </summary>
            <seealso cref="!:http://www.hueniverse.com/hueniverse/2008/10/beginners-gui-1.html"/> 
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.GetNonce">
            <summary>
            Generates a random 16-byte lowercase alphanumeric string. 
            </summary>
            <seealso cref="!:http://oauth.net/core/1.0#nonce"/>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.GetTimestamp">
            <summary>
            Generates a timestamp based on the current elapsed seconds since '01/01/1970 0000 GMT"
            </summary>
            <seealso cref="!:http://oauth.net/core/1.0#nonce"/>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.GetTimestamp(System.DateTime)">
            <summary>
            Generates a timestamp based on the elapsed seconds of a given time since '01/01/1970 0000 GMT"
            </summary>
            <seealso cref="!:http://oauth.net/core/1.0#nonce"/>
            <param name="dateTime">A specified point in time.</param>
            <returns></returns>
        </member>
        <member name="F:RestSharp.Authenticators.OAuth.OAuthTools.UriRfc3986CharsToEscape">
            <summary>
            The set of characters that are unreserved in RFC 2396 but are NOT unreserved in RFC 3986.
            </summary>
            <seealso cref="!:http://stackoverflow.com/questions/846487/how-to-get-uri-escapedatastring-to-comply-with-rfc-3986"/>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.UrlEncodeRelaxed(System.String)">
            <summary>
            URL encodes a string based on section 5.1 of the OAuth spec.
            Namely, percent encoding with [RFC3986], avoiding unreserved characters,
            upper-casing hexadecimal characters, and UTF-8 encoding for text value pairs.
            </summary>
            <param name="value">The value to escape.</param>
            <returns>The escaped value.</returns>
            <remarks>
            The <see cref="M:System.Uri.EscapeDataString(System.String)"/> method is <i>supposed</i> to take on
            RFC 3986 behavior if certain elements are present in a .config file.  Even if this
            actually worked (which in my experiments it <i>doesn't</i>), we can't rely on every
            host actually having this configuration element present.
            </remarks>
            <seealso cref="!:http://oauth.net/core/1.0#encoding_parameters"/>
            <seealso cref="!:http://stackoverflow.com/questions/846487/how-to-get-uri-escapedatastring-to-comply-with-rfc-3986"/>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.UrlEncodeStrict(System.String)">
            <summary>
            URL encodes a string based on section 5.1 of the OAuth spec.
            Namely, percent encoding with [RFC3986], avoiding unreserved characters,
            upper-casing hexadecimal characters, and UTF-8 encoding for text value pairs.
            </summary>
            <param name="value"></param>
            <seealso cref="!:http://oauth.net/core/1.0#encoding_parameters"/>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.NormalizeRequestParameters(RestSharp.Authenticators.OAuth.WebParameterCollection)">
            <summary>
            Sorts a collection of key-value pairs by name, and then value if equal,
            concatenating them into a single string. This string should be encoded
            prior to, or after normalization is run.
            </summary>
            <seealso cref="!:http://oauth.net/core/1.0#rfc.section.9.1.1"/>
            <param name="parameters"></param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.SortParametersExcludingSignature(RestSharp.Authenticators.OAuth.WebParameterCollection)">
            <summary>
            Sorts a <see cref="T:RestSharp.Authenticators.OAuth.WebParameterCollection"/> by name, and then value if equal.
            </summary>
            <param name="parameters">A collection of parameters to sort</param>
            <returns>A sorted parameter collection</returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.ConstructRequestUrl(System.Uri)">
            <summary>
            Creates a request URL suitable for making OAuth requests.
            Resulting URLs must exclude port 80 or port 443 when accompanied by HTTP and HTTPS, respectively.
            Resulting URLs must be lower case.
            </summary>
            <seealso cref="!:http://oauth.net/core/1.0#rfc.section.9.1.2"/>
            <param name="url">The original request URL</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.ConcatenateRequestElements(System.String,System.String,RestSharp.Authenticators.OAuth.WebParameterCollection)">
            <summary>
            Creates a request elements concatentation value to send with a request. 
            This is also known as the signature base.
            </summary>
            <seealso cref="!:http://oauth.net/core/1.0#rfc.section.9.1.3"/>
            <seealso cref="!:http://oauth.net/core/1.0#sig_base_example"/>
            <param name="method">The request's HTTP method type</param>
            <param name="url">The request URL</param>
            <param name="parameters">The request's parameters</param>
            <returns>A signature base string</returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.GetSignature(RestSharp.Authenticators.OAuth.OAuthSignatureMethod,System.String,System.String)">
            <summary>
            Creates a signature value given a signature base and the consumer secret.
            This method is used when the token secret is currently unknown.
            </summary>
            <seealso cref="!:http://oauth.net/core/1.0#rfc.section.9.2"/>
            <param name="signatureMethod">The hashing method</param>
            <param name="signatureBase">The signature base</param>
            <param name="consumerSecret">The consumer key</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.GetSignature(RestSharp.Authenticators.OAuth.OAuthSignatureMethod,RestSharp.Authenticators.OAuth.OAuthSignatureTreatment,System.String,System.String)">
            <summary>
            Creates a signature value given a signature base and the consumer secret.
            This method is used when the token secret is currently unknown.
            </summary>
            <seealso cref="!:http://oauth.net/core/1.0#rfc.section.9.2"/>
            <param name="signatureMethod">The hashing method</param>
            <param name="signatureTreatment">The treatment to use on a signature value</param>
            <param name="signatureBase">The signature base</param>
            <param name="consumerSecret">The consumer key</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.GetSignature(RestSharp.Authenticators.OAuth.OAuthSignatureMethod,System.String,System.String,System.String)">
            <summary>
            Creates a signature value given a signature base and the consumer secret and a known token secret.
            </summary>
            <seealso cref="!:http://oauth.net/core/1.0#rfc.section.9.2"/>
            <param name="signatureMethod">The hashing method</param>
            <param name="signatureBase">The signature base</param>
            <param name="consumerSecret">The consumer secret</param>
            <param name="tokenSecret">The token secret</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthTools.GetSignature(RestSharp.Authenticators.OAuth.OAuthSignatureMethod,RestSharp.Authenticators.OAuth.OAuthSignatureTreatment,System.String,System.String,System.String)">
            <summary>
            Creates a signature value given a signature base and the consumer secret and a known token secret.
            </summary>
            <seealso cref="!:http://oauth.net/core/1.0#rfc.section.9.2"/>
            <param name="signatureMethod">The hashing method</param>
            <param name="signatureTreatment">The treatment to use on a signature value</param>
            <param name="signatureBase">The signature base</param>
            <param name="consumerSecret">The consumer secret</param>
            <param name="tokenSecret">The token secret</param>
            <returns></returns>
        </member>
        <member name="T:RestSharp.Authenticators.OAuth.OAuthWorkflow">
            <summary>
            A class to encapsulate OAuth authentication flow.
            <seealso cref="!:http://oauth.net/core/1.0#anchor9"/>
            </summary>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthWorkflow.BuildRequestTokenInfo(System.String)">
            <summary>
            Generates a <see cref="T:RestSharp.Authenticators.OAuth.OAuthWebQueryInfo"/> instance to pass to an
            <see cref="T:RestSharp.IAuthenticator"/> for the purpose of requesting an
            unauthorized request token.
            </summary>
            <param name="method">The HTTP method for the intended request</param>
            <seealso cref="!:http://oauth.net/core/1.0#anchor9"/>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthWorkflow.BuildRequestTokenInfo(System.String,RestSharp.Authenticators.OAuth.WebParameterCollection)">
            <summary>
            Generates a <see cref="T:RestSharp.Authenticators.OAuth.OAuthWebQueryInfo"/> instance to pass to an
            <see cref="T:RestSharp.IAuthenticator"/> for the purpose of requesting an
            unauthorized request token.
            </summary>
            <param name="method">The HTTP method for the intended request</param>
            <param name="parameters">Any existing, non-OAuth query parameters desired in the request</param>
            <seealso cref="!:http://oauth.net/core/1.0#anchor9"/>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthWorkflow.BuildAccessTokenInfo(System.String)">
            <summary>
            Generates a <see cref="T:RestSharp.Authenticators.OAuth.OAuthWebQueryInfo"/> instance to pass to an
            <see cref="T:RestSharp.IAuthenticator"/> for the purpose of exchanging a request token
            for an access token authorized by the user at the Service Provider site.
            </summary>
            <param name="method">The HTTP method for the intended request</param>
            <seealso cref="!:http://oauth.net/core/1.0#anchor9"/>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthWorkflow.BuildAccessTokenInfo(System.String,RestSharp.Authenticators.OAuth.WebParameterCollection)">
            <summary>
            Generates a <see cref="T:RestSharp.Authenticators.OAuth.OAuthWebQueryInfo"/> instance to pass to an
            <see cref="T:RestSharp.IAuthenticator"/> for the purpose of exchanging a request token
            for an access token authorized by the user at the Service Provider site.
            </summary>
            <param name="method">The HTTP method for the intended request</param>
            <seealso cref="!:http://oauth.net/core/1.0#anchor9"/>
            <param name="parameters">Any existing, non-OAuth query parameters desired in the request</param>
        </member>
        <member name="M:RestSharp.Authenticators.OAuth.OAuthWorkflow.BuildClientAuthAccessTokenInfo(System.String,RestSharp.Authenticators.OAuth.WebParameterCollection)">
            <summary>
            Generates a <see cref="T:RestSharp.Authenticators.OAuth.OAuthWebQueryInfo"/> instance to pass to an
            <see cref="T:RestSharp.IAuthenticator"/> for the purpose of exchanging user credentials
            for an access token authorized by the user at the Service Provider site.
            </summary>
            <param name="method">The HTTP method for the intended request</param>
            <seealso cref="!:http://tools.ietf.org/html/draft-dehora-farrell-oauth-accesstoken-creds-00#section-4"/>
            <param name="parameters">Any existing, non-OAuth query parameters desired in the request</param>
        </member>
        <member name="P:RestSharp.Authenticators.OAuth.OAuthWorkflow.RequestTokenUrl">
            <seealso cref="!:http://oauth.net/core/1.0#request_urls"/>
        </member>
        <member name="P:RestSharp.Authenticators.OAuth.OAuthWorkflow.AccessTokenUrl">
            <seealso cref="!:http://oauth.net/core/1.0#request_urls"/>
        </member>
        <member name="P:RestSharp.Authenticators.OAuth.OAuthWorkflow.AuthorizationUrl">
            <seealso cref="!:http://oauth.net/core/1.0#request_urls"/>
        </member>
        <member name="T:RestSharp.Compression.ZLib.CRC32">
            <summary>
            Calculates a 32bit Cyclic Redundancy Checksum (CRC) using the same polynomial
            used by Zip. This type is used internally by DotNetZip; it is generally not used
            directly by applications wishing to create, read, or manipulate zip archive
            files.
            </summary>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CRC32.GetCrc32(System.IO.Stream)">
            <summary>
            Returns the CRC32 for the specified stream.
            </summary>
            <param name="input">The stream over which to calculate the CRC32</param>
            <returns>the CRC32 calculation</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CRC32.GetCrc32AndCopy(System.IO.Stream,System.IO.Stream)">
            <summary>
            Returns the CRC32 for the specified stream, and writes the input into the
            output stream.
            </summary>
            <param name="input">The stream over which to calculate the CRC32</param>
            <param name="output">The stream into which to deflate the input</param>
            <returns>the CRC32 calculation</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CRC32.ComputeCrc32(System.Int32,System.Byte)">
            <summary>
            Get the CRC32 for the given (word,byte) combo.  This is a computation
            defined by PKzip.
            </summary>
            <param name="W">The word to start with.</param>
            <param name="B">The byte to combine it with.</param>
            <returns>The CRC-ized result.</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CRC32.SlurpBlock(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Update the value for the running CRC32 using the given block of bytes.
            This is useful when using the CRC32() class in a Stream.
            </summary>
            <param name="block">block of bytes to slurp</param>
            <param name="offset">starting point in the block</param>
            <param name="count">how many bytes within the block to slurp</param>
        </member>
        <member name="P:RestSharp.Compression.ZLib.CRC32.TotalBytesRead">
            <summary>
            indicates the total number of bytes read on the CRC stream.
            This is used when writing the ZipDirEntry when compressing files.
            </summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.CRC32.Crc32Result">
            <summary>
            Indicates the current CRC for all blocks slurped in.
            </summary>
        </member>
        <member name="T:RestSharp.Compression.ZLib.CrcCalculatorStream">
             <summary>
             A Stream that calculates a CRC32 (a checksum) on all bytes read, 
             or on all bytes written.
             </summary>
            
             <remarks>
             <para>
             This class can be used to verify the CRC of a ZipEntry when
             reading from a stream, or to calculate a CRC when writing to a
             stream.  The stream should be used to either read, or write, but
             not both.  If you intermix reads and writes, the results are not
             defined.
             </para>
             
             <para>
             This class is intended primarily for use internally by the
             DotNetZip library.
             </para>
             </remarks>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CrcCalculatorStream.#ctor(System.IO.Stream)">
            <summary>
            The default constructor.
            </summary>
            <remarks>
            Instances returned from this constructor will leave the underlying stream
            open upon Close().
            </remarks>
            <param name="stream">The underlying stream</param>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CrcCalculatorStream.#ctor(System.IO.Stream,System.Boolean)">
            <summary>
            The constructor allows the caller to specify how to handle the underlying
            stream at close.
            </summary>
            <param name="stream">The underlying stream</param>
            <param name="leaveOpen">true to leave the underlying stream 
            open upon close of the CrcCalculatorStream.; false otherwise.</param>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CrcCalculatorStream.#ctor(System.IO.Stream,System.Int64)">
            <summary>
            A constructor allowing the specification of the length of the stream to read.
            </summary>
            <remarks>
            Instances returned from this constructor will leave the underlying stream open
            upon Close().
            </remarks>
            <param name="stream">The underlying stream</param>
            <param name="length">The length of the stream to slurp</param>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CrcCalculatorStream.#ctor(System.IO.Stream,System.Int64,System.Boolean)">
            <summary>
            A constructor allowing the specification of the length of the stream to
            read, as well as whether to keep the underlying stream open upon Close().
            </summary>
            <param name="stream">The underlying stream</param>
            <param name="length">The length of the stream to slurp</param>
            <param name="leaveOpen">true to leave the underlying stream 
            open upon close of the CrcCalculatorStream.; false otherwise.</param>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CrcCalculatorStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Read from the stream
            </summary>
            <param name="buffer">the buffer to read</param>
            <param name="offset">the offset at which to start</param>
            <param name="count">the number of bytes to read</param>
            <returns>the number of bytes actually read</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CrcCalculatorStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Write to the stream. 
            </summary>
            <param name="buffer">the buffer from which to write</param>
            <param name="offset">the offset at which to start writing</param>
            <param name="count">the number of bytes to write</param>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CrcCalculatorStream.Flush">
            <summary>
            Flush the stream.
            </summary>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CrcCalculatorStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Not implemented.
            </summary>
            <param name="offset">N/A</param>
            <param name="origin">N/A</param>
            <returns>N/A</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CrcCalculatorStream.SetLength(System.Int64)">
            <summary>
            Not implemented.
            </summary>
            <param name="value">N/A</param>
        </member>
        <member name="M:RestSharp.Compression.ZLib.CrcCalculatorStream.Close">
            <summary>
            Closes the stream.
            </summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.CrcCalculatorStream.TotalBytesSlurped">
             <summary>
             Gets the total number of bytes run through the CRC32 calculator.
             </summary>
            
             <remarks>
             This is either the total number of bytes read, or the total number of bytes
             written, depending on the direction of this stream.
             </remarks>
        </member>
        <member name="P:RestSharp.Compression.ZLib.CrcCalculatorStream.Crc">
            <summary>
            Provides the current CRC for all blocks slurped in.
            </summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.CrcCalculatorStream.LeaveOpen">
            <summary>
            Indicates whether the underlying stream will be left open when the
            CrcCalculatorStream is Closed.
            </summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.CrcCalculatorStream.CanRead">
            <summary>
            Indicates whether the stream supports reading. 
            </summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.CrcCalculatorStream.CanSeek">
            <summary>
            Indicates whether the stream supports seeking. 
            </summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.CrcCalculatorStream.CanWrite">
            <summary>
            Indicates whether the stream supports writing. 
            </summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.CrcCalculatorStream.Length">
            <summary>
            Not implemented.
            </summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.CrcCalculatorStream.Position">
            <summary>
            Not implemented.
            </summary>
        </member>
        <member name="T:RestSharp.Compression.ZLib.FlushType">
            <summary>
            Describes how to flush the current deflate operation. 
            </summary>
            <remarks>
            The different FlushType values are useful when using a Deflate in a streaming application.
            </remarks>
        </member>
        <member name="F:RestSharp.Compression.ZLib.FlushType.None">
            <summary>No flush at all.</summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.FlushType.Partial">
            <summary>Closes the current block, but doesn't flush it to
            the output. Used internally only in hypothetical
            scenarios.  This was supposed to be removed by Zlib, but it is
            still in use in some edge cases. 
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.FlushType.Sync">
            <summary>
            Use this during compression to specify that all pending output should be
            flushed to the output buffer and the output should be aligned on a byte
            boundary.  You might use this in a streaming communication scenario, so that
            the decompressor can get all input data available so far.  When using this
            with a ZlibCodec, <c>AvailableBytesIn</c> will be zero after the call if
            enough output space has been provided before the call.  Flushing will
            degrade compression and so it should be used only when necessary.
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.FlushType.Full">
            <summary>
            Use this during compression to specify that all output should be flushed, as
            with <c>FlushType.Sync</c>, but also, the compression state should be reset
            so that decompression can restart from this point if previous compressed
            data has been damaged or if random access is desired.  Using
            <c>FlushType.Full</c> too often can significantly degrade the compression.
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.FlushType.Finish">
            <summary>Signals the end of the compression/decompression stream.</summary>
        </member>
        <member name="T:RestSharp.Compression.ZLib.GZipStream">
             <summary>
             A class for compressing and decompressing GZIP streams.
             </summary>
             <remarks>
            
             <para>
             The GZipStream is a <see href="http://en.wikipedia.org/wiki/Decorator_pattern">Decorator</see> on a <see cref="T:System.IO.Stream"/>.  It adds GZIP compression or decompression to any stream.
             </para>
            
             <para> Like the <c>Compression.GZipStream</c> in the .NET Base
             Class Library, the Ionic.Zlib.GZipStream can compress while writing, or decompress
             while reading, but not vice versa.  The compression method used is GZIP, which is
             documented in <see href="http://www.ietf.org/rfc/rfc1952.txt">IETF RFC 1952</see>,
             "GZIP file format specification version 4.3".</para>
            
             <para> A GZipStream can be used to decompress data (through Read()) or to compress
             data (through Write()), but not both.  </para>
            
             <para> If you wish to use the GZipStream to compress data, you must wrap it around a
             write-able stream. As you call Write() on the GZipStream, the data will be
             compressed into the GZIP format.  If you want to decompress data, you must wrap the
             GZipStream around a readable stream that contains an IETF RFC 1952-compliant stream.
             The data will be decompressed as you call Read() on the GZipStream.  </para>
            
             <para> Though the GZIP format allows data from multiple files to be concatenated
             together, this stream handles only a single segment of GZIP format, typically
             representing a single file.  </para>
            
             <para>
             This class is similar to <see cref="T:RestSharp.Compression.ZLib.ZlibStream"/> and <see cref="!:DeflateStream"/>.
             <c>ZlibStream</c> handles RFC1950-compliant streams.  <see cref="!:DeflateStream"/>
             handles RFC1951-compliant streams. This class handles RFC1952-compliant streams.
             </para>
            
             </remarks>
            
             <seealso cref="!:DeflateStream"/>
             <seealso cref="T:RestSharp.Compression.ZLib.ZlibStream"/>
        </member>
        <member name="F:RestSharp.Compression.ZLib.GZipStream.LastModified">
             <summary>
             The last modified time for the GZIP stream.
             </summary>
            
             <remarks> GZIP allows the storage of a last modified time with each GZIP entry.
             When compressing data, you can set this before the first call to Write().  When
             decompressing, you can retrieve this value any time after the first call to
             Read().  </remarks>
        </member>
        <member name="M:RestSharp.Compression.ZLib.GZipStream.#ctor(System.IO.Stream)">
            <summary>
            Create a GZipStream using the specified CompressionMode and the specified CompressionLevel,
            and explicitly specify whether the stream should be left open after Deflation or Inflation.
            </summary>
            <remarks>
            <para>
            This constructor allows the application to request that the captive stream remain open after
            the deflation or inflation occurs.  By default, after Close() is called on the stream, the 
            captive stream is also closed. In some cases this is not desired, for example if the stream 
            is a memory stream that will be re-read after compressed data has been written to it.  Specify true for the 
            leaveOpen parameter to leave the stream open. 
            </para>
            <para>
            As noted in the class documentation, 
            the CompressionMode (Compress or Decompress) also establishes the "direction" of the stream.  
            A GZipStream with CompressionMode.Compress works only through Write().  A GZipStream with 
            CompressionMode.Decompress works only through Read().
            </para>
            </remarks>
            <example>
            This example shows how to use a DeflateStream to compress data.
            <code>
            using (System.IO.Stream input = System.IO.File.OpenRead(fileToCompress))
            {
                using (var raw = System.IO.File.Create(outputFile))
                {
                    using (Stream compressor = new GZipStream(raw, CompressionMode.Compress, CompressionLevel.BestCompression, true))
                    {
                        byte[] buffer = new byte[WORKING_BUFFER_SIZE];
                        int n;
                        while ((n= input.Read(buffer, 0, buffer.Length)) != 0)
                        {
                            compressor.Write(buffer, 0, n);
                        }
                    }
                }
            }
            </code>
            <code lang="VB">
            Dim outputFile As String = (fileToCompress &amp; ".compressed")
            Using input As Stream = File.OpenRead(fileToCompress)
                Using raw As FileStream = File.Create(outputFile)
                Using compressor As Stream = New GZipStream(raw, CompressionMode.Compress, CompressionLevel.BestCompression, True)
                    Dim buffer As Byte() = New Byte(4096) {}
                    Dim n As Integer = -1
                    Do While (n &lt;&gt; 0)
                        If (n &gt; 0) Then
                            compressor.Write(buffer, 0, n)
                        End If
                        n = input.Read(buffer, 0, buffer.Length)
                    Loop
                End Using
                End Using
            End Using
            </code>
            </example>
            <param name="stream">The stream which will be read or written.</param>
            <param name="mode">Indicates whether the GZipStream will compress or decompress.</param>
            <param name="leaveOpen">true if the application would like the stream to remain open after inflation/deflation.</param>
            <param name="level">A tuning knob to trade speed for effectiveness.</param>
        </member>
        <member name="M:RestSharp.Compression.ZLib.GZipStream.Dispose(System.Boolean)">
            <summary>
            Dispose the stream.  
            </summary>
            <remarks>
            This may or may not result in a Close() call on the captive stream. 
            See the ctor's with leaveOpen parameters for more information.
            </remarks>
        </member>
        <member name="M:RestSharp.Compression.ZLib.GZipStream.Flush">
            <summary>
            Flush the stream.
            </summary>
        </member>
        <member name="M:RestSharp.Compression.ZLib.GZipStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Read and decompress data from the source stream.
            </summary>
            <remarks>
            With a GZipStream, decompression is done through reading.
            </remarks>
            <example>
            <code>
            byte[] working = new byte[WORKING_BUFFER_SIZE];
            using (System.IO.Stream input = System.IO.File.OpenRead(_CompressedFile))
            {
                using (Stream decompressor= new Ionic.Zlib.GZipStream(input, CompressionMode.Decompress, true))
                {
                    using (var output = System.IO.File.Create(_DecompressedFile))
                    {
                        int n;
                        while ((n= decompressor.Read(working, 0, working.Length)) !=0)
                        {
                            output.Write(working, 0, n);
                        }
                    }
                }
            }
            </code>
            </example>
            <param name="buffer">The buffer into which the decompressed data should be placed.</param>
            <param name="offset">the offset within that data array to put the first byte read.</param>
            <param name="count">the number of bytes to read.</param>
            <returns>the number of bytes actually read</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.GZipStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Calling this method always throws a <see cref="T:System.NotImplementedException"/>.
            </summary>
            <param name="offset">irrelevant; it will always throw!</param>
            <param name="origin">irrelevant; it will always throw!</param>
            <returns>irrelevant!</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.GZipStream.SetLength(System.Int64)">
            <summary>
            Calling this method always throws a NotImplementedException.
            </summary>
            <param name="value">irrelevant; this method will always throw!</param>
        </member>
        <member name="P:RestSharp.Compression.ZLib.GZipStream.Comment">
             <summary>
             The Comment on the GZIP stream.
             </summary>
             <remarks>
             <para>
             The GZIP format allows for each file to optionally have an associated comment stored with the
             file.  The comment is encoded with the ISO-8859-1 code page.  To include a comment in
             a GZIP stream you create, set this property before calling Write() for the first time
             on the GZipStream.  
             </para>
            
             <para>
             When using GZipStream to decompress, you can retrieve this property after the first
             call to Read().  If no comment has been set in the GZIP bytestream, the Comment
             property will return null (Nothing in VB).
             </para>
             </remarks>
        </member>
        <member name="P:RestSharp.Compression.ZLib.GZipStream.FileName">
            <summary>
            The FileName for the GZIP stream.
            </summary>
            <remarks>
            <para>
            The GZIP format optionally allows each file to have an associated filename.  When
            compressing data (through Write()), set this FileName before calling Write() the first
            time on the GZipStream.  The actual filename is encoded into the GZIP bytestream with
            the ISO-8859-1 code page, according to RFC 1952. It is the application's responsibility to 
            insure that the FileName can be encoded and decoded correctly with this code page. 
            </para>
            <para>
            When decompressing (through Read()), you can retrieve this value any time after the
            first Read().  In the case where there was no filename encoded into the GZIP
            bytestream, the property will return null (Nothing in VB).
            </para>
            </remarks>
        </member>
        <member name="P:RestSharp.Compression.ZLib.GZipStream.Crc32">
            <summary>
            The CRC on the GZIP stream. 
            </summary>
            <remarks>
            This is used for internal error checking. You probably don't need to look at this property.
            </remarks>
        </member>
        <member name="P:RestSharp.Compression.ZLib.GZipStream.FlushMode">
            <summary>
            This property sets the flush behavior on the stream.  
            </summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.GZipStream.BufferSize">
             <summary>
             The size of the working buffer for the compression codec. 
             </summary>
            
             <remarks>
             <para>
             The working buffer is used for all stream operations.  The default size is 1024 bytes.
             The minimum size is 128 bytes. You may get better performance with a larger buffer.
             Then again, you might not.  You would have to test it.
             </para>
            
             <para>
             Set this before the first call to Read()  or Write() on the stream. If you try to set it 
             afterwards, it will throw.
             </para>
             </remarks>
        </member>
        <member name="P:RestSharp.Compression.ZLib.GZipStream.TotalIn">
            <summary> Returns the total number of bytes input so far.</summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.GZipStream.TotalOut">
            <summary> Returns the total number of bytes output so far.</summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.GZipStream.CanRead">
            <summary>
            Indicates whether the stream can be read.
            </summary>
            <remarks>
            The return value depends on whether the captive stream supports reading.
            </remarks>
        </member>
        <member name="P:RestSharp.Compression.ZLib.GZipStream.CanSeek">
            <summary>
            Indicates whether the stream supports Seek operations.
            </summary>
            <remarks>
            Always returns false.
            </remarks>
        </member>
        <member name="P:RestSharp.Compression.ZLib.GZipStream.CanWrite">
            <summary>
            Indicates whether the stream can be written.
            </summary>
            <remarks>
            The return value depends on whether the captive stream supports writing.
            </remarks>
        </member>
        <member name="P:RestSharp.Compression.ZLib.GZipStream.Length">
            <summary>
            Reading this property always throws a NotImplementedException.
            </summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.GZipStream.Position">
            <summary>
            The position of the stream pointer. 
            </summary>
            <remarks>
            Writing this property always throws a NotImplementedException. Reading will
            return the total bytes written out, if used in writing, or the total bytes 
            read in, if used in reading.   The count may refer to compressed bytes or 
            uncompressed bytes, depending on how you've used the stream.
            </remarks>
        </member>
        <member name="T:RestSharp.Compression.ZLib.ZlibException">
            <summary>
            A general purpose exception class for exceptions in the Zlib library.
            </summary>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibException.#ctor">
            <summary>
            The ZlibException class captures exception information generated
            by the Zlib library. 
            </summary>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibException.#ctor(System.String)">
            <summary>
            This ctor collects a message attached to the exception.
            </summary>
            <param name="s"></param>
        </member>
        <member name="M:RestSharp.Compression.ZLib.SharedUtils.URShift(System.Int32,System.Int32)">
            <summary>
            Performs an unsigned bitwise right shift with the specified number
            </summary>
            <param name="number">Number to operate on</param>
            <param name="bits">Ammount of bits to shift</param>
            <returns>The resulting number from the shift operation</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.SharedUtils.URShift(System.Int64,System.Int32)">
            <summary>
            Performs an unsigned bitwise right shift with the specified number
            </summary>
            <param name="number">Number to operate on</param>
            <param name="bits">Ammount of bits to shift</param>
            <returns>The resulting number from the shift operation</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.SharedUtils.ReadInput(System.IO.TextReader,System.Byte[],System.Int32,System.Int32)">
            <summary>Reads a number of characters from the current source TextReader and writes the data to the target array at the specified index.</summary>
            <param name="sourceTextReader">The source TextReader to read from</param>
            <param name="target">Contains the array of characteres read from the source TextReader.</param>
            <param name="start">The starting index of the target array.</param>
            <param name="count">The maximum number of characters to read from the source TextReader.</param>
            <returns>The number of characters read. The number will be less than or equal to count depending on the data available in the source TextReader. Returns -1 if the end of the stream is reached.</returns>
        </member>
        <member name="T:RestSharp.Compression.ZLib.Adler">
            <summary>
            Computes an Adler-32 checksum. 
            </summary>
            <remarks>
            The Adler checksum is similar to a CRC checksum, but faster to compute, though less
            reliable.  It is used in producing RFC1950 compressed streams.  The Adler checksum
            is a required part of the "ZLIB" standard.  Applications will almost never need to
            use this class directly.
            </remarks>
        </member>
        <member name="T:RestSharp.Compression.ZLib.ZlibCodec">
             <summary>
             Encoder and Decoder for ZLIB and DEFLATE (IETF RFC1950 and RFC1951).
             </summary>
            
             <remarks>
             This class compresses and decompresses data according to the Deflate algorithm
             and optionally, the ZLIB format, as documented in <see
             href="http://www.ietf.org/rfc/rfc1950.txt">RFC 1950 - ZLIB</see> and <see
             href="http://www.ietf.org/rfc/rfc1951.txt">RFC 1951 - DEFLATE</see>.
             </remarks>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibCodec.InputBuffer">
            <summary>
            The buffer from which data is taken.
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibCodec.NextIn">
            <summary>
            An index into the InputBuffer array, indicating where to start reading. 
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibCodec.AvailableBytesIn">
            <summary>
            The number of bytes available in the InputBuffer, starting at NextIn. 
            </summary>
            <remarks>
            Generally you should set this to InputBuffer.Length before the first Inflate() or Deflate() call. 
            The class will update this number as calls to Inflate/Deflate are made.
            </remarks>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibCodec.TotalBytesIn">
            <summary>
            Total number of bytes read so far, through all calls to Inflate()/Deflate().
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibCodec.OutputBuffer">
            <summary>
            Buffer to store output data.
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibCodec.NextOut">
            <summary>
            An index into the OutputBuffer array, indicating where to start writing. 
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibCodec.AvailableBytesOut">
            <summary>
            The number of bytes available in the OutputBuffer, starting at NextOut. 
            </summary>
            <remarks>
            Generally you should set this to OutputBuffer.Length before the first Inflate() or Deflate() call. 
            The class will update this number as calls to Inflate/Deflate are made.
            </remarks>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibCodec.TotalBytesOut">
            <summary>
            Total number of bytes written to the output so far, through all calls to Inflate()/Deflate().
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibCodec.Message">
            <summary>
            used for diagnostics, when something goes wrong!
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibCodec.WindowBits">
            <summary>
            The number of Window Bits to use.  
            </summary>
            <remarks>
            This gauges the size of the sliding window, and hence the 
            compression effectiveness as well as memory consumption. It's best to just leave this 
            setting alone if you don't know what it is.  The maximum value is 15 bits, which implies
            a 32k window.  
            </remarks>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibCodec.#ctor">
            <summary>
            Create a ZlibCodec that decompresses.
            </summary>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibCodec.InitializeInflate">
            <summary>
            Initialize the inflation state. 
            </summary>
            <remarks>
            It is not necessary to call this before using the ZlibCodec to inflate data; 
            It is implicitly called when you call the constructor.
            </remarks>
            <returns>Z_OK if everything goes well.</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibCodec.InitializeInflate(System.Boolean)">
             <summary>
             Initialize the inflation state with an explicit flag to
             govern the handling of RFC1950 header bytes.
             </summary>
            
             <remarks>
             By default, the ZLIB header defined in <see
             href="http://www.ietf.org/rfc/rfc1950.txt">RFC 1950</see> is expected.  If
             you want to read a zlib stream you should specify true for
             expectRfc1950Header.  If you have a deflate stream, you will want to specify
             false. It is only necessary to invoke this initializer explicitly if you
             want to specify false.
             </remarks>
            
             <param name="expectRfc1950Header">whether to expect an RFC1950 header byte
             pair when reading the stream of data to be inflated.</param>
            
             <returns>Z_OK if everything goes well.</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibCodec.InitializeInflate(System.Int32)">
            <summary>
            Initialize the ZlibCodec for inflation, with the specified number of window bits. 
            </summary>
            <param name="windowBits">The number of window bits to use. If you need to ask what that is, 
            then you shouldn't be calling this initializer.</param>
            <returns>Z_OK if all goes well.</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibCodec.InitializeInflate(System.Int32,System.Boolean)">
             <summary>
             Initialize the inflation state with an explicit flag to govern the handling of
             RFC1950 header bytes. 
             </summary>
            
             <remarks>
             If you want to read a zlib stream you should specify true for
             expectRfc1950Header. In this case, the library will expect to find a ZLIB
             header, as defined in <see href="http://www.ietf.org/rfc/rfc1950.txt">RFC
             1950</see>, in the compressed stream.  If you will be reading a DEFLATE or
             GZIP stream, which does not have such a header, you will want to specify
             false.
             </remarks>
            
             <param name="expectRfc1950Header">whether to expect an RFC1950 header byte pair when reading 
             the stream of data to be inflated.</param>
             <param name="windowBits">The number of window bits to use. If you need to ask what that is, 
             then you shouldn't be calling this initializer.</param>
             <returns>Z_OK if everything goes well.</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibCodec.Inflate(RestSharp.Compression.ZLib.FlushType)">
             <summary>
             Inflate the data in the InputBuffer, placing the result in the OutputBuffer.
             </summary>
             <remarks>
             You must have set InputBuffer and OutputBuffer, NextIn and NextOut, and AvailableBytesIn and 
             AvailableBytesOut  before calling this method.
             </remarks>
             <example>
             <code>
             private void InflateBuffer()
             {
                 int bufferSize = 1024;
                 byte[] buffer = new byte[bufferSize];
                 ZlibCodec decompressor = new ZlibCodec();
             
                 Console.WriteLine("\n============================================");
                 Console.WriteLine("Size of Buffer to Inflate: {0} bytes.", CompressedBytes.Length);
                 MemoryStream ms = new MemoryStream(DecompressedBytes);
             
                 int rc = decompressor.InitializeInflate();
             
                 decompressor.InputBuffer = CompressedBytes;
                 decompressor.NextIn = 0;
                 decompressor.AvailableBytesIn = CompressedBytes.Length;
             
                 decompressor.OutputBuffer = buffer;
             
                 // pass 1: inflate 
                 do
                 {
                     decompressor.NextOut = 0;
                     decompressor.AvailableBytesOut = buffer.Length;
                     rc = decompressor.Inflate(ZlibConstants.Z_NO_FLUSH);
             
                     if (rc != ZlibConstants.Z_OK &amp;&amp; rc != ZlibConstants.Z_STREAM_END)
                         throw new Exception("inflating: " + decompressor.Message);
             
                     ms.Write(decompressor.OutputBuffer, 0, buffer.Length - decompressor.AvailableBytesOut);
                 }
                 while (decompressor.AvailableBytesIn &gt; 0 || decompressor.AvailableBytesOut == 0);
             
                 // pass 2: finish and flush
                 do
                 {
                     decompressor.NextOut = 0;
                     decompressor.AvailableBytesOut = buffer.Length;
                     rc = decompressor.Inflate(ZlibConstants.Z_FINISH);
             
                     if (rc != ZlibConstants.Z_STREAM_END &amp;&amp; rc != ZlibConstants.Z_OK)
                         throw new Exception("inflating: " + decompressor.Message);
             
                     if (buffer.Length - decompressor.AvailableBytesOut &gt; 0)
                         ms.Write(buffer, 0, buffer.Length - decompressor.AvailableBytesOut);
                 }
                 while (decompressor.AvailableBytesIn &gt; 0 || decompressor.AvailableBytesOut == 0);
             
                 decompressor.EndInflate();
             }
            
             </code>
             </example>
             <param name="flush">The flush to use when inflating.</param>
             <returns>Z_OK if everything goes well.</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibCodec.EndInflate">
            <summary>
            Ends an inflation session. 
            </summary>
            <remarks>
            Call this after successively calling Inflate().  This will cause all buffers to be flushed. 
            After calling this you cannot call Inflate() without a intervening call to one of the
            InitializeInflate() overloads.
            </remarks>
            <returns>Z_OK if everything goes well.</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibCodec.SyncInflate">
            <summary>
            I don't know what this does!
            </summary>
            <returns>Z_OK if everything goes well.</returns>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibCodec.SetDictionary(System.Byte[])">
            <summary>
            Set the dictionary to be used for either Inflation or Deflation.
            </summary>
            <param name="dictionary">The dictionary bytes to use.</param>
            <returns>Z_OK if all goes well.</returns>
        </member>
        <member name="P:RestSharp.Compression.ZLib.ZlibCodec.Adler32">
            <summary>
            The Adler32 checksum on the data transferred through the codec so far. You probably don't need to look at this.
            </summary>
        </member>
        <member name="T:RestSharp.Compression.ZLib.ZlibConstants">
            <summary>
            A bunch of constants used in the Zlib interface.
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibConstants.WindowBitsMax">
            <summary>
            The maximum number of window bits for the Deflate algorithm.
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibConstants.WindowBitsDefault">
            <summary>
            The default number of window bits for the Deflate algorithm.
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibConstants.Z_OK">
            <summary>
            indicates everything is A-OK
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibConstants.Z_STREAM_END">
            <summary>
            Indicates that the last operation reached the end of the stream.
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibConstants.Z_NEED_DICT">
            <summary>
            The operation ended in need of a dictionary. 
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibConstants.Z_STREAM_ERROR">
            <summary>
            There was an error with the stream - not enough data, not open and readable, etc.
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibConstants.Z_DATA_ERROR">
            <summary>
            There was an error with the data - not enough data, bad data, etc.
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibConstants.Z_BUF_ERROR">
            <summary>
            There was an error with the working buffer.
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibConstants.WorkingBufferSizeDefault">
            <summary>
            The size of the working buffer used in the ZlibCodec class. Defaults to 8192 bytes.
            </summary>
        </member>
        <member name="F:RestSharp.Compression.ZLib.ZlibConstants.WorkingBufferSizeMin">
            <summary>
            The minimum size of the working buffer used in the ZlibCodec class.  Currently it is 128 bytes.
            </summary>
        </member>
        <member name="T:RestSharp.Compression.ZLib.ZlibStream">
             <summary>
             Represents a Zlib stream for compression or decompression.
             </summary>
             <remarks>
            
             <para>
             The ZlibStream is a <see href="http://en.wikipedia.org/wiki/Decorator_pattern">Decorator</see> on a <see cref="T:System.IO.Stream"/>.  It adds ZLIB compression or decompression to any
             stream.
             </para>
            
             <para> Using this stream, applications can compress or decompress data via
             stream <c>Read</c> and <c>Write</c> operations.  Either compresssion or
             decompression can occur through either reading or writing. The compression
             format used is ZLIB, which is documented in <see href="http://www.ietf.org/rfc/rfc1950.txt">IETF RFC 1950</see>, "ZLIB Compressed
             Data Format Specification version 3.3". This implementation of ZLIB always uses
             DEFLATE as the compression method.  (see <see href="http://www.ietf.org/rfc/rfc1951.txt">IETF RFC 1951</see>, "DEFLATE
             Compressed Data Format Specification version 1.3.") </para>
            
             <para>
             The ZLIB format allows for varying compression methods, window sizes, and dictionaries.
             This implementation always uses the DEFLATE compression method, a preset dictionary,
             and 15 window bits by default.  
             </para>
            
             <para>
             This class is similar to <see cref="!:DeflateStream"/>, except that it adds the
             RFC1950 header and trailer bytes to a compressed stream when compressing, or expects
             the RFC1950 header and trailer bytes when decompressing.  It is also similar to the
             <see cref="T:RestSharp.Compression.ZLib.GZipStream"/>.
             </para>
             </remarks>
             <seealso cref="!:DeflateStream"/>
             <seealso cref="T:RestSharp.Compression.ZLib.GZipStream"/>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibStream.Dispose(System.Boolean)">
            <summary>
            Dispose the stream.  
            </summary>
            <remarks>
            This may or may not result in a Close() call on the captive stream. 
            See the constructors that have a leaveOpen parameter for more information.
            </remarks>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibStream.Flush">
            <summary>
            Flush the stream.
            </summary>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibStream.Read(System.Byte[],System.Int32,System.Int32)">
             <summary>
             Read data from the stream. 
             </summary>
            
             <remarks>
            
             <para>
             If you wish to use the ZlibStream to compress data while reading, you can create a
             ZlibStream with CompressionMode.Compress, providing an uncompressed data stream.  Then
             call Read() on that ZlibStream, and the data read will be compressed.  If you wish to
             use the ZlibStream to decompress data while reading, you can create a ZlibStream with
             CompressionMode.Decompress, providing a readable compressed data stream.  Then call
             Read() on that ZlibStream, and the data will be decompressed as it is read.
             </para>
            
             <para>
             A ZlibStream can be used for Read() or Write(), but not both. 
             </para>
             </remarks>
             <param name="buffer">The buffer into which the read data should be placed.</param>
             <param name="offset">the offset within that data array to put the first byte read.</param>
             <param name="count">the number of bytes to read.</param>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Calling this method always throws a NotImplementedException.
            </summary>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibStream.SetLength(System.Int64)">
            <summary>
            Calling this method always throws a NotImplementedException.
            </summary>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibStream.Write(System.Byte[],System.Int32,System.Int32)">
             <summary>
             Write data to the stream. 
             </summary>
            
             <remarks>
            
             <para>
             If you wish to use the ZlibStream to compress data while writing, you can create a
             ZlibStream with CompressionMode.Compress, and a writable output stream.  Then call
             Write() on that ZlibStream, providing uncompressed data as input.  The data sent to
             the output stream will be the compressed form of the data written.  If you wish to use
             the ZlibStream to decompress data while writing, you can create a ZlibStream with
             CompressionMode.Decompress, and a writable output stream.  Then call Write() on that
             stream, providing previously compressed data. The data sent to the output stream will
             be the decompressed form of the data written.
             </para>
            
             <para>
             A ZlibStream can be used for Read() or Write(), but not both. 
             </para>
             </remarks>
             <param name="buffer">The buffer holding data to write to the stream.</param>
             <param name="offset">the offset within that data array to find the first byte to write.</param>
             <param name="count">the number of bytes to write.</param>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibStream.UncompressString(System.Byte[])">
            <summary>
            Uncompress a byte array into a single string.
            </summary>
            <seealso cref="!:ZlibStream.CompressString(String)"/>
            <param name="compressed">
            A buffer containing ZLIB-compressed data.  
            </param>
        </member>
        <member name="M:RestSharp.Compression.ZLib.ZlibStream.UncompressBuffer(System.Byte[])">
            <summary>
            Uncompress a byte array into a byte array.
            </summary>
            <seealso cref="!:ZlibStream.CompressBuffer(byte[])"/>
            <seealso cref="M:RestSharp.Compression.ZLib.ZlibStream.UncompressString(System.Byte[])"/>
            <param name="compressed">
            A buffer containing ZLIB-compressed data.  
            </param>
        </member>
        <member name="P:RestSharp.Compression.ZLib.ZlibStream.FlushMode">
            <summary>
            This property sets the flush behavior on the stream.  
            Sorry, though, not sure exactly how to describe all the various settings.
            </summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.ZlibStream.BufferSize">
             <summary>
             The size of the working buffer for the compression codec. 
             </summary>
            
             <remarks>
             <para>
             The working buffer is used for all stream operations.  The default size is 1024 bytes.
             The minimum size is 128 bytes. You may get better performance with a larger buffer.
             Then again, you might not.  You would have to test it.
             </para>
            
             <para>
             Set this before the first call to Read()  or Write() on the stream. If you try to set it 
             afterwards, it will throw.
             </para>
             </remarks>
        </member>
        <member name="P:RestSharp.Compression.ZLib.ZlibStream.TotalIn">
            <summary> Returns the total number of bytes input so far.</summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.ZlibStream.TotalOut">
            <summary> Returns the total number of bytes output so far.</summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.ZlibStream.CanRead">
            <summary>
            Indicates whether the stream can be read.
            </summary>
            <remarks>
            The return value depends on whether the captive stream supports reading.
            </remarks>
        </member>
        <member name="P:RestSharp.Compression.ZLib.ZlibStream.CanSeek">
            <summary>
            Indicates whether the stream supports Seek operations.
            </summary>
            <remarks>
            Always returns false.
            </remarks>
        </member>
        <member name="P:RestSharp.Compression.ZLib.ZlibStream.CanWrite">
            <summary>
            Indicates whether the stream can be written.
            </summary>
            <remarks>
            The return value depends on whether the captive stream supports writing.
            </remarks>
        </member>
        <member name="P:RestSharp.Compression.ZLib.ZlibStream.Length">
            <summary>
            Reading this property always throws a NotImplementedException.
            </summary>
        </member>
        <member name="P:RestSharp.Compression.ZLib.ZlibStream.Position">
            <summary>
            The position of the stream pointer. 
            </summary>
            <remarks>
            Writing this property always throws a NotImplementedException. Reading will
            return the total bytes written out, if used in writing, or the total bytes 
            read in, if used in reading.   The count may refer to compressed bytes or 
            uncompressed bytes, depending on how you've used the stream.
            </remarks>
        </member>
        <member name="T:RestSharp.Deserializers.DeserializeAsAttribute">
            <summary>
            Allows control how class and property names and values are deserialized by XmlAttributeDeserializer
            </summary>
        </member>
        <member name="P:RestSharp.Deserializers.DeserializeAsAttribute.Name">
            <summary>
            The name to use for the serialized element
            </summary>
        </member>
        <member name="P:RestSharp.Deserializers.DeserializeAsAttribute.Attribute">
            <summary>
            Sets if the property to Deserialize is an Attribute or Element (Default: false)
            </summary>
        </member>
        <member name="T:RestSharp.Deserializers.DotNetXmlDeserializer">
            <summary>
            Wrapper for System.Xml.Serialization.XmlSerializer.
            </summary>
        </member>
        <member name="T:RestSharp.ParameterType">
            <summary>
             Types of parameters that can be added to requests
            </summary>
        </member>
        <member name="T:RestSharp.DataFormat">
            <summary>
            Data formats
            </summary>
        </member>
        <member name="T:RestSharp.Method">
            <summary>
            HTTP method to use when making requests
            </summary>
        </member>
        <member name="T:RestSharp.DateFormat">
            <summary>
            Format strings for commonly-used date formats
            </summary>
        </member>
        <member name="F:RestSharp.DateFormat.Iso8601">
            <summary>
            .NET format string for ISO 8601 date format
            </summary>
        </member>
        <member name="F:RestSharp.DateFormat.RoundTrip">
            <summary>
            .NET format string for roundtrip date format
            </summary>
        </member>
        <member name="T:RestSharp.ResponseStatus">
            <summary>
            Status for responses (surprised?)
            </summary>
        </member>
        <member name="T:RestSharp.Extensions.MiscExtensions">
            <summary>
            Extension method overload!
            </summary>
        </member>
        <member name="M:RestSharp.Extensions.MiscExtensions.ReadAsBytes(System.IO.Stream)">
            <summary>
            Read a stream into a byte array
            </summary>
            <param name="input">Stream to read</param>
            <returns>byte[]</returns>
        </member>
        <member name="M:RestSharp.Extensions.MiscExtensions.CopyTo(System.IO.Stream,System.IO.Stream)">
            <summary>
            Copies bytes from one stream to another
            </summary>
            <param name="input">The input stream.</param>
            <param name="output">The output stream.</param>
        </member>
        <member name="M:RestSharp.Extensions.MiscExtensions.AsString(System.Byte[])">
            <summary>
            Converts a byte array to a string, using its byte order mark to convert it to the right encoding.
            http://www.shrinkrays.net/code-snippets/csharp/an-extension-method-for-converting-a-byte-array-to-a-string.aspx
            </summary>
            <param name="buffer">An array of bytes to convert</param>
            <returns>The byte as a string.</returns>
        </member>
        <member name="T:RestSharp.Extensions.ReflectionExtensions">
            <summary>
            Reflection extensions
            </summary>
        </member>
        <member name="M:RestSharp.Extensions.ReflectionExtensions.GetAttribute``1(System.Reflection.MemberInfo)">
            <summary>
            Retrieve an attribute from a member (property)
            </summary>
            <typeparam name="T">Type of attribute to retrieve</typeparam>
            <param name="prop">Member to retrieve attribute from</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Extensions.ReflectionExtensions.GetAttribute``1(System.Type)">
            <summary>
            Retrieve an attribute from a type
            </summary>
            <typeparam name="T">Type of attribute to retrieve</typeparam>
            <param name="type">Type to retrieve attribute from</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Extensions.ReflectionExtensions.IsSubclassOfRawGeneric(System.Type,System.Type)">
            <summary>
            Checks a type to see if it derives from a raw generic (e.g. List[[]])
            </summary>
            <param name="toCheck"></param>
            <param name="generic"></param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:RestSharp.Extensions.ReflectionExtensions.FindEnumValue(System.Type,System.String,System.Globalization.CultureInfo)" -->
        <member name="M:RestSharp.Extensions.StringExtensions.UrlEncode(System.String)">
            <summary>
            Uses Uri.EscapeDataString() based on recommendations on MSDN
            http://blogs.msdn.com/b/yangxind/archive/2006/11/09/don-t-use-net-system-uri-unescapedatastring-in-url-decoding.aspx
            </summary>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.HasValue(System.String)">
            <summary>
            Check that a string is not null or empty
            </summary>
            <param name="input">String to check</param>
            <returns>bool</returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.RemoveUnderscoresAndDashes(System.String)">
            <summary>
            Remove underscores from a string
            </summary>
            <param name="input">String to process</param>
            <returns>string</returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.ParseJsonDate(System.String,System.Globalization.CultureInfo)">
            <summary>
            Parses most common JSON date formats
            </summary>
            <param name="input">JSON value to parse</param>
            <returns>DateTime</returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.RemoveSurroundingQuotes(System.String)">
            <summary>
            Remove leading and trailing " from a string
            </summary>
            <param name="input">String to parse</param>
            <returns>String</returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.Matches(System.String,System.String)">
            <summary>
            Checks a string to see if it matches a regex
            </summary>
            <param name="input">String to check</param>
            <param name="pattern">Pattern to match</param>
            <returns>bool</returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.ToPascalCase(System.String,System.Globalization.CultureInfo)">
            <summary>
            Converts a string to pascal case
            </summary>
            <param name="lowercaseAndUnderscoredWord">String to convert</param>
            <returns>string</returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.ToPascalCase(System.String,System.Boolean,System.Globalization.CultureInfo)">
            <summary>
            Converts a string to pascal case with the option to remove underscores
            </summary>
            <param name="text">String to convert</param>
            <param name="removeUnderscores">Option to remove underscores</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.ToCamelCase(System.String,System.Globalization.CultureInfo)">
            <summary>
            Converts a string to camel case
            </summary>
            <param name="lowercaseAndUnderscoredWord">String to convert</param>
            <returns>String</returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.MakeInitialLowerCase(System.String)">
            <summary>
            Convert the first letter of a string to lower case
            </summary>
            <param name="word">String to convert</param>
            <returns>string</returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.IsUpperCase(System.String)">
            <summary>
            Checks to see if a string is all uppper case
            </summary>
            <param name="inputString">String to check</param>
            <returns>bool</returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.AddUnderscores(System.String)">
            <summary>
            Add underscores to a pascal-cased string
            </summary>
            <param name="pascalCasedWord">String to convert</param>
            <returns>string</returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.AddDashes(System.String)">
            <summary>
            Add dashes to a pascal-cased string
            </summary>
            <param name="pascalCasedWord">String to convert</param>
            <returns>string</returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.AddUnderscorePrefix(System.String)">
            <summary>
            Add an undescore prefix to a pascasl-cased string
            </summary>
            <param name="pascalCasedWord"></param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Extensions.StringExtensions.GetNameVariants(System.String,System.Globalization.CultureInfo)">
            <summary>
            Return possible variants of a name for name matching.
            </summary>
            <param name="name">String to convert</param>
            <param name="culture">The culture to use for conversion</param>
            <returns>IEnumerable&lt;string&gt;</returns>
        </member>
        <member name="T:RestSharp.Extensions.XmlExtensions">
            <summary>
            XML Extension Methods
            </summary>
        </member>
        <member name="M:RestSharp.Extensions.XmlExtensions.AsNamespaced(System.String,System.String)">
            <summary>
            Returns the name of an element with the namespace if specified
            </summary>
            <param name="name">Element name</param>
            <param name="namespace">XML Namespace</param>
            <returns></returns>
        </member>
        <member name="T:RestSharp.FileParameter">
            <summary>
            Container for files to be uploaded with requests
            </summary>
        </member>
        <member name="M:RestSharp.FileParameter.Create(System.String,System.Byte[],System.String,System.String)">
            <summary>
             Creates a file parameter from an array of bytes.
            </summary>
            <param name="name">The parameter name to use in the request.</param>
            <param name="data">The data to use as the file's contents.</param>
            <param name="filename">The filename to use in the request.</param>
            <param name="contentType">The content type to use in the request.</param>
            <returns>The <see cref="T:RestSharp.FileParameter"/></returns>
        </member>
        <member name="M:RestSharp.FileParameter.Create(System.String,System.Byte[],System.String)">
            <summary>
             Creates a file parameter from an array of bytes.
            </summary>
            <param name="name">The parameter name to use in the request.</param>
            <param name="data">The data to use as the file's contents.</param>
            <param name="filename">The filename to use in the request.</param>
            <returns>The <see cref="T:RestSharp.FileParameter"/> using the default content type.</returns>
        </member>
        <member name="P:RestSharp.FileParameter.ContentLength">
            <summary>
            The length of data to be sent
            </summary>
        </member>
        <member name="P:RestSharp.FileParameter.Writer">
            <summary>
            Provides raw data for file
            </summary>
        </member>
        <member name="P:RestSharp.FileParameter.FileName">
            <summary>
            Name of the file to use when uploading
            </summary>
        </member>
        <member name="P:RestSharp.FileParameter.ContentType">
            <summary>
            MIME content type of file
            </summary>
        </member>
        <member name="P:RestSharp.FileParameter.Name">
            <summary>
            Name of the parameter
            </summary>
        </member>
        <member name="T:RestSharp.Http">
            <summary>
            HttpWebRequest wrapper (async methods)
            </summary>
            <summary>
            HttpWebRequest wrapper
            </summary>
        </member>
        <member name="M:RestSharp.Http.AsPostAsync(System.Action{RestSharp.HttpResponse},System.String)">
            <summary>
            Execute an async POST-style request with the specified HTTP Method.  
            </summary>
            <param name="httpMethod">The HTTP method to execute.</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Http.AsGetAsync(System.Action{RestSharp.HttpResponse},System.String)">
            <summary>
            Execute an async GET-style request with the specified HTTP Method.  
            </summary>
            <param name="httpMethod">The HTTP method to execute.</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Http.Create">
            <summary>
             Creates an IHttp
            </summary>
            <returns></returns>
        </member>
        <member name="M:RestSharp.Http.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:RestSharp.Http.HasParameters">
            <summary>
            True if this HTTP request has any HTTP parameters
            </summary>
        </member>
        <member name="P:RestSharp.Http.HasCookies">
            <summary>
            True if this HTTP request has any HTTP cookies
            </summary>
        </member>
        <member name="P:RestSharp.Http.HasBody">
            <summary>
            True if a request body has been specified
            </summary>
        </member>
        <member name="P:RestSharp.Http.HasFiles">
            <summary>
            True if files have been set to be uploaded
            </summary>
        </member>
        <member name="P:RestSharp.Http.UserAgent">
            <summary>
            UserAgent to be sent with request
            </summary>
        </member>
        <member name="P:RestSharp.Http.Timeout">
            <summary>
            Timeout in milliseconds to be used for the request
            </summary>
        </member>
        <member name="P:RestSharp.Http.Credentials">
            <summary>
            System.Net.ICredentials to be sent with request
            </summary>
        </member>
        <member name="P:RestSharp.Http.CookieContainer">
            <summary>
            The System.Net.CookieContainer to be used for the request
            </summary>
        </member>
        <member name="P:RestSharp.Http.Files">
            <summary>
            Collection of files to be sent with request
            </summary>
        </member>
        <member name="P:RestSharp.Http.FollowRedirects">
            <summary>
            Whether or not HTTP 3xx response redirects should be automatically followed
            </summary>
        </member>
        <member name="P:RestSharp.Http.Headers">
            <summary>
            HTTP headers to be sent with request
            </summary>
        </member>
        <member name="P:RestSharp.Http.Parameters">
            <summary>
            HTTP parameters (QueryString or Form values) to be sent with request
            </summary>
        </member>
        <member name="P:RestSharp.Http.Cookies">
            <summary>
            HTTP cookies to be sent with request
            </summary>
        </member>
        <member name="P:RestSharp.Http.RequestBody">
            <summary>
            Request body to be sent with request
            </summary>
        </member>
        <member name="P:RestSharp.Http.RequestContentType">
            <summary>
            Content type of the request body.
            </summary>
        </member>
        <member name="P:RestSharp.Http.Url">
            <summary>
            URL to call for this request
            </summary>
        </member>
        <member name="T:RestSharp.HttpCookie">
            <summary>
            Representation of an HTTP cookie
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.Comment">
            <summary>
            Comment of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.CommentUri">
            <summary>
            Comment of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.Discard">
            <summary>
            Indicates whether the cookie should be discarded at the end of the session
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.Domain">
            <summary>
            Domain of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.Expired">
            <summary>
            Indicates whether the cookie is expired
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.Expires">
            <summary>
            Date and time that the cookie expires
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.HttpOnly">
            <summary>
            Indicates that this cookie should only be accessed by the server
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.Name">
            <summary>
            Name of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.Path">
            <summary>
            Path of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.Port">
            <summary>
            Port of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.Secure">
            <summary>
            Indicates that the cookie should only be sent over secure channels
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.TimeStamp">
            <summary>
            Date and time the cookie was created
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.Value">
            <summary>
            Value of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.HttpCookie.Version">
            <summary>
            Version of the cookie
            </summary>
        </member>
        <member name="T:RestSharp.HttpFile">
            <summary>
            Container for HTTP file
            </summary>
        </member>
        <member name="P:RestSharp.HttpFile.ContentLength">
            <summary>
            The length of data to be sent
            </summary>
        </member>
        <member name="P:RestSharp.HttpFile.Writer">
            <summary>
            Provides raw data for file
            </summary>
        </member>
        <member name="P:RestSharp.HttpFile.FileName">
            <summary>
            Name of the file to use when uploading
            </summary>
        </member>
        <member name="P:RestSharp.HttpFile.ContentType">
            <summary>
            MIME content type of file
            </summary>
        </member>
        <member name="P:RestSharp.HttpFile.Name">
            <summary>
            Name of the parameter
            </summary>
        </member>
        <member name="T:RestSharp.HttpHeader">
            <summary>
            Representation of an HTTP header
            </summary>
        </member>
        <member name="P:RestSharp.HttpHeader.Name">
            <summary>
            Name of the header
            </summary>
        </member>
        <member name="P:RestSharp.HttpHeader.Value">
            <summary>
            Value of the header
            </summary>
        </member>
        <member name="T:RestSharp.HttpParameter">
            <summary>
            Representation of an HTTP parameter (QueryString or Form value)
            </summary>
        </member>
        <member name="P:RestSharp.HttpParameter.Name">
            <summary>
            Name of the parameter
            </summary>
        </member>
        <member name="P:RestSharp.HttpParameter.Value">
            <summary>
            Value of the parameter
            </summary>
        </member>
        <member name="T:RestSharp.HttpResponse">
            <summary>
            HTTP response data
            </summary>
        </member>
        <member name="T:RestSharp.IHttpResponse">
            <summary>
            HTTP response data
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.ContentType">
            <summary>
            MIME content type of response
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.ContentLength">
            <summary>
            Length in bytes of the response content
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.ContentEncoding">
            <summary>
            Encoding of the response content
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.Content">
            <summary>
            String representation of response content
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.StatusCode">
            <summary>
            HTTP response status code
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.StatusDescription">
            <summary>
            Description of HTTP status returned
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.RawBytes">
            <summary>
            Response content
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.ResponseUri">
            <summary>
            The URL that actually responded to the content (different from request if redirected)
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.Server">
            <summary>
            HttpWebResponse.Server
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.Headers">
            <summary>
            Headers returned by server with the response
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.Cookies">
            <summary>
            Cookies returned by server with the response
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.ResponseStatus">
            <summary>
            Status of the request. Will return Error for transport errors.
            HTTP errors will still return ResponseStatus.Completed, check StatusCode instead
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.ErrorMessage">
            <summary>
            Transport or other non-HTTP error generated while attempting request
            </summary>
        </member>
        <member name="P:RestSharp.IHttpResponse.ErrorException">
            <summary>
            Exception thrown when error is encountered.
            </summary>
        </member>
        <member name="M:RestSharp.HttpResponse.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.ContentType">
            <summary>
            MIME content type of response
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.ContentLength">
            <summary>
            Length in bytes of the response content
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.ContentEncoding">
            <summary>
            Encoding of the response content
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.Content">
            <summary>
            Lazy-loaded string representation of response content
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.StatusCode">
            <summary>
            HTTP response status code
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.StatusDescription">
            <summary>
            Description of HTTP status returned
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.RawBytes">
            <summary>
            Response content
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.ResponseUri">
            <summary>
            The URL that actually responded to the content (different from request if redirected)
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.Server">
            <summary>
            HttpWebResponse.Server
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.Headers">
            <summary>
            Headers returned by server with the response
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.Cookies">
            <summary>
            Cookies returned by server with the response
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.ResponseStatus">
            <summary>
            Status of the request. Will return Error for transport errors.
            HTTP errors will still return ResponseStatus.Completed, check StatusCode instead
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.ErrorMessage">
            <summary>
            Transport or other non-HTTP error generated while attempting request
            </summary>
        </member>
        <member name="P:RestSharp.HttpResponse.ErrorException">
            <summary>
            Exception thrown when error is encountered.
            </summary>
        </member>
        <member name="T:RestSharp.IRestClient">
            <summary>
            
            </summary>
        </member>
        <member name="M:RestSharp.IRestClient.ExecuteAsync(RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse,RestSharp.RestRequestAsyncHandle})">
            <summary>
            
            </summary>
            <param name="request"></param>
        </member>
        <member name="M:RestSharp.IRestClient.ExecuteAsync``1(RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse{``0},RestSharp.RestRequestAsyncHandle})">
            <summary>
            
            </summary>
            <param name="request"></param>
        </member>
        <member name="M:RestSharp.IRestClient.ExecuteAsyncGet(RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse,RestSharp.RestRequestAsyncHandle},System.String)">
            <summary>
            Executes a GET-style request and callback asynchronously, authenticating if needed
            </summary>
            <param name="request">Request to be executed</param>
            <param name="callback">Callback function to be executed upon completion providing access to the async handle.</param>
            <param name="httpMethod">The HTTP method to execute</param>
        </member>
        <member name="M:RestSharp.IRestClient.ExecuteAsyncPost(RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse,RestSharp.RestRequestAsyncHandle},System.String)">
            <summary>
            Executes a POST-style request and callback asynchronously, authenticating if needed
            </summary>
            <param name="request">Request to be executed</param>
            <param name="callback">Callback function to be executed upon completion providing access to the async handle.</param>
            <param name="httpMethod">The HTTP method to execute</param>
        </member>
        <member name="M:RestSharp.IRestClient.ExecuteAsyncGet``1(RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse{``0},RestSharp.RestRequestAsyncHandle},System.String)">
            <summary>
            Executes a GET-style request and callback asynchronously, authenticating if needed
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="request">Request to be executed</param>
            <param name="callback">Callback function to be executed upon completion</param>
            <param name="httpMethod">The HTTP method to execute</param>
        </member>
        <member name="M:RestSharp.IRestClient.ExecuteAsyncPost``1(RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse{``0},RestSharp.RestRequestAsyncHandle},System.String)">
            <summary>
            Executes a GET-style request and callback asynchronously, authenticating if needed
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="request">Request to be executed</param>
            <param name="callback">Callback function to be executed upon completion</param>
            <param name="httpMethod">The HTTP method to execute</param>
        </member>
        <member name="P:RestSharp.IRestClient.CookieContainer">
            <summary>
            
            </summary>
        </member>
        <member name="P:RestSharp.IRestClient.UserAgent">
            <summary>
            
            </summary>
        </member>
        <member name="P:RestSharp.IRestClient.Timeout">
            <summary>
            
            </summary>
        </member>
        <member name="P:RestSharp.IRestClient.UseSynchronizationContext">
            <summary>
            
            </summary>
        </member>
        <member name="P:RestSharp.IRestClient.Authenticator">
            <summary>
            
            </summary>
        </member>
        <member name="P:RestSharp.IRestClient.BaseUrl">
            <summary>
            
            </summary>
        </member>
        <member name="P:RestSharp.IRestClient.DefaultParameters">
            <summary>
            
            </summary>
        </member>
        <member name="M:RestSharp.IRestRequest.AddBody(System.Object,System.String)">
            <summary>
            Serializes obj to format specified by RequestFormat, but passes xmlNamespace if using the default XmlSerializer
            </summary>
            <param name="obj">The object to serialize</param>
            <param name="xmlNamespace">The XML namespace to use when serializing</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.IRestRequest.AddBody(System.Object)">
            <summary>
            Serializes obj to data format specified by RequestFormat and adds it to the request body.
            </summary>
            <param name="obj">The object to serialize</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.IRestRequest.AddObject(System.Object,System.String[])">
            <summary>
            Calls AddParameter() for all public, readable properties specified in the white list
            </summary>
            <example>
            request.AddObject(product, "ProductId", "Price", ...);
            </example>
            <param name="obj">The object with properties to add as parameters</param>
            <param name="whitelist">The names of the properties to include</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.IRestRequest.AddObject(System.Object)">
            <summary>
            Calls AddParameter() for all public, readable properties of obj
            </summary>
            <param name="obj">The object with properties to add as parameters</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.IRestRequest.AddParameter(RestSharp.Parameter)">
            <summary>
            Add the parameter to the request
            </summary>
            <param name="p">Parameter to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.IRestRequest.AddParameter(System.String,System.Object)">
            <summary>
            Adds a HTTP parameter to the request (QueryString for GET, DELETE, OPTIONS and HEAD; Encoded form for POST and PUT)
            </summary>
            <param name="name">Name of the parameter</param>
            <param name="value">Value of the parameter</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.IRestRequest.AddParameter(System.String,System.Object,RestSharp.ParameterType)">
            <summary>
            Adds a parameter to the request. There are five types of parameters:
            - GetOrPost: Either a QueryString value or encoded form value based on method
            - HttpHeader: Adds the name/value pair to the HTTP request's Headers collection
            - UrlSegment: Inserted into URL if there is a matching url token e.g. {AccountId}
            - Cookie: Adds the name/value pair to the HTTP request's Cookies collection
            - RequestBody: Used by AddBody() (not recommended to use directly)
            </summary>
            <param name="name">Name of the parameter</param>
            <param name="value">Value of the parameter</param>
            <param name="type">The type of parameter to add</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.IRestRequest.AddHeader(System.String,System.String)">
            <summary>
            Shortcut to AddParameter(name, value, HttpHeader) overload
            </summary>
            <param name="name">Name of the header to add</param>
            <param name="value">Value of the header to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.IRestRequest.AddCookie(System.String,System.String)">
            <summary>
            Shortcut to AddParameter(name, value, Cookie) overload
            </summary>
            <param name="name">Name of the cookie to add</param>
            <param name="value">Value of the cookie to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.IRestRequest.AddUrlSegment(System.String,System.String)">
            <summary>
            Shortcut to AddParameter(name, value, UrlSegment) overload
            </summary>
            <param name="name">Name of the segment to add</param>
            <param name="value">Value of the segment to add</param>
            <returns></returns>
        </member>
        <member name="P:RestSharp.IRestRequest.JsonSerializer">
            <summary>
            Serializer to use when writing JSON request bodies. Used if RequestFormat is Json.
            By default the included JsonSerializer is used (currently using JSON.NET default serialization).
            </summary>
        </member>
        <member name="P:RestSharp.IRestRequest.XmlSerializer">
            <summary>
            Serializer to use when writing XML request bodies. Used if RequestFormat is Xml.
            By default the included XmlSerializer is used.
            </summary>
        </member>
        <member name="P:RestSharp.IRestRequest.Parameters">
            <summary>
            Container of all HTTP parameters to be passed with the request. 
            See AddParameter() for explanation of the types of parameters that can be passed
            </summary>
        </member>
        <member name="P:RestSharp.IRestRequest.Files">
            <summary>
            Container of all the files to be uploaded with the request.
            </summary>
        </member>
        <member name="P:RestSharp.IRestRequest.Method">
            <summary>
            Determines what HTTP method to use for this request. Supported methods: GET, POST, PUT, DELETE, HEAD, OPTIONS
            Default is GET
            </summary>
        </member>
        <member name="P:RestSharp.IRestRequest.Resource">
            <summary>
            The Resource URL to make the request against.
            Tokens are substituted with UrlSegment parameters and match by name.
            Should not include the scheme or domain. Do not include leading slash.
            Combined with RestClient.BaseUrl to assemble final URL:
            {BaseUrl}/{Resource} (BaseUrl is scheme + domain, e.g. http://example.com)
            </summary>
            <example>
            // example for url token replacement
            request.Resource = "Products/{ProductId}";
            request.AddParameter("ProductId", 123, ParameterType.UrlSegment);
            </example>
        </member>
        <member name="P:RestSharp.IRestRequest.RequestFormat">
            <summary>
            Serializer to use when writing XML request bodies. Used if RequestFormat is Xml.
            By default XmlSerializer is used.
            </summary>
        </member>
        <member name="P:RestSharp.IRestRequest.RootElement">
            <summary>
            Used by the default deserializers to determine where to start deserializing from.
            Can be used to skip container or root elements that do not have corresponding deserialzation targets.
            </summary>
        </member>
        <member name="P:RestSharp.IRestRequest.DateFormat">
            <summary>
            Used by the default deserializers to explicitly set which date format string to use when parsing dates.
            </summary>
        </member>
        <member name="P:RestSharp.IRestRequest.XmlNamespace">
            <summary>
            Used by XmlDeserializer. If not specified, XmlDeserializer will flatten response by removing namespaces from element names.
            </summary>
        </member>
        <member name="P:RestSharp.IRestRequest.Credentials">
            <summary>
            In general you would not need to set this directly. Used by the NtlmAuthenticator. 
            </summary>
        </member>
        <member name="P:RestSharp.IRestRequest.Timeout">
            <summary>
            Timeout in milliseconds to be used for the request. This timeout value overrides a timeout set on the RestClient.
            </summary>
        </member>
        <member name="P:RestSharp.IRestRequest.Attempts">
            <summary>
            How many attempts were made to send this Request?
            </summary>
            <remarks>
            This Number is incremented each time the RestClient sends the request.
            Useful when using Asynchronous Execution with Callbacks
            </remarks>
        </member>
        <member name="T:RestSharp.IRestResponse">
            <summary>
            Container for data sent back from API
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.Request">
            <summary>
            The RestRequest that was made to get this RestResponse
            </summary>
            <remarks>
            Mainly for debugging if ResponseStatus is not OK
            </remarks> 
        </member>
        <member name="P:RestSharp.IRestResponse.ContentType">
            <summary>
            MIME content type of response
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.ContentLength">
            <summary>
            Length in bytes of the response content
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.ContentEncoding">
            <summary>
            Encoding of the response content
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.Content">
            <summary>
            String representation of response content
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.StatusCode">
            <summary>
            HTTP response status code
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.StatusDescription">
            <summary>
            Description of HTTP status returned
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.RawBytes">
            <summary>
            Response content
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.ResponseUri">
            <summary>
            The URL that actually responded to the content (different from request if redirected)
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.Server">
            <summary>
            HttpWebResponse.Server
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.Cookies">
            <summary>
            Cookies returned by server with the response
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.Headers">
            <summary>
            Headers returned by server with the response
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.ResponseStatus">
            <summary>
            Status of the request. Will return Error for transport errors.
            HTTP errors will still return ResponseStatus.Completed, check StatusCode instead
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.ErrorMessage">
            <summary>
            Transport or other non-HTTP error generated while attempting request
            </summary>
        </member>
        <member name="P:RestSharp.IRestResponse.ErrorException">
            <summary>
            The exception thrown during the request, if any
            </summary>
        </member>
        <member name="T:RestSharp.IRestResponse`1">
            <summary>
            Container for data sent back from API including deserialized data
            </summary>
            <typeparam name="T">Type of data to deserialize to</typeparam>
        </member>
        <member name="P:RestSharp.IRestResponse`1.Data">
            <summary>
            Deserialized entity data
            </summary>
        </member>
        <member name="T:RestSharp.Parameter">
            <summary>
            Parameter container for REST requests
            </summary>
        </member>
        <member name="M:RestSharp.Parameter.ToString">
            <summary>
            Return a human-readable representation of this parameter
            </summary>
            <returns>String</returns>
        </member>
        <member name="P:RestSharp.Parameter.Name">
            <summary>
            Name of the parameter
            </summary>
        </member>
        <member name="P:RestSharp.Parameter.Value">
            <summary>
            Value of the parameter
            </summary>
        </member>
        <member name="P:RestSharp.Parameter.Type">
            <summary>
            Type of the parameter
            </summary>
        </member>
        <member name="T:RestSharp.RestClient">
            <summary>
            Client to translate RestRequests into Http requests and process response result
            </summary>
        </member>
        <member name="M:RestSharp.RestClient.ExecuteAsync(RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse,RestSharp.RestRequestAsyncHandle})">
            <summary>
            Executes the request and callback asynchronously, authenticating if needed
            </summary>
            <param name="request">Request to be executed</param>
            <param name="callback">Callback function to be executed upon completion providing access to the async handle.</param>
        </member>
        <member name="M:RestSharp.RestClient.ExecuteAsyncGet(RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse,RestSharp.RestRequestAsyncHandle},System.String)">
            <summary>
            Executes a GET-style request and callback asynchronously, authenticating if needed
            </summary>
            <param name="request">Request to be executed</param>
            <param name="callback">Callback function to be executed upon completion providing access to the async handle.</param>
            <param name="httpMethod">The HTTP method to execute</param>
        </member>
        <member name="M:RestSharp.RestClient.ExecuteAsyncPost(RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse,RestSharp.RestRequestAsyncHandle},System.String)">
            <summary>
            Executes a POST-style request and callback asynchronously, authenticating if needed
            </summary>
            <param name="request">Request to be executed</param>
            <param name="callback">Callback function to be executed upon completion providing access to the async handle.</param>
            <param name="httpMethod">The HTTP method to execute</param>
        </member>
        <member name="M:RestSharp.RestClient.ExecuteAsync``1(RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse{``0},RestSharp.RestRequestAsyncHandle})">
            <summary>
            Executes the request and callback asynchronously, authenticating if needed
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="request">Request to be executed</param>
            <param name="callback">Callback function to be executed upon completion</param>
        </member>
        <member name="M:RestSharp.RestClient.ExecuteAsyncGet``1(RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse{``0},RestSharp.RestRequestAsyncHandle},System.String)">
            <summary>
            Executes a GET-style request and callback asynchronously, authenticating if needed
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="request">Request to be executed</param>
            <param name="callback">Callback function to be executed upon completion</param>
            <param name="httpMethod">The HTTP method to execute</param>
        </member>
        <member name="M:RestSharp.RestClient.ExecuteAsyncPost``1(RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse{``0},RestSharp.RestRequestAsyncHandle},System.String)">
            <summary>
            Executes a POST-style request and callback asynchronously, authenticating if needed
            </summary>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="request">Request to be executed</param>
            <param name="callback">Callback function to be executed upon completion</param>
            <param name="httpMethod">The HTTP method to execute</param>
        </member>
        <member name="M:RestSharp.RestClient.#ctor">
            <summary>
            Default constructor that registers default content handlers
            </summary>
        </member>
        <member name="M:RestSharp.RestClient.#ctor(System.String)">
            <summary>
            Sets the BaseUrl property for requests made by this client instance
            </summary>
            <param name="baseUrl"></param>
        </member>
        <member name="M:RestSharp.RestClient.AddHandler(System.String,RestSharp.Deserializers.IDeserializer)">
            <summary>
            Registers a content handler to process response content
            </summary>
            <param name="contentType">MIME content type of the response content</param>
            <param name="deserializer">Deserializer to use to process content</param>
        </member>
        <member name="M:RestSharp.RestClient.RemoveHandler(System.String)">
            <summary>
            Remove a content handler for the specified MIME content type
            </summary>
            <param name="contentType">MIME content type to remove</param>
        </member>
        <member name="M:RestSharp.RestClient.ClearHandlers">
            <summary>
            Remove all content handlers
            </summary>
        </member>
        <member name="M:RestSharp.RestClient.GetHandler(System.String)">
            <summary>
            Retrieve the handler for the specified MIME content type
            </summary>
            <param name="contentType">MIME content type to retrieve</param>
            <returns>IDeserializer instance</returns>
        </member>
        <member name="M:RestSharp.RestClient.BuildUri(RestSharp.IRestRequest)">
            <summary>
            Assembles URL to call based on parameters, method and resource
            </summary>
            <param name="request">RestRequest to execute</param>
            <returns>Assembled System.Uri</returns>
        </member>
        <member name="P:RestSharp.RestClient.DefaultParameters">
            <summary>
            Parameters included with every request made with this instance of RestClient
            If specified in both client and request, the request wins
            </summary>
        </member>
        <member name="P:RestSharp.RestClient.MaxRedirects">
            <summary>
            Maximum number of redirects to follow if FollowRedirects is true
            </summary>
        </member>
        <member name="P:RestSharp.RestClient.FollowRedirects">
            <summary>
            Default is true. Determine whether or not requests that result in 
            HTTP status codes of 3xx should follow returned redirect
            </summary>
        </member>
        <member name="P:RestSharp.RestClient.CookieContainer">
            <summary>
            The CookieContainer used for requests made by this client instance
            </summary>
        </member>
        <member name="P:RestSharp.RestClient.UserAgent">
            <summary>
            UserAgent to use for requests made by this client instance
            </summary>
        </member>
        <member name="P:RestSharp.RestClient.Timeout">
            <summary>
            Timeout in milliseconds to use for requests made by this client instance
            </summary>
        </member>
        <member name="P:RestSharp.RestClient.UseSynchronizationContext">
            <summary>
            Whether to invoke async callbacks using the SynchronizationContext.Current captured when invoked
            </summary>
        </member>
        <member name="P:RestSharp.RestClient.Authenticator">
            <summary>
            Authenticator to use for requests made by this client instance
            </summary>
        </member>
        <member name="P:RestSharp.RestClient.BaseUrl">
            <summary>
            Combined with Request.Resource to construct URL for request
            Should include scheme and domain without trailing slash.
            </summary>
            <example>
            client.BaseUrl = "http://example.com";
            </example>
        </member>
        <member name="M:RestSharp.RestClientExtensions.ExecuteAsync(RestSharp.IRestClient,RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse})">
            <summary>
            Executes the request and callback asynchronously, authenticating if needed
            </summary>
            <param name="client">The IRestClient this method extends</param>
            <param name="request">Request to be executed</param>
            <param name="callback">Callback function to be executed upon completion</param>
        </member>
        <member name="M:RestSharp.RestClientExtensions.ExecuteAsync``1(RestSharp.IRestClient,RestSharp.IRestRequest,System.Action{RestSharp.IRestResponse{``0}})">
            <summary>
            Executes the request and callback asynchronously, authenticating if needed
            </summary>
            <param name="client">The IRestClient this method extends</param>
            <typeparam name="T">Target deserialization type</typeparam>
            <param name="request">Request to be executed</param>
            <param name="callback">Callback function to be executed upon completion providing access to the async handle</param>
        </member>
        <member name="M:RestSharp.RestClientExtensions.AddDefaultParameter(RestSharp.IRestClient,RestSharp.Parameter)">
            <summary>
            Add a parameter to use on every request made with this client instance
            </summary>
            <param name="restClient">The IRestClient instance</param>
            <param name="p">Parameter to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.AddDefaultParameter(RestSharp.IRestClient,System.String,System.Object)">
            <summary>
            Adds a HTTP parameter (QueryString for GET, DELETE, OPTIONS and HEAD; Encoded form for POST and PUT)
            Used on every request made by this client instance
            </summary>
            <param name="restClient">The IRestClient instance</param>
            <param name="name">Name of the parameter</param>
            <param name="value">Value of the parameter</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.AddDefaultParameter(RestSharp.IRestClient,System.String,System.Object,RestSharp.ParameterType)">
            <summary>
            Adds a parameter to the request. There are four types of parameters:
            - GetOrPost: Either a QueryString value or encoded form value based on method
            - HttpHeader: Adds the name/value pair to the HTTP request's Headers collection
            - UrlSegment: Inserted into URL if there is a matching url token e.g. {AccountId}
            - RequestBody: Used by AddBody() (not recommended to use directly)
            </summary>
            <param name="restClient">The IRestClient instance</param>
            <param name="name">Name of the parameter</param>
            <param name="value">Value of the parameter</param>
            <param name="type">The type of parameter to add</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.AddDefaultHeader(RestSharp.IRestClient,System.String,System.String)">
            <summary>
            Shortcut to AddDefaultParameter(name, value, HttpHeader) overload
            </summary>
            <param name="restClient">The IRestClient instance</param>
            <param name="name">Name of the header to add</param>
            <param name="value">Value of the header to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestClientExtensions.AddDefaultUrlSegment(RestSharp.IRestClient,System.String,System.String)">
            <summary>
            Shortcut to AddDefaultParameter(name, value, UrlSegment) overload
            </summary>
            <param name="restClient">The IRestClient instance</param>
            <param name="name">Name of the segment to add</param>
            <param name="value">Value of the segment to add</param>
            <returns></returns>
        </member>
        <member name="T:RestSharp.RestRequest">
            <summary>
            Container for data used to make requests
            </summary>
        </member>
        <member name="M:RestSharp.RestRequest.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:RestSharp.RestRequest.#ctor(RestSharp.Method)">
            <summary>
            Sets Method property to value of method
            </summary>
            <param name="method">Method to use for this request</param>
        </member>
        <member name="M:RestSharp.RestRequest.#ctor(System.String)">
            <summary>
            Sets Resource property
            </summary>
            <param name="resource">Resource to use for this request</param>
        </member>
        <member name="M:RestSharp.RestRequest.#ctor(System.String,RestSharp.Method)">
            <summary>
            Sets Resource and Method properties
            </summary>
            <param name="resource">Resource to use for this request</param>
            <param name="method">Method to use for this request</param>
        </member>
        <member name="M:RestSharp.RestRequest.#ctor(System.Uri)">
            <summary>
            Sets Resource property
            </summary>
            <param name="resource">Resource to use for this request</param>
        </member>
        <member name="M:RestSharp.RestRequest.#ctor(System.Uri,RestSharp.Method)">
            <summary>
            Sets Resource and Method properties
            </summary>
            <param name="resource">Resource to use for this request</param>
            <param name="method">Method to use for this request</param>
        </member>
        <member name="M:RestSharp.RestRequest.AddFile(System.String,System.String)">
            <summary>
            Adds a file to the Files collection to be included with a POST or PUT request 
            (other methods do not support file uploads).
            </summary>
            <param name="name">The parameter name to use in the request</param>
            <param name="path">Full path to file to upload</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddFile(System.String,System.Byte[],System.String)">
            <summary>
            Adds the bytes to the Files collection with the specified file name
            </summary>
            <param name="name">The parameter name to use in the request</param>
            <param name="bytes">The file data</param>
            <param name="fileName">The file name to use for the uploaded file</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddFile(System.String,System.Byte[],System.String,System.String)">
            <summary>
            Adds the bytes to the Files collection with the specified file name and content type
            </summary>
            <param name="name">The parameter name to use in the request</param>
            <param name="bytes">The file data</param>
            <param name="fileName">The file name to use for the uploaded file</param>
            <param name="contentType">The MIME type of the file to upload</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddFile(System.String,System.Action{System.IO.Stream},System.String)">
            <summary>
            Adds the bytes to the Files collection with the specified file name and content type
            </summary>
            <param name="name">The parameter name to use in the request</param>
            <param name="writer">A function that writes directly to the stream.  Should NOT close the stream.</param>
            <param name="fileName">The file name to use for the uploaded file</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddFile(System.String,System.Action{System.IO.Stream},System.String,System.String)">
            <summary>
            Adds the bytes to the Files collection with the specified file name and content type
            </summary>
            <param name="name">The parameter name to use in the request</param>
            <param name="writer">A function that writes directly to the stream.  Should NOT close the stream.</param>
            <param name="fileName">The file name to use for the uploaded file</param>
            <param name="contentType">The MIME type of the file to upload</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddBody(System.Object,System.String)">
            <summary>
            Serializes obj to format specified by RequestFormat, but passes xmlNamespace if using the default XmlSerializer
            </summary>
            <param name="obj">The object to serialize</param>
            <param name="xmlNamespace">The XML namespace to use when serializing</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddBody(System.Object)">
            <summary>
            Serializes obj to data format specified by RequestFormat and adds it to the request body.
            </summary>
            <param name="obj">The object to serialize</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddObject(System.Object,System.String[])">
            <summary>
            Calls AddParameter() for all public, readable properties specified in the white list
            </summary>
            <example>
            request.AddObject(product, "ProductId", "Price", ...);
            </example>
            <param name="obj">The object with properties to add as parameters</param>
            <param name="whitelist">The names of the properties to include</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddObject(System.Object)">
            <summary>
            Calls AddParameter() for all public, readable properties of obj
            </summary>
            <param name="obj">The object with properties to add as parameters</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddParameter(RestSharp.Parameter)">
            <summary>
            Add the parameter to the request
            </summary>
            <param name="p">Parameter to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddParameter(System.String,System.Object)">
            <summary>
            Adds a HTTP parameter to the request (QueryString for GET, DELETE, OPTIONS and HEAD; Encoded form for POST and PUT)
            </summary>
            <param name="name">Name of the parameter</param>
            <param name="value">Value of the parameter</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddParameter(System.String,System.Object,RestSharp.ParameterType)">
            <summary>
            Adds a parameter to the request. There are four types of parameters:
            - GetOrPost: Either a QueryString value or encoded form value based on method
            - HttpHeader: Adds the name/value pair to the HTTP request's Headers collection
            - UrlSegment: Inserted into URL if there is a matching url token e.g. {AccountId}
            - RequestBody: Used by AddBody() (not recommended to use directly)
            </summary>
            <param name="name">Name of the parameter</param>
            <param name="value">Value of the parameter</param>
            <param name="type">The type of parameter to add</param>
            <returns>This request</returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddHeader(System.String,System.String)">
            <summary>
            Shortcut to AddParameter(name, value, HttpHeader) overload
            </summary>
            <param name="name">Name of the header to add</param>
            <param name="value">Value of the header to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddCookie(System.String,System.String)">
            <summary>
            Shortcut to AddParameter(name, value, Cookie) overload
            </summary>
            <param name="name">Name of the cookie to add</param>
            <param name="value">Value of the cookie to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequest.AddUrlSegment(System.String,System.String)">
            <summary>
            Shortcut to AddParameter(name, value, UrlSegment) overload
            </summary>
            <param name="name">Name of the segment to add</param>
            <param name="value">Value of the segment to add</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.RestRequest.IncreaseNumAttempts">
            <summary>
            Internal Method so that RestClient can increase the number of attempts
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.JsonSerializer">
            <summary>
            Serializer to use when writing JSON request bodies. Used if RequestFormat is Json.
            By default the included JsonSerializer is used (currently using JSON.NET default serialization).
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.XmlSerializer">
            <summary>
            Serializer to use when writing XML request bodies. Used if RequestFormat is Xml.
            By default the included XmlSerializer is used.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Parameters">
            <summary>
            Container of all HTTP parameters to be passed with the request. 
            See AddParameter() for explanation of the types of parameters that can be passed
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Files">
            <summary>
            Container of all the files to be uploaded with the request.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Method">
            <summary>
            Determines what HTTP method to use for this request. Supported methods: GET, POST, PUT, DELETE, HEAD, OPTIONS
            Default is GET
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Resource">
            <summary>
            The Resource URL to make the request against.
            Tokens are substituted with UrlSegment parameters and match by name.
            Should not include the scheme or domain. Do not include leading slash.
            Combined with RestClient.BaseUrl to assemble final URL:
            {BaseUrl}/{Resource} (BaseUrl is scheme + domain, e.g. http://example.com)
            </summary>
            <example>
            // example for url token replacement
            request.Resource = "Products/{ProductId}";
            request.AddParameter("ProductId", 123, ParameterType.UrlSegment);
            </example>
        </member>
        <member name="P:RestSharp.RestRequest.RequestFormat">
            <summary>
            Serializer to use when writing XML request bodies. Used if RequestFormat is Xml.
            By default XmlSerializer is used.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.RootElement">
            <summary>
            Used by the default deserializers to determine where to start deserializing from.
            Can be used to skip container or root elements that do not have corresponding deserialzation targets.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.OnBeforeDeserialization">
            <summary>
            A function to run prior to deserializing starting (e.g. change settings if error encountered)
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.DateFormat">
            <summary>
            Used by the default deserializers to explicitly set which date format string to use when parsing dates.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.XmlNamespace">
            <summary>
            Used by XmlDeserializer. If not specified, XmlDeserializer will flatten response by removing namespaces from element names.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Credentials">
            <summary>
            In general you would not need to set this directly. Used by the NtlmAuthenticator. 
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.UserState">
            <summary>
            Gets or sets a user-defined state object that contains information about a request and which can be later 
            retrieved when the request completes.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Timeout">
            <summary>
            Timeout in milliseconds to be used for the request. This timeout value overrides a timeout set on the RestClient.
            </summary>
        </member>
        <member name="P:RestSharp.RestRequest.Attempts">
            <summary>
            How many attempts were made to send this Request?
            </summary>
            <remarks>
            This Number is incremented each time the RestClient sends the request.
            Useful when using Asynchronous Execution with Callbacks
            </remarks>
        </member>
        <member name="T:RestSharp.RestResponseBase">
            <summary>
            Base class for common properties shared by RestResponse and RestResponse[[T]]
            </summary>
        </member>
        <member name="M:RestSharp.RestResponseBase.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.Request">
            <summary>
            The RestRequest that was made to get this RestResponse
            </summary>
            <remarks>
            Mainly for debugging if ResponseStatus is not OK
            </remarks> 
        </member>
        <member name="P:RestSharp.RestResponseBase.ContentType">
            <summary>
            MIME content type of response
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.ContentLength">
            <summary>
            Length in bytes of the response content
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.ContentEncoding">
            <summary>
            Encoding of the response content
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.Content">
            <summary>
            String representation of response content
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.StatusCode">
            <summary>
            HTTP response status code
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.StatusDescription">
            <summary>
            Description of HTTP status returned
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.RawBytes">
            <summary>
            Response content
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.ResponseUri">
            <summary>
            The URL that actually responded to the content (different from request if redirected)
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.Server">
            <summary>
            HttpWebResponse.Server
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.Cookies">
            <summary>
            Cookies returned by server with the response
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.Headers">
            <summary>
            Headers returned by server with the response
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.ResponseStatus">
            <summary>
            Status of the request. Will return Error for transport errors.
            HTTP errors will still return ResponseStatus.Completed, check StatusCode instead
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.ErrorMessage">
            <summary>
            Transport or other non-HTTP error generated while attempting request
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseBase.ErrorException">
            <summary>
            The exception thrown during the request, if any
            </summary>
        </member>
        <member name="T:RestSharp.RestResponse`1">
            <summary>
            Container for data sent back from API including deserialized data
            </summary>
            <typeparam name="T">Type of data to deserialize to</typeparam>
        </member>
        <member name="P:RestSharp.RestResponse`1.Data">
            <summary>
            Deserialized entity data
            </summary>
        </member>
        <member name="T:RestSharp.RestResponse">
            <summary>
            Container for data sent back from API
            </summary>
        </member>
        <member name="T:RestSharp.Serializers.DotNetXmlSerializer">
            <summary>
            Wrapper for System.Xml.Serialization.XmlSerializer.
            </summary>
        </member>
        <member name="M:RestSharp.Serializers.DotNetXmlSerializer.#ctor">
            <summary>
            Default constructor, does not specify namespace
            </summary>
        </member>
        <member name="M:RestSharp.Serializers.DotNetXmlSerializer.#ctor(System.String)">
            <summary>
            Specify the namespaced to be used when serializing
            </summary>
            <param name="namespace">XML namespace</param>
        </member>
        <member name="M:RestSharp.Serializers.DotNetXmlSerializer.Serialize(System.Object)">
            <summary>
            Serialize the object as XML
            </summary>
            <param name="obj">Object to serialize</param>
            <returns>XML as string</returns>
        </member>
        <member name="P:RestSharp.Serializers.DotNetXmlSerializer.RootElement">
            <summary>
            Name of the root element to use when serializing
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.DotNetXmlSerializer.Namespace">
            <summary>
            XML namespace to use when serializing
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.DotNetXmlSerializer.DateFormat">
            <summary>
            Format string to use when serializing dates
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.DotNetXmlSerializer.ContentType">
            <summary>
            Content type for serialized content
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.DotNetXmlSerializer.Encoding">
            <summary>
            Encoding for serialized content
            </summary>
        </member>
        <member name="T:RestSharp.Serializers.DotNetXmlSerializer.EncodingStringWriter">
            <summary>
            Need to subclass StringWriter in order to override Encoding
            </summary>
        </member>
        <member name="T:RestSharp.Serializers.JsonSerializer">
            <summary>
            Default JSON serializer for request bodies
            Doesn't currently use the SerializeAs attribute, defers to Newtonsoft's attributes
            </summary>
        </member>
        <member name="M:RestSharp.Serializers.JsonSerializer.#ctor">
            <summary>
            Default serializer
            </summary>
        </member>
        <member name="M:RestSharp.Serializers.JsonSerializer.Serialize(System.Object)">
            <summary>
            Serialize the object as JSON
            </summary>
            <param name="obj">Object to serialize</param>
            <returns>JSON as String</returns>
        </member>
        <member name="P:RestSharp.Serializers.JsonSerializer.DateFormat">
            <summary>
            Unused for JSON Serialization
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.JsonSerializer.RootElement">
            <summary>
            Unused for JSON Serialization
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.JsonSerializer.Namespace">
            <summary>
            Unused for JSON Serialization
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.JsonSerializer.ContentType">
            <summary>
            Content type for serialized content
            </summary>
        </member>
        <member name="T:RestSharp.Serializers.SerializeAsAttribute">
            <summary>
            Allows control how class and property names and values are serialized by XmlSerializer
            Currently not supported with the JsonSerializer
            When specified at the property level the class-level specification is overridden
            </summary>
        </member>
        <member name="M:RestSharp.Serializers.SerializeAsAttribute.TransformName(System.String)">
            <summary>
            Called by the attribute when NameStyle is speficied
            </summary>
            <param name="input">The string to transform</param>
            <returns>String</returns>
        </member>
        <member name="P:RestSharp.Serializers.SerializeAsAttribute.Name">
            <summary>
            The name to use for the serialized element
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.SerializeAsAttribute.Attribute">
            <summary>
            Sets the value to be serialized as an Attribute instead of an Element
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.SerializeAsAttribute.Culture">
            <summary>
            The culture to use when serializing
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.SerializeAsAttribute.NameStyle">
            <summary>
            Transforms the casing of the name based on the selected value.
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.SerializeAsAttribute.Index">
            <summary>
            The order to serialize the element. Default is int.MaxValue.
            </summary>
        </member>
        <member name="T:RestSharp.Serializers.NameStyle">
            <summary>
            Options for transforming casing of element names
            </summary>
        </member>
        <member name="T:RestSharp.Serializers.XmlSerializer">
            <summary>
            Default XML Serializer
            </summary>
        </member>
        <member name="M:RestSharp.Serializers.XmlSerializer.#ctor">
            <summary>
            Default constructor, does not specify namespace
            </summary>
        </member>
        <member name="M:RestSharp.Serializers.XmlSerializer.#ctor(System.String)">
            <summary>
            Specify the namespaced to be used when serializing
            </summary>
            <param name="namespace">XML namespace</param>
        </member>
        <member name="M:RestSharp.Serializers.XmlSerializer.Serialize(System.Object)">
            <summary>
            Serialize the object as XML
            </summary>
            <param name="obj">Object to serialize</param>
            <returns>XML as string</returns>
        </member>
        <member name="P:RestSharp.Serializers.XmlSerializer.RootElement">
            <summary>
            Name of the root element to use when serializing
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.XmlSerializer.Namespace">
            <summary>
            XML namespace to use when serializing
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.XmlSerializer.DateFormat">
            <summary>
            Format string to use when serializing dates
            </summary>
        </member>
        <member name="P:RestSharp.Serializers.XmlSerializer.ContentType">
            <summary>
            Content type for serialized content
            </summary>
        </member>
        <member name="T:RestSharp.JsonArray">
            <summary>
            Represents the json array.
            </summary>
        </member>
        <member name="M:RestSharp.JsonArray.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RestSharp.JsonArray"/> class. 
            </summary>
        </member>
        <member name="M:RestSharp.JsonArray.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:RestSharp.JsonArray"/> class. 
            </summary>
            <param name="capacity">The capacity of the json array.</param>
        </member>
        <member name="M:RestSharp.JsonArray.ToString">
            <summary>
            The json representation of the array.
            </summary>
            <returns>The json representation of the array.</returns>
        </member>
        <member name="T:RestSharp.JsonObject">
            <summary>
            Represents the json object.
            </summary>
        </member>
        <member name="F:RestSharp.JsonObject._members">
            <summary>
            The internal member dictionary.
            </summary>
        </member>
        <member name="M:RestSharp.JsonObject.Add(System.String,System.Object)">
            <summary>
            Adds the specified key.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:RestSharp.JsonObject.ContainsKey(System.String)">
            <summary>
            Determines whether the specified key contains key.
            </summary>
            <param name="key">The key.</param>
            <returns>
            	<c>true</c> if the specified key contains key; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:RestSharp.JsonObject.Remove(System.String)">
            <summary>
            Removes the specified key.
            </summary>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.JsonObject.TryGetValue(System.String,System.Object@)">
            <summary>
            Tries the get value.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.JsonObject.Add(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Adds the specified item.
            </summary>
            <param name="item">The item.</param>
        </member>
        <member name="M:RestSharp.JsonObject.Clear">
            <summary>
            Clears this instance.
            </summary>
        </member>
        <member name="M:RestSharp.JsonObject.Contains(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Determines whether [contains] [the specified item].
            </summary>
            <param name="item">The item.</param>
            <returns>
            	<c>true</c> if [contains] [the specified item]; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:RestSharp.JsonObject.CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Object}[],System.Int32)">
            <summary>
            Copies to.
            </summary>
            <param name="array">The array.</param>
            <param name="arrayIndex">Index of the array.</param>
        </member>
        <member name="M:RestSharp.JsonObject.Remove(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Removes the specified item.
            </summary>
            <param name="item">The item.</param>
            <returns></returns>
        </member>
        <member name="M:RestSharp.JsonObject.GetEnumerator">
            <summary>
            Gets the enumerator.
            </summary>
            <returns></returns>
        </member>
        <member name="M:RestSharp.JsonObject.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:RestSharp.JsonObject.ToString">
            <summary>
            Returns a json <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A json <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="P:RestSharp.JsonObject.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:System.Object"/> at the specified index.
            </summary>
            <value></value>
        </member>
        <member name="P:RestSharp.JsonObject.Keys">
            <summary>
            Gets the keys.
            </summary>
            <value>The keys.</value>
        </member>
        <member name="P:RestSharp.JsonObject.Values">
            <summary>
            Gets the values.
            </summary>
            <value>The values.</value>
        </member>
        <member name="P:RestSharp.JsonObject.Item(System.String)">
            <summary>
            Gets or sets the <see cref="T:System.Object"/> with the specified key.
            </summary>
            <value></value>
        </member>
        <member name="P:RestSharp.JsonObject.Count">
            <summary>
            Gets the count.
            </summary>
            <value>The count.</value>
        </member>
        <member name="P:RestSharp.JsonObject.IsReadOnly">
            <summary>
            Gets a value indicating whether this instance is read only.
            </summary>
            <value>
            	<c>true</c> if this instance is read only; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:RestSharp.SimpleJson">
            <summary>
            This class encodes and decodes JSON strings.
            Spec. details, see http://www.json.org/
            
            JSON uses Arrays and Objects. These correspond here to the datatypes JsonArray(IList&lt;object>) and JsonObject(IDictionary&lt;string,object>).
            All numbers are parsed to doubles.
            </summary>
        </member>
        <member name="M:RestSharp.SimpleJson.DeserializeObject(System.String)">
            <summary>
            Parses the string json into a value
            </summary>
            <param name="json">A JSON string.</param>
            <returns>An IList&lt;object>, a IDictionary&lt;string,object>, a double, a string, null, true, or false</returns>
        </member>
        <member name="M:RestSharp.SimpleJson.TryDeserializeObject(System.String,System.Object@)">
            <summary>
            Try parsing the json string into a value.
            </summary>
            <param name="json">
            A JSON string.
            </param>
            <param name="object">
            The object.
            </param>
            <returns>
            Returns true if successfull otherwise false.
            </returns>
        </member>
        <member name="M:RestSharp.SimpleJson.SerializeObject(System.Object,RestSharp.IJsonSerializerStrategy)">
            <summary>
            Converts a IDictionary&lt;string,object> / IList&lt;object> object into a JSON string
            </summary>
            <param name="json">A IDictionary&lt;string,object> / IList&lt;object></param>
            <param name="jsonSerializerStrategy">Serializer strategy to use</param>
            <returns>A JSON encoded string, or null if object 'json' is not serializable</returns>
        </member>
        <member name="M:RestSharp.SimpleJson.IsNumeric(System.Object)">
            <summary>
            Determines if a given object is numeric in any way
            (can be integer, double, null, etc).
            </summary>
        </member>
        <member name="T:RestSharp.Validation.Require">
            <summary>
            Helper methods for validating required values
            </summary>
        </member>
        <member name="M:RestSharp.Validation.Require.Argument(System.String,System.Object)">
            <summary>
            Require a parameter to not be null
            </summary>
            <param name="argumentName">Name of the parameter</param>
            <param name="argumentValue">Value of the parameter</param>
        </member>
        <member name="T:RestSharp.Validation.Validate">
            <summary>
            Helper methods for validating values
            </summary>
        </member>
        <member name="M:RestSharp.Validation.Validate.IsBetween(System.Int32,System.Int32,System.Int32)">
            <summary>
            Validate an integer value is between the specified values (exclusive of min/max)
            </summary>
            <param name="value">Value to validate</param>
            <param name="min">Exclusive minimum value</param>
            <param name="max">Exclusive maximum value</param>
        </member>
        <member name="M:RestSharp.Validation.Validate.IsValidLength(System.String,System.Int32)">
            <summary>
            Validate a string length
            </summary>
            <param name="value">String to be validated</param>
            <param name="maxSize">Maximum length of the string</param>
        </member>
        <member name="P:RestSharp.RestResponseCookie.Comment">
            <summary>
            Comment of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseCookie.CommentUri">
            <summary>
            Comment of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseCookie.Discard">
            <summary>
            Indicates whether the cookie should be discarded at the end of the session
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseCookie.Domain">
            <summary>
            Domain of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseCookie.Expired">
            <summary>
            Indicates whether the cookie is expired
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseCookie.Expires">
            <summary>
            Date and time that the cookie expires
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseCookie.HttpOnly">
            <summary>
            Indicates that this cookie should only be accessed by the server
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseCookie.Name">
            <summary>
            Name of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseCookie.Path">
            <summary>
            Path of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseCookie.Port">
            <summary>
            Port of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseCookie.Secure">
            <summary>
            Indicates that the cookie should only be sent over secure channels
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseCookie.TimeStamp">
            <summary>
            Date and time the cookie was created
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseCookie.Value">
            <summary>
            Value of the cookie
            </summary>
        </member>
        <member name="P:RestSharp.RestResponseCookie.Version">
            <summary>
            Version of the cookie
            </summary>
        </member>
    </members>
</doc>
