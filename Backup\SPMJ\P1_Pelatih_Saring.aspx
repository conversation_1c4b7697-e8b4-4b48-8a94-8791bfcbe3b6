﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P1_Pelatih_Saring.aspx.vb" Inherits="SPMJ.WebForm5" 
    title="Untitled Page"  %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style79
        {
            height: 28px;
        }
        .style2
        {
            height: 3px;
            width: 718px;
        }
        .style80
        {
            width: 718px;
        }
        .style81
        {
            height: 28px;
            width: 718px;
        }
        .style82
        {
            width: 173px;
        }
        .style83
        {
            height: 28px;
            width: 173px;
        }
        </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    
    <div style="height: 100%; width: 100%; background-image: url('Image/Bg_Dot2.gif'); background-attachment: fixed;">
        <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td class="style82"></td>
            <td class="style80"></td>
            <td></td>
        </tr>
        <tr>
            <td class="style82">&nbsp;</td>
            <td class="style80">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style83"></td>
            <td align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#8CBB00" class="style81">Saring Pelatih Untuk Peperiksaan - Carian 
                Rekod Pelatih</td>
            <td class="style79"></td>
        </tr>
        <tr>
            <td class="style82"></td>
            <td style="border-width: 1px; border-right-style: solid; border-left-style: solid; border-color: #5D7B9D" 
                bgcolor="White" class="style80">&nbsp;&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style82"></td>
            <td 
                
                style="border-width: 1px; border-right-style: solid; border-left-style: solid; border-color: #5D7B9D" 
                bgcolor="White" class="style80">
                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj1" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="16px" tabIndex="36" Width="144px" Enabled="False" 
                            ReadOnly="True">KOLEJ / INSTITUSI</asp:TextBox>
                        <asp:DropDownList ID="Cb_Kolej" runat="server" AutoPostBack="True" 
                            Font-Names="Arial" Font-Size="8pt" Width="520px">
                        </asp:DropDownList>
                         <br />
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj6" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="16px" tabIndex="36" Width="144px" Enabled="False" 
                            ReadOnly="True">JENIS KURSUS</asp:TextBox>
                        <asp:DropDownList ID="Cb_Kursus" runat="server" AutoPostBack="True" 
                            Font-Names="Arial" Font-Size="8pt" Width="300px">
                        </asp:DropDownList>
                        <br />
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="16px" tabIndex="36" Width="144px" Enabled="False" 
                            ReadOnly="True">SESI PENGAMBILAN</asp:TextBox>
                        <asp:DropDownList ID="Cb_Sesi" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="156px">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
&nbsp;</td>
            <td align="center"></td>
        </tr>
        <tr>
            <td class="style82"></td>
            <td 
                
                style="border-left-style: solid; border-width: 1px; border-color: #5D7B9D; border-right-style: solid;" 
                bgcolor="White" class="style2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="16px" 
            tabIndex="36" Width="144px" Enabled="False" 
                            ReadOnly="True">NAMA</asp:TextBox>
                <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="150px" 
                    Wrap="False"></asp:TextBox>
                <asp:Button ID="cmdHantar0" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CARI" Width="60px" />
            </td>
            <td></td>
        </tr>
        <tr>
            <td class="style82">&nbsp;</td>
            <td 
                             
                style="border-left-style: solid; border-width: 1px; border-color: #5D7B9D; border-right-style: solid;" 
                bgcolor="White" class="style80">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj3" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="144px" Enabled="False" 
                            ReadOnly="True">NO. KP/TENTERA/PASPORT</asp:TextBox>
                <asp:TextBox ID="Tx_NoKP" runat="server" CssClass="std" Width="150px" 
                    Wrap="False"></asp:TextBox>
                <asp:Button ID="cmdHantar1" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CARI" Width="60px" />
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style82"></td>
            <td bgcolor="White" 
                
                style="border-left-style: solid; border-width: 1px; border-color: #5D7B9D; border-right-style: solid;" 
                class="style80">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:TextBox 
                    ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" 
            tabIndex="36" Width="144px" Enabled="False" 
                            ReadOnly="True">STATUS</asp:TextBox>
                <asp:DropDownList ID="Cb_Saring" runat="server" Font-Names="Arial" 
            Font-Size="8pt" Width="157px">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem>TELAH DISARING</asp:ListItem>
                    <asp:ListItem>BELUM DISARING</asp:ListItem>
        </asp:DropDownList>
                <asp:Button ID="cmd_Saring" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CARI" Width="60px" />
            </td>
            <td></td>
        </tr>
    
    
        <tr>
            <td class="style82">&nbsp;</td>
            <td bgcolor="White" 
                
                style="border-bottom-style: solid; border-left-style: solid; border-width: 1px; border-color: #5D7B9D; border-right-style: solid;" 
                class="style80">
                &nbsp;</td>
            <td>&nbsp;</td>
        </tr>
    
    
        <tr>
            <td class="style82">&nbsp;</td>
            <td class="style80">
                &nbsp;</td>
            <td>&nbsp;</td>
        </tr>
    
    
        <tr>
            <td class="style82">&nbsp;</td>
            <td class="style80">
        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="9pt" ForeColor="Black" GridLines="Horizontal" HorizontalAlign="Left" 
                    Width="100%" Visible="False" BorderColor="#8CBB00">
                    <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                    <RowStyle BackColor="White" />
                    <Columns>
                        <asp:TemplateField HeaderText="#">
                            <HeaderStyle HorizontalAlign="Center" />
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="PILIH">
                            <ItemTemplate>
                                <asp:CheckBox ID="chk_Pilih" runat="server" />
                            </ItemTemplate>
                            <EditItemTemplate>
                                <asp:CheckBox ID="CheckBox1" runat="server" />
                            </EditItemTemplate>
                            <HeaderTemplate>
                                PILIH
                            </HeaderTemplate>
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                    </Columns>
                    <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                    <SelectedRowStyle Font-Bold="True" />
                    <HeaderStyle BackColor="#8CBB00" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                    <AlternatingRowStyle BackColor="White" />
                </asp:GridView>
                <br />
		<br />
                <br />
                <br />
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>
    
    
        <tr>
            <td class="style82">&nbsp;</td>
            <td class="style80">
                <br />
                        <asp:Button ID="cmd_Hantar" runat="server" Font-Names="Arial" Font-Size="8pt" 
                            Height="20px" tabIndex="3" Text="KEMASKINI" Width="80px" 
            Visible="False" />
                        <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>
    
    
    </table>
    
    </div></asp:Content>
