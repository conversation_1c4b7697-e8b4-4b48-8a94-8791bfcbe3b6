﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm43
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Tb = "jt_tpc"

        Dim List_Adp As New SqlDataAdapter(X, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Add verify login 06122019 -OSH 
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'Fix security access 23072018 - OSH 
        If Session("ORIGIN") = "yes" Then
            If IsPostBack Then Exit Sub

            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

            'JAWATAN
            Cb_Jawatan.Items.Clear()
            Cb_Jawatan.Items.Add("(SEMUA)")
            Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = ""
            Cb_Jawatan.Items.Add("JURURAWAT TERLATIH")
            Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "1"
            Cb_Jawatan.Items.Add("LATIHAN ELEKTIF")
            Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "2"
            Cb_Jawatan.Items.Add("PENGAJAR")
            Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "3"
            Cb_Jawatan.Items.Add("INSTRUKTOR KLINIKAL")
            Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "4"

            'NEGERI
            Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
            Rdr = Cmd.ExecuteReader()
            Cb_Negeri.Items.Clear()
            Cb_Negeri.Items.Add("(SEMUA)")
            Cb_Negeri.Items.Item(Cb_Negeri.Items.Count - 1).Value = ""
            While Rdr.Read
                Cb_Negeri.Items.Add(Rdr(0))
                Cb_Negeri.Items.Item(Cb_Negeri.Items.Count - 1).Value = Rdr(1)
            End While
            Rdr.Close()

            'AMALAN
            Cmd.CommandText = "SELECT Dc_amalan, Id_amalan FROM pn_tpt_amalan where sektor=2 ORDER BY Dc_amalan"
            Rdr = Cmd.ExecuteReader()
            Cb_Tpt_Amalan.Items.Clear()
            Cb_Tpt_Amalan.Items.Add("(SEMUA)")
            Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = ""
            While Rdr.Read
                Cb_Tpt_Amalan.Items.Add(Rdr(0))
                Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = Rdr(1)
            End While
            Rdr.Close()
            Cn.Close()

            'TAHUN
            Dim i As Int16
            Cb_Tahun.Items.Clear()
            Cb_Tahun.Items.Add("")
            For i = 0 To 9
                Cb_Tahun.Items.Add(Year(Now) - i)
            Next i

            'JANTINA
            Cb_Jantina.Items.Clear()
            Cb_Jantina.Items.Add("(SEMUA)")
            Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = ""
            Cb_Jantina.Items.Add("LELAKI")
            Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = "1"
            Cb_Jantina.Items.Add("PEREMPUAN")
            Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = "2"

        Else
            Response.Redirect("p0_Login.aspx")
        End If

        'Comment Ori 23072018 -OSH
        'If IsPostBack Then Exit Sub

        'Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        'Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        ''JAWATAN
        'Cb_Jawatan.Items.Clear()
        'Cb_Jawatan.Items.Add("(SEMUA)")
        'Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = ""
        'Cb_Jawatan.Items.Add("JURURAWAT TERLATIH")
        'Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "1"
        'Cb_Jawatan.Items.Add("LATIHAN ELEKTIF")
        'Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "2"
        'Cb_Jawatan.Items.Add("PENGAJAR")
        'Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "3"
        'Cb_Jawatan.Items.Add("INSTRUKTOR KLINIKAL")
        'Cb_Jawatan.Items.Item(Cb_Jawatan.Items.Count - 1).Value = "4"

        ''NEGERI
        'Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        'Rdr = Cmd.ExecuteReader()
        'Cb_Negeri.Items.Clear()
        'Cb_Negeri.Items.Add("(SEMUA)")
        'Cb_Negeri.Items.Item(Cb_Negeri.Items.Count - 1).Value = ""
        'While Rdr.Read
        '    Cb_Negeri.Items.Add(Rdr(0))
        '    Cb_Negeri.Items.Item(Cb_Negeri.Items.Count - 1).Value = Rdr(1)
        'End While
        'Rdr.Close()

        ''AMALAN
        'Cmd.CommandText = "SELECT Dc_amalan, Id_amalan FROM pn_tpt_amalan where sektor=2 ORDER BY Dc_amalan"
        'Rdr = Cmd.ExecuteReader()
        'Cb_Tpt_Amalan.Items.Clear()
        'Cb_Tpt_Amalan.Items.Add("(SEMUA)")
        'Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = ""
        'While Rdr.Read
        '    Cb_Tpt_Amalan.Items.Add(Rdr(0))
        '    Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = Rdr(1)
        'End While
        'Rdr.Close()
        'Cn.Close()

        ''TAHUN
        'Dim i As Int16
        'Cb_Tahun.Items.Clear()
        'Cb_Tahun.Items.Add("")
        'For i = 0 To 9
        '    Cb_Tahun.Items.Add(Year(Now) - i)
        'Next i

        ''JANTINA
        'Cb_Jantina.Items.Clear()
        'Cb_Jantina.Items.Add("(SEMUA)")
        'Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = ""
        'Cb_Jantina.Items.Add("LELAKI")
        'Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = "1"
        'Cb_Jantina.Items.Add("PEREMPUAN")
        'Cb_Jantina.Items.Item(Cb_Jantina.Items.Count - 1).Value = "2"
    End Sub

    Protected Sub Cb_Negeri_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Negeri.SelectedIndexChanged
        Dim SQL As String
        SQL = "select dc_amalan, id_amalan from pn_tpt_amalan where sektor=2 and negeri = '" & Cb_Negeri.SelectedValue & "' order by dc_amalan"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'TEMPAT AMALAN
        Cmd.CommandText = SQL
        Rdr = Cmd.ExecuteReader()
        Cb_Tpt_Amalan.Items.Clear()
        Cb_Tpt_Amalan.Items.Add("(SEMUA)")
        Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Tpt_Amalan.Items.Add(Rdr(0))
            Cb_Tpt_Amalan.Items.Item(Cb_Tpt_Amalan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari2.Click
        GridViewExportUtil.Export("Laporan.xls", Gd)
    End Sub

    Public Sub Laporan()
        Dim SQL, SQL_pilih, order As String
        SQL = "select distinct"
        SQL_pilih = ""
        order = ""
        If Chk_Pilih.Items(0).Selected Then SQL_pilih += " jt.nokp as 'NO. PASPORT' ,"
        If Chk_Pilih.Items(1).Selected Then SQL_pilih += " nama as 'NAMA'," : order = "order by nama"
        If Chk_Pilih.Items(2).Selected Then SQL_pilih += " nopd as 'NO. TPC',"
        If Chk_Pilih.Items(3).Selected Then SQL_pilih += " Umur as 'UMUR',"
        If Chk_Pilih.Items(4).Selected Then SQL_pilih += " case t_kahwin when 1 then 'BUJANG' when 2 then 'BERKAHWIN' when 3 then 'DUDA' when 4 then 'JANDA' end as 'TARAF PERKAHWINAN',"
        If Chk_Pilih.Items(5).Selected Then SQL_pilih += " dc_negara as 'WARGANEGARA',"
        If Chk_Pilih.Items(6).Selected Then SQL_pilih += " case jantina when 1 then 'L' when 2 then 'P' else '' end as 'JANTINA',"
        If Chk_Pilih.Items(7).Selected Then SQL_pilih += " case jt.j_daftar when 1 then 'JB' when 2 then 'LE' when 3 then 'PG' when 4 then 'IK' else '' end as 'JAWATAN DIPOHON',"
        If Chk_Pilih.Items(8).Selected Then SQL_pilih += " CONVERT(char(12), mohon_tkh, 103) as 'TARIKH PERMOHONAN',"
        If Chk_Pilih.Items(9).Selected Then SQL_pilih += " dc_amalan as 'TEMPAT AMALAN',"
        If Chk_Pilih.Items(10).Selected Then SQL_pilih += " upper(pta.alamat) as 'ALAMAT AMALAN',"
        If Len(SQL_pilih) > 0 Then SQL_pilih = Mid(SQL_pilih, 1, Len(SQL_pilih) - 1) : SQL += SQL_pilih

        If Cb_Status.SelectedIndex = 0 Then
            SQL += " from jt_tpc jt "
            SQL += "inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp "
            SQL += "inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp "
            SQL += "left join pn_negara pn on jt.warganegara=pn.id_negara "
            SQL += "left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan "
            SQL += "left join pn_negeri pni on pni.id_negeri=pta.negeri "
            SQL += "where jt.nokp is not null and status=1 "
        Else
            SQL += " from tmp_tpc jt "
            SQL += "inner join tmp_tpc_majikan ttm on jt.nokp=ttm.nokp "
            SQL += "left join pn_negara w on jt.warganegara=w.id_negara "
            SQL += "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan "
            SQL += "where jt.nokp is not null "
        End If

        If Cb_Negeri.SelectedIndex < 1 Then  Else SQL += " and pta.negeri = " & Cb_Negeri.SelectedValue

        If Cb_Tpt_Amalan.SelectedIndex < 1 Then  Else SQL += " and pta.id_amalan = " & Cb_Tpt_Amalan.SelectedValue

        If Cb_Jawatan.SelectedIndex < 1 Then  Else SQL += " and jt.j_daftar = " & Cb_Jawatan.SelectedValue

        If Cb_Tahun.SelectedItem.Text = "" Then  Else SQL += " and jtt.tpc_tahun = " & Cb_Tahun.SelectedValue

        If Cb_Jantina.SelectedIndex < 1 Then  Else SQL += " and jt.jantina = " & Cb_Jantina.SelectedValue

        If Tx_NoTPC1.Text = "" Then
        Else
            If Tx_NoTPC2.Text = "" Then Tx_NoTPC2.Text = Tx_NoTPC1.Text
            SQL += " and jt.nopd between '" & CLng(Tx_NoTPC1.Text) & "' and '" & CLng(Tx_NoTPC2.Text) & "' "
        End If

        SQL += order

        Try
            Cari(SQL)
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Laporan()
    End Sub
End Class