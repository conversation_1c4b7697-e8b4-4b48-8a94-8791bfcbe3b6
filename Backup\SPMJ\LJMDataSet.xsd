﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="LJMDataSet" targetNamespace="http://tempuri.org/LJMDataSet.xsd" xmlns:mstns="http://tempuri.org/LJMDataSet.xsd" xmlns="http://tempuri.org/LJMDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="LJMConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="LJMConnectionString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.LJMConnectionString.ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="JT_PENUH_APCTableAdapter" GeneratorDataComponentClassName="JT_PENUH_APCTableAdapter" Name="JT_PENUH_APC" UserDataComponentName="JT_PENUH_APCTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="LJMConnectionString (Web.config)" DbObjectName="LJM.dbo.JT_PENUH_APC" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[JT_PENUH_APC] WHERE (([NoKP] = @Original_NoKP) AND ([J_Daftar] = @Original_J_Daftar) AND ([Apc_Tahun] = @Original_Apc_Tahun))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_NoKP" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="NoKP" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@Original_J_Daftar" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="J_Daftar" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Apc_Tahun" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Apc_Tahun" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[JT_PENUH_APC] ([NoKP], [J_Daftar], [Apc_No], [Apc_Tahun], [Apc_Tkh], [Apc_TkhResit], [Apc_NoResit], [Apc_Amaun], [Apc_Lwt_TkhResit], [Apc_Lwt_NoResit], [Apc_Lwt_Amaun], [Id_Amalan], [Log_Id], [Log_Tkh]) VALUES (@NoKP, @J_Daftar, @Apc_No, @Apc_Tahun, @Apc_Tkh, @Apc_TkhResit, @Apc_NoResit, @Apc_Amaun, @Apc_Lwt_TkhResit, @Apc_Lwt_NoResit, @Apc_Lwt_Amaun, @Id_Amalan, @Log_Id, @Log_Tkh)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@NoKP" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="NoKP" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@J_Daftar" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="J_Daftar" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Apc_No" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Apc_No" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Apc_Tahun" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Apc_Tahun" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Apc_Tkh" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Apc_Tkh" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Apc_TkhResit" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Apc_TkhResit" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Apc_NoResit" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Apc_NoResit" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Apc_Amaun" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Apc_Amaun" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Apc_Lwt_TkhResit" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Apc_Lwt_TkhResit" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Apc_Lwt_NoResit" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Apc_Lwt_NoResit" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Apc_Lwt_Amaun" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Apc_Lwt_Amaun" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Id_Amalan" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Id_Amalan" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Log_Id" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Log_Id" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Log_Tkh" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Log_Tkh" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT NoKP, J_Daftar, Apc_No, Apc_Tahun, Apc_Tkh, Apc_TkhResit, Apc_NoResit, Apc_Amaun, Apc_Lwt_TkhResit, Apc_Lwt_NoResit, Apc_Lwt_Amaun, Id_Amalan, Log_Id, Log_Tkh FROM dbo.JT_PENUH_APC</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[JT_PENUH_APC] SET [NoKP] = @NoKP, [J_Daftar] = @J_Daftar, [Apc_No] = @Apc_No, [Apc_Tahun] = @Apc_Tahun, [Apc_Tkh] = @Apc_Tkh, [Apc_TkhResit] = @Apc_TkhResit, [Apc_NoResit] = @Apc_NoResit, [Apc_Amaun] = @Apc_Amaun, [Apc_Lwt_TkhResit] = @Apc_Lwt_TkhResit, [Apc_Lwt_NoResit] = @Apc_Lwt_NoResit, [Apc_Lwt_Amaun] = @Apc_Lwt_Amaun, [Id_Amalan] = @Id_Amalan, [Log_Id] = @Log_Id, [Log_Tkh] = @Log_Tkh WHERE (([NoKP] = @Original_NoKP) AND ([J_Daftar] = @Original_J_Daftar) AND ([Apc_Tahun] = @Original_Apc_Tahun))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@NoKP" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="NoKP" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@J_Daftar" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="J_Daftar" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Apc_No" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Apc_No" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Apc_Tahun" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Apc_Tahun" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Apc_Tkh" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Apc_Tkh" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Apc_TkhResit" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Apc_TkhResit" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Apc_NoResit" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Apc_NoResit" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Apc_Amaun" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Apc_Amaun" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Apc_Lwt_TkhResit" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Apc_Lwt_TkhResit" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Apc_Lwt_NoResit" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Apc_Lwt_NoResit" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Apc_Lwt_Amaun" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Apc_Lwt_Amaun" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Id_Amalan" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Id_Amalan" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Log_Id" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Log_Id" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Log_Tkh" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Log_Tkh" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_NoKP" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="NoKP" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@Original_J_Daftar" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="J_Daftar" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Apc_Tahun" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Apc_Tahun" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="NoKP" DataSetColumn="NoKP" />
              <Mapping SourceColumn="J_Daftar" DataSetColumn="J_Daftar" />
              <Mapping SourceColumn="Apc_No" DataSetColumn="Apc_No" />
              <Mapping SourceColumn="Apc_Tahun" DataSetColumn="Apc_Tahun" />
              <Mapping SourceColumn="Apc_Tkh" DataSetColumn="Apc_Tkh" />
              <Mapping SourceColumn="Apc_TkhResit" DataSetColumn="Apc_TkhResit" />
              <Mapping SourceColumn="Apc_NoResit" DataSetColumn="Apc_NoResit" />
              <Mapping SourceColumn="Apc_Amaun" DataSetColumn="Apc_Amaun" />
              <Mapping SourceColumn="Apc_Lwt_TkhResit" DataSetColumn="Apc_Lwt_TkhResit" />
              <Mapping SourceColumn="Apc_Lwt_NoResit" DataSetColumn="Apc_Lwt_NoResit" />
              <Mapping SourceColumn="Apc_Lwt_Amaun" DataSetColumn="Apc_Lwt_Amaun" />
              <Mapping SourceColumn="Id_Amalan" DataSetColumn="Id_Amalan" />
              <Mapping SourceColumn="Log_Id" DataSetColumn="Log_Id" />
              <Mapping SourceColumn="Log_Tkh" DataSetColumn="Log_Tkh" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="LJMDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_DataSetName="LJMDataSet" msprop:Generator_UserDSName="LJMDataSet" msprop:EnableTableAdapterManager="True">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="JT_PENUH_APC" msprop:Generator_UserTableName="JT_PENUH_APC" msprop:Generator_RowDeletedName="JT_PENUH_APCRowDeleted" msprop:Generator_TableClassName="JT_PENUH_APCDataTable" msprop:Generator_RowChangedName="JT_PENUH_APCRowChanged" msprop:Generator_RowClassName="JT_PENUH_APCRow" msprop:Generator_RowChangingName="JT_PENUH_APCRowChanging" msprop:Generator_RowEvArgName="JT_PENUH_APCRowChangeEvent" msprop:Generator_RowEvHandlerName="JT_PENUH_APCRowChangeEventHandler" msprop:Generator_TablePropName="JT_PENUH_APC" msprop:Generator_TableVarName="tableJT_PENUH_APC" msprop:Generator_RowDeletingName="JT_PENUH_APCRowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="NoKP" msprop:Generator_UserColumnName="NoKP" msprop:Generator_ColumnPropNameInRow="NoKP" msprop:Generator_ColumnVarNameInTable="columnNoKP" msprop:Generator_ColumnPropNameInTable="NoKPColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="14" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="J_Daftar" msprop:Generator_UserColumnName="J_Daftar" msprop:Generator_ColumnPropNameInRow="J_Daftar" msprop:Generator_ColumnVarNameInTable="columnJ_Daftar" msprop:Generator_ColumnPropNameInTable="J_DaftarColumn" type="xs:unsignedByte" />
              <xs:element name="Apc_No" msprop:Generator_UserColumnName="Apc_No" msprop:Generator_ColumnPropNameInRow="Apc_No" msprop:Generator_ColumnVarNameInTable="columnApc_No" msprop:Generator_ColumnPropNameInTable="Apc_NoColumn" type="xs:int" />
              <xs:element name="Apc_Tahun" msprop:Generator_UserColumnName="Apc_Tahun" msprop:Generator_ColumnPropNameInRow="Apc_Tahun" msprop:Generator_ColumnVarNameInTable="columnApc_Tahun" msprop:Generator_ColumnPropNameInTable="Apc_TahunColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Apc_Tkh" msprop:Generator_UserColumnName="Apc_Tkh" msprop:Generator_ColumnPropNameInRow="Apc_Tkh" msprop:Generator_ColumnVarNameInTable="columnApc_Tkh" msprop:Generator_ColumnPropNameInTable="Apc_TkhColumn" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Apc_TkhResit" msprop:Generator_UserColumnName="Apc_TkhResit" msprop:Generator_ColumnPropNameInRow="Apc_TkhResit" msprop:Generator_ColumnVarNameInTable="columnApc_TkhResit" msprop:Generator_ColumnPropNameInTable="Apc_TkhResitColumn" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Apc_NoResit" msprop:Generator_UserColumnName="Apc_NoResit" msprop:Generator_ColumnPropNameInRow="Apc_NoResit" msprop:Generator_ColumnVarNameInTable="columnApc_NoResit" msprop:Generator_ColumnPropNameInTable="Apc_NoResitColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Apc_Amaun" msprop:Generator_UserColumnName="Apc_Amaun" msprop:Generator_ColumnPropNameInRow="Apc_Amaun" msprop:Generator_ColumnVarNameInTable="columnApc_Amaun" msprop:Generator_ColumnPropNameInTable="Apc_AmaunColumn" type="xs:short" minOccurs="0" />
              <xs:element name="Apc_Lwt_TkhResit" msprop:Generator_UserColumnName="Apc_Lwt_TkhResit" msprop:Generator_ColumnPropNameInRow="Apc_Lwt_TkhResit" msprop:Generator_ColumnVarNameInTable="columnApc_Lwt_TkhResit" msprop:Generator_ColumnPropNameInTable="Apc_Lwt_TkhResitColumn" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Apc_Lwt_NoResit" msprop:Generator_UserColumnName="Apc_Lwt_NoResit" msprop:Generator_ColumnPropNameInRow="Apc_Lwt_NoResit" msprop:Generator_ColumnVarNameInTable="columnApc_Lwt_NoResit" msprop:Generator_ColumnPropNameInTable="Apc_Lwt_NoResitColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Apc_Lwt_Amaun" msprop:Generator_UserColumnName="Apc_Lwt_Amaun" msprop:Generator_ColumnPropNameInRow="Apc_Lwt_Amaun" msprop:Generator_ColumnVarNameInTable="columnApc_Lwt_Amaun" msprop:Generator_ColumnPropNameInTable="Apc_Lwt_AmaunColumn" type="xs:short" minOccurs="0" />
              <xs:element name="Id_Amalan" msprop:Generator_UserColumnName="Id_Amalan" msprop:Generator_ColumnPropNameInRow="Id_Amalan" msprop:Generator_ColumnVarNameInTable="columnId_Amalan" msprop:Generator_ColumnPropNameInTable="Id_AmalanColumn" type="xs:int" minOccurs="0" />
              <xs:element name="Log_Id" msprop:Generator_UserColumnName="Log_Id" msprop:Generator_ColumnPropNameInRow="Log_Id" msprop:Generator_ColumnVarNameInTable="columnLog_Id" msprop:Generator_ColumnPropNameInTable="Log_IdColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Log_Tkh" msprop:Generator_UserColumnName="Log_Tkh" msprop:Generator_ColumnPropNameInRow="Log_Tkh" msprop:Generator_ColumnVarNameInTable="columnLog_Tkh" msprop:Generator_ColumnPropNameInTable="Log_TkhColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:JT_PENUH_APC" />
      <xs:field xpath="mstns:NoKP" />
      <xs:field xpath="mstns:J_Daftar" />
      <xs:field xpath="mstns:Apc_Tahun" />
    </xs:unique>
  </xs:element>
</xs:schema>