﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm64
    Inherits System.Web.UI.Page

    Public Sub Cari()
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select * from pn_gelaran_jwtn"
        Tb = "pn_gelaran_jwtn"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("PN", "CPD", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PENYELENGGARAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
        Cari()
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(4).Visible = False
        e.Row.Cells(5).Visible = False
        e.Row.Cells(6).Visible = False
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(0).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.Cells(5).Text
        Dim tx As TextBox
        tx = e.Row.FindControl("Tx_CPD")
        tx.Text = e.Row.Cells(6).Text
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Dim SQL As String, tx As TextBox
        tx = Gd.SelectedRow.FindControl("Tx_CPD")
        If Not IsNumeric(tx.Text) Then Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        SQL = "update pn_gelaran_jwtn set cpd = " & tx.Text & " where id_gelaran = " & Gd.SelectedRow.Cells(4).Text
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Msg(Me, "Rekod Telah Disimpan...")
        Catch ex As Exception
            Msg(Me, "Error...")
        End Try
        Cn.Close()
        Cari()
    End Sub
End Class