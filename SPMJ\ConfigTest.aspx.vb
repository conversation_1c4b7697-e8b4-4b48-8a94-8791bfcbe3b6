Imports System.Configuration
Imports System.Text
Imports System.Web.Configuration

Public Partial Class ConfigTest
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            lblResults.Text = "<div class='info'>Click 'Test Configuration' to check Web.config settings.</div>"
        End If
    End Sub

    Protected Sub btnTest_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnTest.Click
        Dim results As New StringBuilder()
        Dim hasErrors As Boolean = False

        results.AppendLine("=== SPMJ Web.config Configuration Test ===")
        results.AppendLine("Test Date: " & DateTime.Now.ToString())
        results.AppendLine()

        Try
            ' Test 1: Basic Configuration Access
            results.AppendLine("Test 1: Basic Configuration Access")
            Dim config As Configuration = WebConfigurationManager.OpenWebConfiguration("~")
            If config IsNot Nothing Then
                results.AppendLine("✓ PASS - Web.config can be read successfully")
            Else
                results.AppendLine("✗ FAIL - Cannot read Web.config")
                hasErrors = True
            End If
            results.AppendLine()

            ' Test 2: Session State Configuration
            results.AppendLine("Test 2: Session State Configuration")
            Try
                Dim sessionSection As SessionStateSection = CType(config.GetSection("system.web/sessionState"), SessionStateSection)
                If sessionSection IsNot Nothing Then
                    results.AppendLine("✓ PASS - Session state configuration found")
                    results.AppendLine("  Mode: " & sessionSection.Mode.ToString())
                    results.AppendLine("  Timeout: " & sessionSection.Timeout.TotalMinutes.ToString() & " minutes")
                    results.AppendLine("  HttpOnly Cookies: " & sessionSection.HttpOnlyCookies.ToString())
                    results.AppendLine("  Require SSL: " & sessionSection.CookiesRequireSSL.ToString())
                Else
                    results.AppendLine("✗ FAIL - Session state configuration not found")
                    hasErrors = True
                End If
            Catch ex As Exception
                results.AppendLine("✗ FAIL - Session state configuration error: " & ex.Message)
                hasErrors = True
            End Try
            results.AppendLine()

            ' Test 3: HTTP Runtime Configuration
            results.AppendLine("Test 3: HTTP Runtime Configuration")
            Try
                Dim httpRuntimeSection As HttpRuntimeSection = CType(config.GetSection("system.web/httpRuntime"), HttpRuntimeSection)
                If httpRuntimeSection IsNot Nothing Then
                    results.AppendLine("✓ PASS - HTTP runtime configuration found")
                    results.AppendLine("  Max Request Length: " & httpRuntimeSection.MaxRequestLength.ToString() & " KB")
                    results.AppendLine("  Execution Timeout: " & httpRuntimeSection.ExecutionTimeout.TotalSeconds.ToString() & " seconds")
                    results.AppendLine("  Enable Version Header: " & httpRuntimeSection.EnableVersionHeader.ToString())
                Else
                    results.AppendLine("✗ FAIL - HTTP runtime configuration not found")
                    hasErrors = True
                End If
            Catch ex As Exception
                results.AppendLine("✗ FAIL - HTTP runtime configuration error: " & ex.Message)
                hasErrors = True
            End Try
            results.AppendLine()

            ' Test 4: Pages Configuration
            results.AppendLine("Test 4: Pages Configuration")
            Try
                Dim pagesSection As PagesSection = CType(config.GetSection("system.web/pages"), PagesSection)
                If pagesSection IsNot Nothing Then
                    results.AppendLine("✓ PASS - Pages configuration found")
                    results.AppendLine("  Enable ViewState MAC: " & pagesSection.EnableViewStateMac.ToString())
                    results.AppendLine("  Enable Event Validation: " & pagesSection.EnableEventValidation.ToString())
                    results.AppendLine("  Validate Request: " & pagesSection.ValidateRequest.ToString())
                Else
                    results.AppendLine("✗ FAIL - Pages configuration not found")
                    hasErrors = True
                End If
            Catch ex As Exception
                results.AppendLine("✗ FAIL - Pages configuration error: " & ex.Message)
                hasErrors = True
            End Try
            results.AppendLine()

            ' Test 5: Compilation Configuration
            results.AppendLine("Test 5: Compilation Configuration")
            Try
                Dim compilationSection As CompilationSection = CType(config.GetSection("system.web/compilation"), CompilationSection)
                If compilationSection IsNot Nothing Then
                    results.AppendLine("✓ PASS - Compilation configuration found")
                    results.AppendLine("  Debug Mode: " & compilationSection.Debug.ToString())
                    results.AppendLine("  Target Framework: " & compilationSection.TargetFramework)
                Else
                    results.AppendLine("✗ FAIL - Compilation configuration not found")
                    hasErrors = True
                End If
            Catch ex As Exception
                results.AppendLine("✗ FAIL - Compilation configuration error: " & ex.Message)
                hasErrors = True
            End Try
            results.AppendLine()

            ' Test 6: Connection String
            results.AppendLine("Test 6: Connection String Test")
            Try
                Dim connectionString As String = ConfigurationManager.ConnectionStrings("ServerId").ConnectionString
                If Not String.IsNullOrEmpty(connectionString) Then
                    results.AppendLine("✓ PASS - Connection string found")
                    results.AppendLine("  Connection: " & connectionString.Substring(0, Math.Min(50, connectionString.Length)) & "...")
                Else
                    results.AppendLine("✗ FAIL - Connection string not found")
                    hasErrors = True
                End If
            Catch ex As Exception
                results.AppendLine("✗ FAIL - Connection string error: " & ex.Message)
                hasErrors = True
            End Try
            results.AppendLine()

            ' Test 7: .NET Framework Version
            results.AppendLine("Test 7: .NET Framework Version")
            results.AppendLine("  Runtime Version: " & System.Environment.Version.ToString())
            results.AppendLine("  CLR Version: " & System.Runtime.InteropServices.RuntimeEnvironment.GetSystemVersion())
            results.AppendLine()

            ' Summary
            results.AppendLine("=== TEST SUMMARY ===")
            If hasErrors Then
                results.AppendLine("❌ SOME TESTS FAILED - Configuration issues detected")
                lblResults.Text = "<div class='error'><strong>Configuration Issues Detected!</strong> Please review the test results.</div>"
            Else
                results.AppendLine("🎉 ALL TESTS PASSED - Configuration is working correctly")
                lblResults.Text = "<div class='success'><strong>Configuration Test Passed!</strong> Web.config is being read correctly.</div>"
            End If

        Catch ex As Exception
            results.AppendLine("❌ CRITICAL ERROR: " & ex.Message)
            results.AppendLine("Stack Trace: " & ex.StackTrace)
            lblResults.Text = "<div class='error'><strong>Critical Error!</strong> " & ex.Message & "</div>"
        End Try

        txtResults.Text = results.ToString()
    End Sub

End Class
