﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm37
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Tb = "jt_penuh"

        Dim List_Adp As New SqlDataAdapter(X, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Add verify login 06122019 -OSH 
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        'Fix security access 23072018 - OSH 
        If Session("ORIGIN") = "yes" Then

            If IsPostBack Then Exit Sub

            Cb_Kiri.Items.Add("")
            Cb_Kiri.Items.Add("NEGERI")
            Cb_Kiri.Items.Add("MAJIKAN")

            Cb_Atas.Items.Add("")
            Cb_Atas.Items.Add("JAWATAN")
            Cb_Atas.Items.Add("JANTINA")
            Cb_Atas.Items.Add("BANGSA")
            Cb_Atas.Items.Add("SEKTOR")

            'Cb_Tahun.Items.Add(Now.Year + 2)  'Add previous year option 05012021 - OSH
            'Cb_Tahun.Items.Add(Now.Year)
            'Cb_Tahun.Items.Add(Now.Year + 1)

        Else
            Response.Redirect("p0_Login.aspx")
        End If


        'Comment Ori 23072018 -OSH

        'If IsPostBack Then Exit Sub

        'Cb_Kiri.Items.Add("")
        'Cb_Kiri.Items.Add("NEGERI")
        'Cb_Kiri.Items.Add("MAJIKAN")

        'Cb_Atas.Items.Add("")
        'Cb_Atas.Items.Add("JAWATAN")
        'Cb_Atas.Items.Add("JANTINA")
        'Cb_Atas.Items.Add("BANGSA")
        'Cb_Atas.Items.Add("SEKTOR")

        'Cb_Tahun.Items.Add(Now.Year)
        'Cb_Tahun.Items.Add(Now.Year + 1)

    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click

        If Cb_Kiri.SelectedItem.Text = "NEGERI" Then
            If Cb_Atas.SelectedItem.Text = "JAWATAN" Then
                Dim SQL As String

                SQL = "drop table stat_apc; "
                SQL += "select dc_negeri as Negeri, count(j_daftar) as 'JB', 0 as 'JM', 0 as 'PJ' into stat_apc from pn_tpt_amalan pta 	inner join jt_penuh_apc jta on jta.id_amalan=pta.id_amalan and jta.apc_tahun = " & Cb_Tahun.SelectedItem.Text & " and j_daftar=1	inner join pn_negeri pn on pta.negeri=pn.id_negeri group by dc_negeri "
                SQL += "union "
                SQL += "select dc_negeri as Negeri, 0 as 'JB', count(j_daftar) as 'JM', 0 as 'PJ' from pn_tpt_amalan pta inner join jt_penuh_apc jta on jta.id_amalan=pta.id_amalan and jta.apc_tahun = " & Cb_Tahun.SelectedItem.Text & " and j_daftar=2	inner join pn_negeri pn on pta.negeri=pn.id_negeri group by dc_negeri "
                SQL += "union "
                SQL += "select dc_negeri as Negeri, 0 as 'JB', 0 as 'JM', count(j_daftar) as 'PJ' from pn_tpt_amalan pta inner join jt_penuh_apc jta on jta.id_amalan=pta.id_amalan and jta.apc_tahun = " & Cb_Tahun.SelectedItem.Text & " and j_daftar=3	inner join pn_negeri pn on pta.negeri=pn.id_negeri group by dc_negeri"

                Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
                Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Cn.Close()

                SQL = "select distinct(negeri) as 'Negeri', sum(jb) as 'JB', sum(jm) as 'JM', sum(pj) as 'PJ', sum(jb)+sum(jm)+sum(pj) as 'Jumlah' from stat_apc group by negeri"
                Session("Chart_SQL") = SQL
                Session("Chart_X") = "Negeri"
                Session("Chart_Y1") = "JB"
                Session("Chart_Y2") = "JM"
                Session("Chart_Y3") = "PJ"
                Session("Chart_Y4") = "Jumlah"
                Cari(SQL)
            End If
            If Cb_Atas.SelectedItem.Text = "SEKTOR" Then
                Dim SQL As String, X As String = ""

                SQL = "drop table stat_apc; "
                SQL += "select dc_negeri as Negeri, count(j_daftar) as 'Kerajaan', 0 as 'Swasta' into stat_apc from pn_tpt_amalan pta inner join jt_penuh_apc jta on jta.id_amalan=pta.id_amalan and jta.apc_tahun = " & Cb_Tahun.SelectedItem.Text & " and sektor=1 inner join pn_negeri pn on pta.negeri=pn.id_negeri group by dc_negeri "
                SQL += "union "
                SQL += "select dc_negeri as Negeri, 0 as 'Kerajaan', count(j_daftar) as 'Swasta' from pn_tpt_amalan pta inner join jt_penuh_apc jta on jta.id_amalan=pta.id_amalan and jta.apc_tahun = " & Cb_Tahun.SelectedItem.Text & " and sektor=2 inner join pn_negeri pn on pta.negeri=pn.id_negeri group by dc_negeri "

                Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
                Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Cn.Close()

                SQL = "select distinct(negeri) as 'Negeri', sum(Kerajaan) as 'Kerajaan', sum(Swasta) as 'Swasta', sum(Kerajaan)+sum(Swasta) as 'Jumlah' from stat_apc group by negeri"
                Session("Chart_SQL") = SQL
                Session("Chart_X") = "Negeri"
                Session("Chart_Y1") = "Kerajaan"
                Session("Chart_Y2") = "Swasta"
                Session("Chart_Y3") = ""
                Session("Chart_Y4") = "Jumlah"
                Cari(SQL)
            End If
        End If

        If Cb_Kiri.SelectedItem.Text = "MAJIKAN" Then
            If Cb_Atas.SelectedItem.Text = "JAWATAN" Then

                Dim SQL As String
                Dim X As String = ""
                If Cb_Negeri.SelectedIndex > 0 Then X = " and pn.id_negeri=" & Cb_Negeri.SelectedValue & " "

                SQL = "drop table stat_apc; "
                SQL += "select dc_amalan as 'Tempat_Amalan', count(j_daftar) as 'JB', 0 as 'JM', 0 as 'PJ' into stat_apc from pn_tpt_amalan pta 	inner join jt_penuh_apc jta on jta.id_amalan=pta.id_amalan and jta.apc_tahun = " & Cb_Tahun.SelectedItem.Text & " and j_daftar=1 inner join pn_negeri pn on pta.negeri=pn.id_negeri " & X & " group by dc_amalan "
                SQL += "union "
                SQL += "select dc_amalan as 'Tempat_Amalan', 0 as 'JB', count(j_daftar) as 'JM', 0 as 'PJ' from pn_tpt_amalan pta inner join jt_penuh_apc jta on jta.id_amalan=pta.id_amalan and jta.apc_tahun = " & Cb_Tahun.SelectedItem.Text & " and j_daftar=2	inner join pn_negeri pn on pta.negeri=pn.id_negeri " & X & " group by dc_amalan "
                SQL += "union "
                SQL += "select dc_amalan as 'Tempat_Amalan', 0 as 'JB', 0 as 'JM', count(j_daftar) as 'PJ' from pn_tpt_amalan pta inner join jt_penuh_apc jta on jta.id_amalan=pta.id_amalan and jta.apc_tahun = " & Cb_Tahun.SelectedItem.Text & " and j_daftar=3	inner join pn_negeri pn on pta.negeri=pn.id_negeri " & X & " group by dc_amalan"

                Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
                Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Cn.Close()


                SQL = "select distinct(Tempat_Amalan) as 'Tempat Amalan', sum(jb) as 'JB', sum(jm) as 'JM', sum(pj) as 'PJ', sum(jb)+sum(jm)+sum(pj) as 'Jumlah' from stat_apc group by Tempat_Amalan"
                Session("Chart_SQL") = SQL
                Session("Chart_X") = "Tempat Amalan"
                Session("Chart_Y1") = "JB"
                Session("Chart_Y2") = "JM"
                Session("Chart_Y3") = "PJ"
                Session("Chart_Y4") = "Jumlah"
                Cari(SQL)
            End If
        End If


    End Sub

    Protected Sub Cb_Kiri_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kiri.SelectedIndexChanged
        If Cb_Kiri.SelectedItem.Text = "MAJIKAN" Then
            Cb_Negeri.Visible = True
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

            'NEGERI
            Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
            Rdr = Cmd.ExecuteReader()
            Cb_Negeri.Items.Clear()
            Cb_Negeri.Items.Add("(SEMUA NEGERI)")
            Cb_Negeri.Items.Item(Cb_Negeri.Items.Count - 1).Value = ""
            While Rdr.Read
                Cb_Negeri.Items.Add(Rdr(0))
                Cb_Negeri.Items.Item(Cb_Negeri.Items.Count - 1).Value = Rdr(1)
            End While
            Rdr.Close()
            Cn.Close()
        Else
            Cb_Negeri.Visible = False
        End If
    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari2.Click
        'Chart2.Visible = True
        'Chart1.Visible = False
        'Chart2.Series.Item(0).ChartType = DataVisualization.Charting.SeriesChartType.Pie
        'Cn_Chart.SelectCommand = Session("Chart_SQL")
        'Chart2.Series.Item(0).XValueMember = Session("Chart_X")
        'Chart2.Series.Item(0).YValueMembers = Session("Chart_Y1")
        'Chart2.DataBind()

    End Sub

    Protected Sub cmd_Cari3_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari3.Click
        'Chart1.Visible = True
        'Chart2.Visible = False
        'Chart1.Series.Item(0).ChartType = DataVisualization.Charting.SeriesChartType.Column
        'Cn_Chart.SelectCommand = Session("Chart_SQL")
        'Chart1.Series.Item(0).XValueMember = Session("Chart_X")
        'Chart1.Series.Item(0).YValueMembers = Session("Chart_Y1") : Chart1.Series.Item(0).Name = Session("Chart_Y1")
        'If Session("Chart_Y2") = "" Then  Else Chart1.Series.Item(1).YValueMembers = Session("Chart_Y2") : Chart1.Series.Item(1).Name = Session("Chart_Y2")
        'If Session("Chart_Y3") = "" Then  Else Chart1.Series.Item(2).YValueMembers = Session("Chart_Y3") : Chart1.Series.Item(2).Name = Session("Chart_Y3")
        'Chart1.DataBind()
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Button1.Click

        GridViewExportUtil.Export("Laporan.xls", Gd)
    End Sub
End Class