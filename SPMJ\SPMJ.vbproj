﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{8F6F8AC3-C8BE-4285-92D3-AA4BDCDC6AEF}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{F184B08F-C81C-45F6-A57F-5ABD9991F28F}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <RootNamespace>SPMJ</RootNamespace>
    <AssemblyName>SPMJ</AssemblyName>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <OptionExplicit>On</OptionExplicit>
    <OptionCompare>Binary</OptionCompare>
    <OptionStrict>Off</OptionStrict>
    <OptionInfer>On</OptionInfer>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UseIISExpress>false</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\</OutputPath>
    <DocumentationFile>SPMJ.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DocumentationFile>SPMJ.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AjaxControlToolkit, Version=4.1.60919.0, Culture=neutral, PublicKeyToken=28f01b0e84b6d53e, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\AjaxControlToolkit.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp, Version=5.5.10.0, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\itextsharp.dll</HintPath>
    </Reference>
    <!-- Microsoft.ReportViewer.WebForms reference temporarily commented out until proper assembly is available
    <Reference Include="Microsoft.ReportViewer.WebForms, Version=9.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\..\..\WINDOWS\assembly\GAC_MSIL\Microsoft.ReportViewer.WebForms\9.0.0.0__b03f5f7f11d50a3a\Microsoft.ReportViewer.WebForms.dll</HintPath>
    </Reference>
    -->
    <Reference Include="Newtonsoft.Json, Version=3.5.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=104.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <RequiredTargetFramework>3.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.ServiceModel">
      <RequiredTargetFramework>3.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Web.Extensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Web.Mobile" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Collections.Specialized" />
    <Import Include="System.Configuration" />
    <Import Include="System.Text" />
    <Import Include="System.Text.RegularExpressions" />
    <Import Include="System.Web" />
    <Import Include="System.Web.Caching" />
    <Import Include="System.Web.SessionState" />
    <Import Include="System.Web.Security" />
    <Import Include="System.Web.Profile" />
    <Import Include="System.Web.UI" />
    <Import Include="System.Web.UI.WebControls" />
    <Import Include="System.Web.UI.WebControls.WebParts" />
    <Import Include="System.Web.UI.HtmlControls" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="P1_Pelatih_Cari.aspx" />
    <Content Include="P1_Pelatih_Pinda4a.aspx" />
    <Content Include="P3_APC_Lantik_KPSL.aspx" />
    <Content Include="P3_APC_Mesej_J.aspx" />
    <Content Include="P3_APC_Proses_J2.aspx" />
    <Content Include="P4_Penuh_J.aspx" />
    <Content Include="P4_Pre_JTWA.aspx" />
    <Content Include="PN_Tpt_Amalan_J.aspx" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="HeaderPerson.vb" />
    <Compile Include="HeaderRONmonthly.vb" />
    <Compile Include="itsEvents.vb" />
    <Compile Include="LP_STAT_XM_E.aspx.designer.vb">
      <DependentUpon>LP_STAT_XM_E.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_XM_E.aspx.vb">
      <DependentUpon>LP_STAT_XM_E.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_Pelatih_Cari.aspx.designer.vb">
      <DependentUpon>P1_Pelatih_Cari.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_Pelatih_Cari.aspx.vb">
      <DependentUpon>P1_Pelatih_Cari.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_Pelatih_Daftar4.aspx.designer.vb">
      <DependentUpon>P1_Pelatih_Daftar4.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_Pelatih_Daftar4.aspx.vb">
      <DependentUpon>P1_Pelatih_Daftar4.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P1_Pelatih_Daftar5.aspx.designer.vb">
      <DependentUpon>P1_Pelatih_Daftar5.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_Pelatih_Daftar5.aspx.vb">
      <DependentUpon>P1_Pelatih_Daftar5.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda3.aspx.designer.vb">
      <DependentUpon>P1_Pelatih_Pinda3.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda3.aspx.vb">
      <DependentUpon>P1_Pelatih_Pinda3.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LP_TPC_Ulang.aspx.designer.vb">
      <DependentUpon>LP_TPC_Ulang.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_TPC_Ulang.aspx.vb">
      <DependentUpon>LP_TPC_Ulang.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_Pelatih_Daftar3.aspx.designer.vb">
      <DependentUpon>P1_Pelatih_Daftar3.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_Pelatih_Daftar3.aspx.vb">
      <DependentUpon>P1_Pelatih_Daftar3.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda4.aspx.designer.vb">
      <DependentUpon>P1_Pelatih_Pinda4.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda4.aspx.vb">
      <DependentUpon>P1_Pelatih_Pinda4.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda4a.aspx.designer.vb">
      <DependentUpon>P1_Pelatih_Pinda4a.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda4a.aspx.vb">
      <DependentUpon>P1_Pelatih_Pinda4a.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda7.aspx.designer.vb">
      <DependentUpon>P1_Pelatih_Pinda7.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda7.aspx.vb">
      <DependentUpon>P1_Pelatih_Pinda7.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda8.aspx.designer.vb">
      <DependentUpon>P1_Pelatih_Pinda8.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda8.aspx.vb">
      <DependentUpon>P1_Pelatih_Pinda8.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda9.aspx.designer.vb">
      <DependentUpon>P1_Pelatih_Pinda9.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda9.aspx.vb">
      <DependentUpon>P1_Pelatih_Pinda9.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P2_Penuh.aspx.designer.vb">
      <DependentUpon>P2_Penuh.aspx</DependentUpon>
    </Compile>
    <Compile Include="P2_Penuh.aspx.vb">
      <DependentUpon>P2_Penuh.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P2_SN_Penuh2.aspx.designer.vb">
      <DependentUpon>P2_SN_Penuh2.aspx</DependentUpon>
    </Compile>
    <Compile Include="P2_SN_Penuh2.aspx.vb">
      <DependentUpon>P2_SN_Penuh2.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P3_APC_Cari2.aspx.designer.vb">
      <DependentUpon>P3_APC_Cari2.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_APC_Cari2.aspx.vb">
      <DependentUpon>P3_APC_Cari2.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P3_APC_Lantik_KPSL.aspx.designer.vb">
      <DependentUpon>P3_APC_Lantik_KPSL.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_APC_Lantik_KPSL.aspx.vb">
      <DependentUpon>P3_APC_Lantik_KPSL.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P3_APC_Mesej.aspx.designer.vb">
      <DependentUpon>P3_APC_Mesej.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_APC_Mesej.aspx.vb">
      <DependentUpon>P3_APC_Mesej.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P3_APC_Mesej_J.aspx.designer.vb">
      <DependentUpon>P3_APC_Mesej_J.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_APC_Mesej_J.aspx.vb">
      <DependentUpon>P3_APC_Mesej_J.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P3_APC_Proses_J2.aspx.designer.vb">
      <DependentUpon>P3_APC_Proses_J2.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_APC_Proses_J2.aspx.vb">
      <DependentUpon>P3_APC_Proses_J2.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P3_Cetak.aspx.designer.vb">
      <DependentUpon>P3_Cetak.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_Cetak.aspx.vb">
      <DependentUpon>P3_Cetak.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_XM_Mark.aspx.designer.vb">
      <DependentUpon>LP_STAT_XM_Mark.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_XM_Mark.aspx.vb">
      <DependentUpon>LP_STAT_XM_Mark.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_RET_Sebab.aspx.designer.vb">
      <DependentUpon>LP_RET_Sebab.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_RET_Sebab.aspx.vb">
      <DependentUpon>LP_RET_Sebab.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_RET_Negara.aspx.designer.vb">
      <DependentUpon>LP_RET_Negara.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_RET_Negara.aspx.vb">
      <DependentUpon>LP_RET_Negara.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P1_CalonMarkah.aspx.designer.vb">
      <DependentUpon>P1_CalonMarkah.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_CalonMarkah.aspx.vb">
      <DependentUpon>P1_CalonMarkah.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_StatXM.aspx.designer.vb">
      <DependentUpon>P1_StatXM.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_StatXM.aspx.vb">
      <DependentUpon>P1_StatXM.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_CalonTumpang.aspx.designer.vb">
      <DependentUpon>P1_CalonTumpang.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_CalonTumpang.aspx.vb">
      <DependentUpon>P1_CalonTumpang.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_CalonUlang.aspx.designer.vb">
      <DependentUpon>P1_CalonUlang.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_CalonUlang.aspx.vb">
      <DependentUpon>P1_CalonUlang.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_CalonBaru.aspx.designer.vb">
      <DependentUpon>P1_CalonBaru.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_CalonBaru.aspx.vb">
      <DependentUpon>P1_CalonBaru.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P3_RET_Batal.aspx.designer.vb">
      <DependentUpon>P3_RET_Batal.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_RET_Batal.aspx.vb">
      <DependentUpon>P3_RET_Batal.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P3_RET_Batal_Proses.aspx.designer.vb">
      <DependentUpon>P3_RET_Batal_Proses.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_RET_Batal_Proses.aspx.vb">
      <DependentUpon>P3_RET_Batal_Proses.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P3_RET_Proses2.aspx.designer.vb">
      <DependentUpon>P3_RET_Proses2.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_RET_Proses2.aspx.vb">
      <DependentUpon>P3_RET_Proses2.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P3_STAT_RON_YEARS_DETAIL_J.aspx.designer.vb">
      <DependentUpon>P3_STAT_RON_YEARS_DETAIL_J.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_STAT_RON_YEARS_DETAIL_J.aspx.vb">
      <DependentUpon>P3_STAT_RON_YEARS_DETAIL_J.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P3_ST_APC_2.aspx.designer.vb">
      <DependentUpon>P3_ST_APC_2.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_ST_APC_2.aspx.vb">
      <DependentUpon>P3_ST_APC_2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_Penuh_J.aspx.designer.vb">
      <DependentUpon>P4_Penuh_J.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Penuh_J.aspx.vb">
      <DependentUpon>P4_Penuh_J.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P4_Pre_JTWA.aspx.designer.vb">
      <DependentUpon>P4_Pre_JTWA.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Pre_JTWA.aspx.vb">
      <DependentUpon>P4_Pre_JTWA.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PageFooter.vb" />
    <Compile Include="PN_Pengguna2.aspx.designer.vb">
      <DependentUpon>PN_Pengguna2.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Pengguna2.aspx.vb">
      <DependentUpon>PN_Pengguna2.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_Sebab_Pengekalan.aspx.designer.vb">
      <DependentUpon>PN_Sebab_Pengekalan.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Sebab_Pengekalan.aspx.vb">
      <DependentUpon>PN_Sebab_Pengekalan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_XM_Distribution.aspx.designer.vb">
      <DependentUpon>LP_STAT_XM_Distribution.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_XM_Distribution.aspx.vb">
      <DependentUpon>LP_STAT_XM_Distribution.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_XM_Analys.aspx.designer.vb">
      <DependentUpon>LP_STAT_XM_Analys.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_XM_Analys.aspx.vb">
      <DependentUpon>LP_STAT_XM_Analys.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_XM_Ques.aspx.designer.vb">
      <DependentUpon>LP_STAT_XM_Ques.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_XM_Ques.aspx.vb">
      <DependentUpon>LP_STAT_XM_Ques.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_XM_Markah.aspx.designer.vb">
      <DependentUpon>LP_STAT_XM_Markah.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_XM_Markah.aspx.vb">
      <DependentUpon>LP_STAT_XM_Markah.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_XM.aspx.designer.vb">
      <DependentUpon>LP_STAT_XM.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_XM.aspx.vb">
      <DependentUpon>LP_STAT_XM.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_APC_Tempoh.aspx.designer.vb">
      <DependentUpon>LP_STAT_APC_Tempoh.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_APC_Tempoh.aspx.vb">
      <DependentUpon>LP_STAT_APC_Tempoh.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_APC_Gred.aspx.designer.vb">
      <DependentUpon>LP_STAT_APC_Gred.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_APC_Gred.aspx.vb">
      <DependentUpon>LP_STAT_APC_Gred.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_APC_Jawatan.aspx.designer.vb">
      <DependentUpon>LP_STAT_APC_Jawatan.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_APC_Jawatan.aspx.vb">
      <DependentUpon>LP_STAT_APC_Jawatan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_APC_Tajaan.aspx.designer.vb">
      <DependentUpon>LP_STAT_APC_Tajaan.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_APC_Tajaan.aspx.vb">
      <DependentUpon>LP_STAT_APC_Tajaan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_APC_Kekal_Nama.aspx.designer.vb">
      <DependentUpon>LP_STAT_APC_Kekal_Nama.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_APC_Kekal_Nama.aspx.vb">
      <DependentUpon>LP_STAT_APC_Kekal_Nama.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_P2_Sah_Nama.aspx.designer.vb">
      <DependentUpon>LP_P2_Sah_Nama.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_P2_Sah_Nama.aspx.vb">
      <DependentUpon>LP_P2_Sah_Nama.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_TPC_Warga.aspx.designer.vb">
      <DependentUpon>LP_STAT_TPC_Warga.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_TPC_Warga.aspx.vb">
      <DependentUpon>LP_STAT_TPC_Warga.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_TPC_Lulus_IK.aspx.designer.vb">
      <DependentUpon>LP_STAT_TPC_Lulus_IK.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_TPC_Lulus_IK.aspx.vb">
      <DependentUpon>LP_STAT_TPC_Lulus_IK.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_TPC_Lulus_JT.aspx.designer.vb">
      <DependentUpon>LP_STAT_TPC_Lulus_JT.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_TPC_Lulus_JT.aspx.vb">
      <DependentUpon>LP_STAT_TPC_Lulus_JT.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_TPC_Lulus_LE.aspx.designer.vb">
      <DependentUpon>LP_STAT_TPC_Lulus_LE.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_TPC_Lulus_LE.aspx.vb">
      <DependentUpon>LP_STAT_TPC_Lulus_LE.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_TPC_Lulus_PJ.aspx.designer.vb">
      <DependentUpon>LP_STAT_TPC_Lulus_PJ.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_TPC_Lulus_PJ.aspx.vb">
      <DependentUpon>LP_STAT_TPC_Lulus_PJ.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_TPC_Lulus_Warga.aspx.designer.vb">
      <DependentUpon>LP_STAT_TPC_Lulus_Warga.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_TPC_Lulus_Warga.aspx.vb">
      <DependentUpon>LP_STAT_TPC_Lulus_Warga.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_TPC_Negara_Majikan.aspx.designer.vb">
      <DependentUpon>LP_STAT_TPC_Negara_Majikan.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_TPC_Negara_Majikan.aspx.vb">
      <DependentUpon>LP_STAT_TPC_Negara_Majikan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_TPC_Jawatan.aspx.designer.vb">
      <DependentUpon>LP_STAT_TPC_Jawatan.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_TPC_Jawatan.aspx.vb">
      <DependentUpon>LP_STAT_TPC_Jawatan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_TPC_Majikan.aspx.designer.vb">
      <DependentUpon>LP_STAT_TPC_Majikan.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_TPC_Majikan.aspx.vb">
      <DependentUpon>LP_STAT_TPC_Majikan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_TPC_Jantina.aspx.designer.vb">
      <DependentUpon>LP_STAT_TPC_Jantina.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_TPC_Jantina.aspx.vb">
      <DependentUpon>LP_STAT_TPC_Jantina.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_APC_Sektor.aspx.designer.vb">
      <DependentUpon>LP_STAT_APC_Sektor.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_APC_Sektor.aspx.vb">
      <DependentUpon>LP_STAT_APC_Sektor.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_APC_Bangsa.aspx.designer.vb">
      <DependentUpon>LP_STAT_APC_Bangsa.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_APC_Bangsa.aspx.vb">
      <DependentUpon>LP_STAT_APC_Bangsa.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P4_Daftar.aspx.designer.vb">
      <DependentUpon>P4_Daftar.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Daftar.aspx.vb">
      <DependentUpon>P4_Daftar.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PN_Nota_Kolej.aspx.designer.vb">
      <DependentUpon>PN_Nota_Kolej.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Nota_Kolej.aspx.vb">
      <DependentUpon>PN_Nota_Kolej.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PN_Pengguna_Kolej.aspx.designer.vb">
      <DependentUpon>PN_Pengguna_Kolej.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Pengguna_Kolej.aspx.vb">
      <DependentUpon>PN_Pengguna_Kolej.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P3_Lpr_APC.aspx.designer.vb">
      <DependentUpon>P3_Lpr_APC.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_Lpr_APC.aspx.vb">
      <DependentUpon>P3_Lpr_APC.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_Siri2.aspx.designer.vb">
      <DependentUpon>PN_Siri2.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Siri2.aspx.vb">
      <DependentUpon>PN_Siri2.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_Tpt_Amalan_J.aspx.designer.vb">
      <DependentUpon>PN_Tpt_Amalan_J.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Tpt_Amalan_J.aspx.vb">
      <DependentUpon>PN_Tpt_Amalan_J.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="RP_Calon_Tumpang.aspx.designer.vb">
      <DependentUpon>RP_Calon_Tumpang.aspx</DependentUpon>
    </Compile>
    <Compile Include="RP_Calon_Tumpang.aspx.vb">
      <DependentUpon>RP_Calon_Tumpang.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="RP_Calon_Ulangan.aspx.designer.vb">
      <DependentUpon>RP_Calon_Ulangan.aspx</DependentUpon>
    </Compile>
    <Compile Include="RP_Calon_Ulangan.aspx.vb">
      <DependentUpon>RP_Calon_Ulangan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="RP_STAT_EXAM_Gagal.aspx.designer.vb">
      <DependentUpon>RP_STAT_EXAM_Gagal.aspx</DependentUpon>
    </Compile>
    <Compile Include="RP_STAT_EXAM_Gagal.aspx.vb">
      <DependentUpon>RP_STAT_EXAM_Gagal.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="RP_STAT_EXAM_Lulus.aspx.designer.vb">
      <DependentUpon>RP_STAT_EXAM_Lulus.aspx</DependentUpon>
    </Compile>
    <Compile Include="RP_STAT_EXAM_Lulus.aspx.vb">
      <DependentUpon>RP_STAT_EXAM_Lulus.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="RP_STAT_EXAM_Markah_R.aspx.designer.vb">
      <DependentUpon>RP_STAT_EXAM_Markah_R.aspx</DependentUpon>
    </Compile>
    <Compile Include="RP_STAT_EXAM_Markah_R.aspx.vb">
      <DependentUpon>RP_STAT_EXAM_Markah_R.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="RP_STAT_EXAM_Markah_T.aspx.designer.vb">
      <DependentUpon>RP_STAT_EXAM_Markah_T.aspx</DependentUpon>
    </Compile>
    <Compile Include="RP_STAT_EXAM_Markah_T.aspx.vb">
      <DependentUpon>RP_STAT_EXAM_Markah_T.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P3_STAT_RON_CATEGEOTY_J.aspx.designer.vb">
      <DependentUpon>P3_STAT_RON_CATEGEOTY_J.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_STAT_RON_CATEGEOTY_J.aspx.vb">
      <DependentUpon>P3_STAT_RON_CATEGEOTY_J.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Service References\MyCPD2_SVC\Reference.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Settings.vb" />
    <Compile Include="SPMJ_XLS.vb" />
    <Compile Include="LJMDataSet.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LJMDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Main.Master.designer.vb">
      <DependentUpon>Main.Master</DependentUpon>
    </Compile>
    <Compile Include="Main.Master.vb">
      <DependentUpon>Main.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SPMJ_Mod.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="P1_Pelatih_Daftar.aspx.designer.vb">
      <DependentUpon>P1_Pelatih_Daftar.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_Pelatih_Daftar.aspx.vb">
      <DependentUpon>P1_Pelatih_Daftar.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Blank.aspx.designer.vb">
      <DependentUpon>Blank.aspx</DependentUpon>
    </Compile>
    <Compile Include="Blank.aspx.vb">
      <DependentUpon>Blank.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UserCPD.vb" />
    <Compile Include="Vw_Pelatih.aspx.designer.vb">
      <DependentUpon>Vw_Pelatih.aspx</DependentUpon>
    </Compile>
    <Compile Include="Vw_Pelatih.aspx.vb">
      <DependentUpon>Vw_Pelatih.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda.aspx.designer.vb">
      <DependentUpon>P1_Pelatih_Pinda.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_Pelatih_Pinda.aspx.vb">
      <DependentUpon>P1_Pelatih_Pinda.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_Pelatih_Saring.aspx.designer.vb">
      <DependentUpon>P1_Pelatih_Saring.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_Pelatih_Saring.aspx.vb">
      <DependentUpon>P1_Pelatih_Saring.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_XM_JanaCalon.aspx.designer.vb">
      <DependentUpon>P1_XM_JanaCalon.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_XM_JanaCalon.aspx.vb">
      <DependentUpon>P1_XM_JanaCalon.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_XM_SemakCalon.aspx.designer.vb">
      <DependentUpon>P1_XM_SemakCalon.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_XM_SemakCalon.aspx.vb">
      <DependentUpon>P1_XM_SemakCalon.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_XM_Markah.aspx.designer.vb">
      <DependentUpon>P1_XM_Markah.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_XM_Markah.aspx.vb">
      <DependentUpon>P1_XM_Markah.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_XM_JanaKeputusan.aspx.designer.vb">
      <DependentUpon>P1_XM_JanaKeputusan.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_XM_JanaKeputusan.aspx.vb">
      <DependentUpon>P1_XM_JanaKeputusan.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_XM_SahKeputusan.aspx.designer.vb">
      <DependentUpon>P1_XM_SahKeputusan.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_XM_SahKeputusan.aspx.vb">
      <DependentUpon>P1_XM_SahKeputusan.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P1_XM_Markah_Sah.aspx.designer.vb">
      <DependentUpon>P1_XM_Markah_Sah.aspx</DependentUpon>
    </Compile>
    <Compile Include="P1_XM_Markah_Sah.aspx.vb">
      <DependentUpon>P1_XM_Markah_Sah.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P2_Daftar.aspx.designer.vb">
      <DependentUpon>P2_Daftar.aspx</DependentUpon>
    </Compile>
    <Compile Include="P2_Daftar.aspx.vb">
      <DependentUpon>P2_Daftar.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P2_Semak.aspx.designer.vb">
      <DependentUpon>P2_Semak.aspx</DependentUpon>
    </Compile>
    <Compile Include="P2_Semak.aspx.vb">
      <DependentUpon>P2_Semak.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P2_Daftar_Sah.aspx.designer.vb">
      <DependentUpon>P2_Daftar_Sah.aspx</DependentUpon>
    </Compile>
    <Compile Include="P2_Daftar_Sah.aspx.vb">
      <DependentUpon>P2_Daftar_Sah.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P3_RET_Cari.aspx.designer.vb">
      <DependentUpon>P3_RET_Cari.aspx</DependentUpon>
    </Compile>
    <Compile Include="P3_RET_Cari.aspx.vb">
      <DependentUpon>P3_RET_Cari.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_Semak.aspx.designer.vb">
      <DependentUpon>P4_Semak.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Semak.aspx.vb">
      <DependentUpon>P4_Semak.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_Daftar_Sah.aspx.designer.vb">
      <DependentUpon>P4_Daftar_Sah.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Daftar_Sah.aspx.vb">
      <DependentUpon>P4_Daftar_Sah.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_Penuh.aspx.designer.vb">
      <DependentUpon>P4_Penuh.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Penuh.aspx.vb">
      <DependentUpon>P4_Penuh.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_TPC_Cari.aspx.designer.vb">
      <DependentUpon>P4_TPC_Cari.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_TPC_Cari.aspx.vb">
      <DependentUpon>P4_TPC_Cari.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_TPC_Proses.aspx.designer.vb">
      <DependentUpon>P4_TPC_Proses.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_TPC_Proses.aspx.vb">
      <DependentUpon>P4_TPC_Proses.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P5_Proses.aspx.designer.vb">
      <DependentUpon>P5_Proses.aspx</DependentUpon>
    </Compile>
    <Compile Include="P5_Proses.aspx.vb">
      <DependentUpon>P5_Proses.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P5_Cari.aspx.designer.vb">
      <DependentUpon>P5_Cari.aspx</DependentUpon>
    </Compile>
    <Compile Include="P5_Cari.aspx.vb">
      <DependentUpon>P5_Cari.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="p0_Login.aspx.designer.vb">
      <DependentUpon>p0_Login.aspx</DependentUpon>
    </Compile>
    <Compile Include="p0_Login.aspx.vb">
      <DependentUpon>p0_Login.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="p0_Mesej.aspx.designer.vb">
      <DependentUpon>p0_Mesej.aspx</DependentUpon>
    </Compile>
    <Compile Include="p0_Mesej.aspx.vb">
      <DependentUpon>p0_Mesej.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="M_Senarai.aspx.designer.vb">
      <DependentUpon>M_Senarai.aspx</DependentUpon>
    </Compile>
    <Compile Include="M_Senarai.aspx.vb">
      <DependentUpon>M_Senarai.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PN_NoKP.aspx.designer.vb">
      <DependentUpon>PN_NoKP.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_NoKP.aspx.vb">
      <DependentUpon>PN_NoKP.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LT_APC.aspx.designer.vb">
      <DependentUpon>LT_APC.aspx</DependentUpon>
    </Compile>
    <Compile Include="LT_APC.aspx.vb">
      <DependentUpon>LT_APC.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LN_APC.aspx.designer.vb">
      <DependentUpon>LN_APC.aspx</DependentUpon>
    </Compile>
    <Compile Include="LN_APC.aspx.vb">
      <DependentUpon>LN_APC.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PN_Siri.aspx.designer.vb">
      <DependentUpon>PN_Siri.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Siri.aspx.vb">
      <DependentUpon>PN_Siri.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LP_XM.aspx.designer.vb">
      <DependentUpon>LP_XM.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_XM.aspx.vb">
      <DependentUpon>LP_XM.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LP_Daftar.aspx.designer.vb">
      <DependentUpon>LP_Daftar.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_Daftar.aspx.vb">
      <DependentUpon>LP_Daftar.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LP_APC.aspx.designer.vb">
      <DependentUpon>LP_APC.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_APC.aspx.vb">
      <DependentUpon>LP_APC.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="LP_TPC.aspx.designer.vb">
      <DependentUpon>LP_TPC.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_TPC.aspx.vb">
      <DependentUpon>LP_TPC.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P5_Cetak.aspx.designer.vb">
      <DependentUpon>P5_Cetak.aspx</DependentUpon>
    </Compile>
    <Compile Include="P5_Cetak.aspx.vb">
      <DependentUpon>P5_Cetak.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_Cetak.aspx.designer.vb">
      <DependentUpon>P4_Cetak.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Cetak.aspx.vb">
      <DependentUpon>P4_Cetak.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PN_Pengguna.aspx.designer.vb">
      <DependentUpon>PN_Pengguna.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Pengguna.aspx.vb">
      <DependentUpon>PN_Pengguna.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PN_Pwd.aspx.designer.vb">
      <DependentUpon>PN_Pwd.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Pwd.aspx.vb">
      <DependentUpon>PN_Pwd.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_Cetak2.aspx.designer.vb">
      <DependentUpon>P4_Cetak2.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Cetak2.aspx.vb">
      <DependentUpon>P4_Cetak2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_Cetak3.aspx.designer.vb">
      <DependentUpon>P4_Cetak3.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Cetak3.aspx.vb">
      <DependentUpon>P4_Cetak3.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_Cetak4.aspx.designer.vb">
      <DependentUpon>P4_Cetak4.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Cetak4.aspx.vb">
      <DependentUpon>P4_Cetak4.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_Cetak_Akuan.aspx.designer.vb">
      <DependentUpon>P4_Cetak_Akuan.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Cetak_Akuan.aspx.vb">
      <DependentUpon>P4_Cetak_Akuan.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_Cetak_Akuan2.aspx.designer.vb">
      <DependentUpon>P4_Cetak_Akuan2.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Cetak_Akuan2.aspx.vb">
      <DependentUpon>P4_Cetak_Akuan2.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_Cetak5.aspx.designer.vb">
      <DependentUpon>P4_Cetak5.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Cetak5.aspx.vb">
      <DependentUpon>P4_Cetak5.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="P4_Cetak_TPC.aspx.designer.vb">
      <DependentUpon>P4_Cetak_TPC.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Cetak_TPC.aspx.vb">
      <DependentUpon>P4_Cetak_TPC.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="PN_Negara.aspx.designer.vb">
      <DependentUpon>PN_Negara.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Negara.aspx.vb">
      <DependentUpon>PN_Negara.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_NoPd.aspx.designer.vb">
      <DependentUpon>PN_NoPd.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_NoPd.aspx.vb">
      <DependentUpon>PN_NoPd.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_Kolej.aspx.designer.vb">
      <DependentUpon>PN_Kolej.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Kolej.aspx.vb">
      <DependentUpon>PN_Kolej.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_Tajaan.aspx.designer.vb">
      <DependentUpon>PN_Tajaan.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Tajaan.aspx.vb">
      <DependentUpon>PN_Tajaan.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_Etnik.aspx.designer.vb">
      <DependentUpon>PN_Etnik.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Etnik.aspx.vb">
      <DependentUpon>PN_Etnik.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_NoAPC.aspx.designer.vb">
      <DependentUpon>PN_NoAPC.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_NoAPC.aspx.vb">
      <DependentUpon>PN_NoAPC.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_Akademik.aspx.designer.vb">
      <DependentUpon>PN_Akademik.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Akademik.aspx.vb">
      <DependentUpon>PN_Akademik.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_Ikhtisas.aspx.designer.vb">
      <DependentUpon>PN_Ikhtisas.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Ikhtisas.aspx.vb">
      <DependentUpon>PN_Ikhtisas.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_Disiplin.aspx.designer.vb">
      <DependentUpon>PN_Disiplin.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Disiplin.aspx.vb">
      <DependentUpon>PN_Disiplin.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_Subjek.aspx.designer.vb">
      <DependentUpon>PN_Subjek.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Subjek.aspx.vb">
      <DependentUpon>PN_Subjek.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_Gred.aspx.designer.vb">
      <DependentUpon>PN_Gred.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_Gred.aspx.vb">
      <DependentUpon>PN_Gred.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="PN_CPD.aspx.designer.vb">
      <DependentUpon>PN_CPD.aspx</DependentUpon>
    </Compile>
    <Compile Include="PN_CPD.aspx.vb">
      <DependentUpon>PN_CPD.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P2_Foto.aspx.designer.vb">
      <DependentUpon>P2_Foto.aspx</DependentUpon>
    </Compile>
    <Compile Include="P2_Foto.aspx.vb">
      <DependentUpon>P2_Foto.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="P4_Foto.aspx.designer.vb">
      <DependentUpon>P4_Foto.aspx</DependentUpon>
    </Compile>
    <Compile Include="P4_Foto.aspx.vb">
      <DependentUpon>P4_Foto.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="LP_STAT_APC_Jantina.aspx.designer.vb">
      <DependentUpon>LP_STAT_APC_Jantina.aspx</DependentUpon>
    </Compile>
    <Compile Include="LP_STAT_APC_Jantina.aspx.vb">
      <DependentUpon>LP_STAT_APC_Jantina.aspx</DependentUpon>
      <SubType>ASPXCodebehind</SubType>
    </Compile>
    <Compile Include="Word.aspx.designer.vb">
      <DependentUpon>Word.aspx</DependentUpon>
    </Compile>
    <Compile Include="Word.aspx.vb">
      <DependentUpon>Word.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SecurityUtility.vb" />
    <Compile Include="PasswordMigration.aspx.designer.vb">
      <DependentUpon>PasswordMigration.aspx</DependentUpon>
    </Compile>
    <Compile Include="PasswordMigration.aspx.vb">
      <DependentUpon>PasswordMigration.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="SecurityTest.aspx.designer.vb">
      <DependentUpon>SecurityTest.aspx</DependentUpon>
    </Compile>
    <Compile Include="SecurityTest.aspx.vb">
      <DependentUpon>SecurityTest.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ConfigTest.aspx.designer.vb">
      <DependentUpon>ConfigTest.aspx</DependentUpon>
    </Compile>
    <Compile Include="ConfigTest.aspx.vb">
      <DependentUpon>ConfigTest.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="css\Layout.css" />
    <Content Include="LP_STAT_XM_E.aspx" />
    <Content Include="Image\Border2.gif" />
    <Content Include="Image\Border200.gif" />
    <Content Include="Image\Border230.gif" />
    <Content Include="Image\Border250.gif" />
    <Content Include="Image\Border280.gif" />
    <Content Include="Image\Border300.gif" />
    <Content Include="Image\Border400.gif" />
    <Content Include="Image\Border550.gif" />
    <Content Include="Image\Border600.gif" />
    <Content Include="Image\Border650.gif" />
    <Content Include="P1_Pelatih_Daftar4.aspx" />
    <Content Include="P1_Pelatih_Daftar5.aspx" />
    <Content Include="P1_Pelatih_Pinda3.aspx" />
    <Content Include="LP_TPC_Ulang.aspx" />
    <Content Include="P1_Pelatih_Daftar3.aspx" />
    <Content Include="P1_Pelatih_Pinda4.aspx" />
    <Content Include="P1_Pelatih_Pinda7.aspx" />
    <Content Include="P1_Pelatih_Pinda8.aspx" />
    <Content Include="P1_Pelatih_Pinda9.aspx" />
    <Content Include="P2_Penuh.aspx" />
    <Content Include="P2_SN_Penuh2.aspx" />
    <Content Include="P3_APC_Cari2.aspx" />
    <Content Include="P3_APC_Mesej.aspx" />
    <Content Include="P3_Cetak.aspx" />
    <Content Include="LP_STAT_XM_Mark.aspx" />
    <Content Include="LP_RET_Sebab.aspx" />
    <Content Include="LP_RET_Negara.aspx" />
    <Content Include="P1_CalonMarkah.aspx" />
    <Content Include="P1_StatXM.aspx" />
    <Content Include="P1_CalonTumpang.aspx" />
    <Content Include="P1_CalonUlang.aspx" />
    <Content Include="P1_CalonBaru.aspx" />
    <Content Include="P3_RET_Batal.aspx" />
    <Content Include="P3_RET_Batal_Proses.aspx" />
    <Content Include="P3_RET_Proses2.aspx" />
    <Content Include="P3_STAT_RON_YEARS_DETAIL_J.aspx" />
    <Content Include="P3_ST_APC_2.aspx" />
    <Content Include="PN_Pengguna2.aspx" />
    <Content Include="PN_Sebab_Pengekalan.aspx" />
    <Content Include="LP_STAT_XM_Distribution.aspx" />
    <Content Include="LP_STAT_XM_Analys.aspx" />
    <Content Include="LP_STAT_XM_Ques.aspx" />
    <Content Include="LP_STAT_XM_Markah.aspx" />
    <Content Include="LP_STAT_XM.aspx" />
    <Content Include="LP_STAT_APC_Tempoh.aspx" />
    <Content Include="LP_STAT_APC_Gred.aspx" />
    <Content Include="LP_STAT_APC_Jawatan.aspx" />
    <Content Include="LP_STAT_APC_Tajaan.aspx" />
    <Content Include="LP_STAT_APC_Kekal_Nama.aspx" />
    <Content Include="LP_P2_Sah_Nama.aspx" />
    <Content Include="LP_STAT_TPC_Warga.aspx" />
    <Content Include="LP_STAT_TPC_Lulus_IK.aspx" />
    <Content Include="LP_STAT_TPC_Lulus_JT.aspx" />
    <Content Include="LP_STAT_TPC_Lulus_LE.aspx" />
    <Content Include="LP_STAT_TPC_Lulus_PJ.aspx" />
    <Content Include="LP_STAT_TPC_Lulus_Warga.aspx" />
    <Content Include="LP_STAT_TPC_Negara_Majikan.aspx" />
    <Content Include="LP_STAT_TPC_Jawatan.aspx" />
    <Content Include="LP_STAT_TPC_Majikan.aspx" />
    <Content Include="LP_STAT_TPC_Jantina.aspx" />
    <Content Include="LP_STAT_APC_Sektor.aspx" />
    <Content Include="LP_STAT_APC_Bangsa.aspx" />
    <Content Include="P4_Daftar.aspx" />
    <Content Include="PN_Nota_Kolej.aspx" />
    <Content Include="PN_Pengguna_Kolej.aspx" />
    <Content Include="Image\Bg_Arrow.gif" />
    <Content Include="Image\Bg_Atas.gif" />
    <Content Include="Image\Bg_Dot.gif" />
    <Content Include="Image\Bg_Dot2.gif" />
    <Content Include="Image\Bg_No.gif" />
    <Content Include="Image\Bg_No2.gif" />
    <Content Include="Image\Bg_Sgt.gif" />
    <Content Include="Image\Bg_Trans.gif" />
    <Content Include="Image\Border.gif" />
    <Content Include="Image\Hd_Top.gif" />
    <Content Include="Image\LJM-Bg.gif" />
    <Content Include="Image\Load.gif" />
    <Content Include="P3_Lpr_APC.aspx" />
    <Content Include="Main.Master" />
    <Content Include="P1_Pelatih_Daftar.aspx" />
    <Content Include="Blank.aspx" />
    <None Include="My Project\DataSources\System.Data.DataSet.datasource" />
    <Content Include="PN_Siri2.aspx" />
    <Content Include="RP_Calon_Tumpang.aspx" />
    <Content Include="RP_Calon_Ulangan.aspx" />
    <Content Include="RP_STAT_EXAM_Gagal.aspx" />
    <Content Include="RP_STAT_EXAM_Lulus.aspx" />
    <Content Include="RP_STAT_EXAM_Markah_R.aspx" />
    <Content Include="RP_STAT_EXAM_Markah_T.aspx" />
    <Content Include="P3_STAT_RON_CATEGEOTY_J.aspx" />
    <Content Include="scripts\jquery-ui.js" />
    <Content Include="scripts\jquery-ui.min.js" />
    <None Include="My Project\PublishProfiles\FolderProfile.pubxml" />
    <None Include="Service References\MyCPD2_SVC\configuration.svcinfo" />
    <None Include="Service References\MyCPD2_SVC\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.vb</LastGenOutput>
    </None>
    <Content Include="SPMJ.css" />
    <Content Include="styles\jquery-ui.css" />
    <Content Include="Surat\bidan.jpg" />
    <Content Include="Surat\jata.png" />
    <Content Include="Surat\ljm2.gif" />
    <Content Include="Surat\imga.png" />
    <Content Include="Surat\imgb.png" />
    <Content Include="Surat\imgc.gif" />
    <Content Include="Surat\imgd.png" />
    <Content Include="Surat\jata.jpg" />
    <Content Include="Surat\ljm.gif" />
    <Content Include="Vw_Pelatih.aspx" />
    <Content Include="P1_Pelatih_Pinda.aspx" />
    <Content Include="P1_Pelatih_Saring.aspx" />
    <Content Include="P1_XM_JanaCalon.aspx" />
    <Content Include="P1_XM_SemakCalon.aspx" />
    <Content Include="P1_XM_Markah.aspx" />
    <Content Include="P1_XM_JanaKeputusan.aspx" />
    <Content Include="P1_XM_SahKeputusan.aspx" />
    <Content Include="P1_XM_Markah_Sah.aspx" />
    <Content Include="P2_Daftar.aspx" />
    <Content Include="P2_Semak.aspx" />
    <Content Include="P2_Daftar_Sah.aspx" />
    <Content Include="P3_RET_Cari.aspx" />
    <Content Include="P4_Semak.aspx" />
    <Content Include="P4_Daftar_Sah.aspx" />
    <Content Include="P4_Penuh.aspx" />
    <Content Include="P4_TPC_Cari.aspx" />
    <Content Include="P4_TPC_Proses.aspx" />
    <Content Include="P5_Proses.aspx" />
    <Content Include="P5_Cari.aspx" />
    <Content Include="p0_Login.aspx" />
    <Content Include="p0_Mesej.aspx" />
    <Content Include="PasswordMigration.aspx" />
    <Content Include="SecurityTest.aspx" />
    <Content Include="ConfigTest.aspx" />
    <Content Include="SECURITY_IMPROVEMENTS.md" />
    <Content Include="COOKIE_SECURITY_GUIDE.md" />
    <Content Include="DEPLOYMENT_GUIDE.md" />
    <None Include="LJMDataSet.xsc">
      <DependentUpon>LJMDataSet.xsd</DependentUpon>
    </None>
    <None Include="LJMDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>LJMDataSet.Designer.vb</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="LJMDataSet.xss">
      <DependentUpon>LJMDataSet.xsd</DependentUpon>
    </None>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="Service References\MyCPD2_SVC\Service1.wsdl" />
    <None Include="Service References\MyCPD2_SVC\Service1.xsd" />
    <None Include="Service References\MyCPD2_SVC\Service11.xsd" />
    <None Include="Service References\MyCPD2_SVC\Service12.xsd" />
    <None Include="Service References\MyCPD2_SVC\Service13.xsd" />
    <Content Include="M_Senarai.aspx" />
    <Content Include="PN_NoKP.aspx" />
    <Content Include="LT_APC.aspx" />
    <Content Include="LN_APC.aspx" />
    <Content Include="PN_Siri.aspx" />
    <Content Include="LP_XM.aspx" />
    <Content Include="LP_Daftar.aspx" />
    <Content Include="LP_APC.aspx">
    </Content>
    <Content Include="LP_TPC.aspx" />
    <Content Include="P5_Cetak.aspx" />
    <Content Include="P4_Cetak.aspx" />
    <Content Include="PN_Pengguna.aspx" />
    <Content Include="PN_Pwd.aspx" />
    <Content Include="P4_Cetak2.aspx" />
    <Content Include="P4_Cetak3.aspx" />
    <Content Include="P4_Cetak4.aspx" />
    <Content Include="P4_Cetak_Akuan.aspx" />
    <Content Include="P4_Cetak_Akuan2.aspx" />
    <Content Include="P4_Cetak5.aspx" />
    <Content Include="P4_Cetak_TPC.aspx" />
    <Content Include="PN_Negara.aspx" />
    <Content Include="PN_NoPd.aspx" />
    <Content Include="PN_Kolej.aspx" />
    <Content Include="PN_Tajaan.aspx" />
    <Content Include="PN_Etnik.aspx" />
    <Content Include="PN_NoAPC.aspx" />
    <Content Include="PN_Akademik.aspx" />
    <Content Include="PN_Ikhtisas.aspx" />
    <Content Include="PN_Disiplin.aspx" />
    <Content Include="PN_Subjek.aspx" />
    <Content Include="PN_Gred.aspx" />
    <Content Include="PN_CPD.aspx" />
    <Content Include="P2_Foto.aspx" />
    <Content Include="P4_Foto.aspx" />
    <Content Include="LP_STAT_APC_Jantina.aspx" />
    <Content Include="Word.aspx" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{967B4E0D-AD0C-4609-AB67-0FA40C0206D8}" />
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Service References\MyCPD2_SVC\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Rpt\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.VisualBasic.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>52848</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>
          </IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>