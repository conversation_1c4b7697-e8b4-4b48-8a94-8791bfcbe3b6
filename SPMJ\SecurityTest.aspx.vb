Imports System.Text

Public Partial Class SecurityTest
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Not IsPostBack Then
            lblResults.Text = "<div class='test-result info'>Click 'Run Security Tests' to test the security implementation.</div>"
        End If
    End Sub

    Protected Sub btnTestSecurity_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnTestSecurity.Click
        Dim results As New StringBuilder()
        Dim testsPassed As Integer = 0
        Dim totalTests As Integer = 0

        results.AppendLine("=== SPMJ Security Implementation Test Results ===")
        results.AppendLine("Test Date: " & DateTime.Now.ToString())
        results.AppendLine()

        ' Test 1: Password Hashing
        totalTests += 1
        results.AppendLine("Test 1: Password Hashing Function")
        Try
            Dim testPassword As String = "TestPassword123"
            Dim hashedPassword As String = HashPassword(testPassword)
            
            If hashedPassword.Length = 64 AndAlso hashedPassword <> testPassword Then
                results.AppendLine("✓ PASS - Password hashing works correctly")
                results.AppendLine("  Original: " & testPassword)
                results.AppendLine("  Hashed: " & hashedPassword.Substring(0, 20) & "...")
                testsPassed += 1
            Else
                results.AppendLine("✗ FAIL - Password hashing failed")
            End If
        Catch ex As Exception
            results.AppendLine("✗ FAIL - Password hashing error: " & ex.Message)
        End Try
        results.AppendLine()

        ' Test 2: Password Verification
        totalTests += 1
        results.AppendLine("Test 2: Password Verification Function")
        Try
            Dim testPassword As String = "TestPassword123"
            Dim hashedPassword As String = HashPassword(testPassword)
            Dim isValid As Boolean = VerifyPassword(testPassword, hashedPassword)
            Dim isInvalid As Boolean = VerifyPassword("WrongPassword", hashedPassword)
            
            If isValid AndAlso Not isInvalid Then
                results.AppendLine("✓ PASS - Password verification works correctly")
                results.AppendLine("  Correct password verified: " & isValid.ToString())
                results.AppendLine("  Wrong password rejected: " & (Not isInvalid).ToString())
                testsPassed += 1
            Else
                results.AppendLine("✗ FAIL - Password verification failed")
            End If
        Catch ex As Exception
            results.AppendLine("✗ FAIL - Password verification error: " & ex.Message)
        End Try
        results.AppendLine()

        ' Test 3: Password Strength Validation
        totalTests += 1
        results.AppendLine("Test 3: Password Strength Validation")
        Try
            Dim strongPassword As Boolean = ValidatePasswordStrength("StrongPass123")
            Dim weakPassword1 As Boolean = ValidatePasswordStrength("weak")
            Dim weakPassword2 As Boolean = ValidatePasswordStrength("nodigits")
            Dim weakPassword3 As Boolean = ValidatePasswordStrength("NOLOWERCASE123")
            
            If strongPassword AndAlso Not weakPassword1 AndAlso Not weakPassword2 AndAlso Not weakPassword3 Then
                results.AppendLine("✓ PASS - Password strength validation works correctly")
                results.AppendLine("  Strong password accepted: " & strongPassword.ToString())
                results.AppendLine("  Weak passwords rejected: " & (Not weakPassword1).ToString())
                testsPassed += 1
            Else
                results.AppendLine("✗ FAIL - Password strength validation failed")
            End If
        Catch ex As Exception
            results.AppendLine("✗ FAIL - Password strength validation error: " & ex.Message)
        End Try
        results.AppendLine()

        ' Test 4: Input Sanitization
        totalTests += 1
        results.AppendLine("Test 4: Input Sanitization Function")
        Try
            Dim maliciousInput As String = "<script>alert('xss')</script>"
            Dim sanitizedInput As String = SanitizeInput(maliciousInput)
            
            If Not sanitizedInput.Contains("<script>") AndAlso sanitizedInput.Contains("&lt;") Then
                results.AppendLine("✓ PASS - Input sanitization works correctly")
                results.AppendLine("  Original: " & maliciousInput)
                results.AppendLine("  Sanitized: " & sanitizedInput)
                testsPassed += 1
            Else
                results.AppendLine("✗ FAIL - Input sanitization failed")
            End If
        Catch ex As Exception
            results.AppendLine("✗ FAIL - Input sanitization error: " & ex.Message)
        End Try
        results.AppendLine()

        ' Test 5: SQL Injection Protection
        totalTests += 1
        results.AppendLine("Test 5: SQL Injection Protection")
        Try
            Dim sqlInjection1 As Boolean = Chk_SQL("admin' OR '1'='1")
            Dim sqlInjection2 As Boolean = Chk_SQL("user; DROP TABLE users;")
            Dim sqlInjection3 As Boolean = Chk_SQL("UNION SELECT * FROM passwords")
            Dim validInput As Boolean = Chk_SQL("normaluser")
            
            If sqlInjection1 AndAlso sqlInjection2 AndAlso sqlInjection3 AndAlso Not validInput Then
                results.AppendLine("✓ PASS - SQL injection protection works correctly")
                results.AppendLine("  Malicious inputs blocked: " & (sqlInjection1 AndAlso sqlInjection2 AndAlso sqlInjection3).ToString())
                results.AppendLine("  Valid input allowed: " & (Not validInput).ToString())
                testsPassed += 1
            Else
                results.AppendLine("✗ FAIL - SQL injection protection failed")
            End If
        Catch ex As Exception
            results.AppendLine("✗ FAIL - SQL injection protection error: " & ex.Message)
        End Try
        results.AppendLine()

        ' Test 6: Account Lockout Mechanism
        totalTests += 1
        results.AppendLine("Test 6: Account Lockout Mechanism")
        Try
            Dim testUser As String = "testuser_" & DateTime.Now.Ticks.ToString()
            
            ' Simulate failed login attempts
            For i As Integer = 1 To 5
                RecordLoginAttempt(testUser, False)
            Next
            
            Dim isLocked As Boolean = IsAccountLocked(testUser)
            
            If isLocked Then
                results.AppendLine("✓ PASS - Account lockout mechanism works correctly")
                results.AppendLine("  Account locked after 5 failed attempts: " & isLocked.ToString())
                testsPassed += 1
            Else
                results.AppendLine("✗ FAIL - Account lockout mechanism failed")
            End If
        Catch ex As Exception
            results.AppendLine("✗ FAIL - Account lockout mechanism error: " & ex.Message)
        End Try
        results.AppendLine()

        ' Summary
        results.AppendLine("=== TEST SUMMARY ===")
        results.AppendLine("Tests Passed: " & testsPassed.ToString() & "/" & totalTests.ToString())
        results.AppendLine("Success Rate: " & Math.Round((testsPassed / totalTests) * 100, 2).ToString() & "%")
        results.AppendLine()

        If testsPassed = totalTests Then
            results.AppendLine("🎉 ALL TESTS PASSED! Security implementation is working correctly.")
            lblResults.Text = "<div class='test-result success'><strong>All Tests Passed!</strong> Security implementation is working correctly. (" & testsPassed.ToString() & "/" & totalTests.ToString() & ")</div>"
        ElseIf testsPassed > 0 Then
            results.AppendLine("⚠️ SOME TESTS FAILED. Please review the failed tests and fix any issues.")
            lblResults.Text = "<div class='test-result error'><strong>Some Tests Failed!</strong> Please review the results. (" & testsPassed.ToString() & "/" & totalTests.ToString() & ")</div>"
        Else
            results.AppendLine("❌ ALL TESTS FAILED. There are serious issues with the security implementation.")
            lblResults.Text = "<div class='test-result error'><strong>All Tests Failed!</strong> Serious security issues detected. (" & testsPassed.ToString() & "/" & totalTests.ToString() & ")</div>"
        End If

        results.AppendLine()
        results.AppendLine("=== SECURITY RECOMMENDATIONS ===")
        results.AppendLine("1. Ensure HTTPS is enabled in production")
        results.AppendLine("2. Set cookiesRequireSSL='true' in Web.config")
        results.AppendLine("3. Set compilation debug='false' for production")
        results.AppendLine("4. Run password migration for existing users")
        results.AppendLine("5. Monitor security logs regularly")
        results.AppendLine("6. Conduct regular security audits")

        txtResults.Text = results.ToString()
    End Sub

End Class
