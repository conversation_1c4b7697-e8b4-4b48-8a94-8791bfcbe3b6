﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P1_XM_Markah_Sah.aspx.vb" Inherits="SPMJ.WebForm12" 
    title="SPMJ" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 28px;
        }
        .style2
        {
            height: 23px;
        }
        .style3
        {
            width: 857px;
        }
        .style4
        {
            height: 28px;
            width: 857px;
        }
        .style5
        {
            height: 23px;
            width: 857px;
        }
        .style6
        {
            width: 88px;
        }
        .style7
        {
            height: 28px;
            width: 88px;
        }
        .style8
        {
            height: 23px;
            width: 88px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps; background-image: url('Image/Bg_Dot2.gif'); background-attachment: fixed;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td class="style6"></td>
            <td class="style3"></td>
            <td></td>
        </tr>
        <tr>
            <td class="style6">&nbsp;</td>
            <td class="style3">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style7"></td>
            <td align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#990000" class="style4">Sah Markah Peperiksaan</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td class="style6"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style3">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style6"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #990000" 
                bgcolor="White" class="style3">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">JENIS PEPERIKSAAN</asp:TextBox>
                <asp:DropDownList ID="Cb_Kursus" runat="server" Font-Names="Arial" 
            Font-Size="8pt" Width="250px">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="1">JURURAWAT BERDAFTAR</asp:ListItem>
                    <asp:ListItem Value="2">JURURAWAT MASYARAKAT</asp:ListItem>
                    <asp:ListItem Value="3">PENOLONG JURURAWAT</asp:ListItem>
                    <asp:ListItem Value="4">KEBIDANAN I</asp:ListItem>
        </asp:DropDownList>
            </td>
            <td align="center"></td>
        </tr>
        <tr>
            <td class="style6"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #990000" 
                bgcolor="White" class="style5">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj3" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">ANGKA GILIRAN</asp:TextBox>
                <asp:TextBox ID="Tx_AG1" runat="server" CssClass="std" Width="40px" 
                    Wrap="False"></asp:TextBox>
                                        <asp:TextBox ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" 
            tabIndex="36" Width="5px">-</asp:TextBox>
                <asp:TextBox ID="Tx_AG2" runat="server" CssClass="std" Width="40px" 
                    Wrap="False"></asp:TextBox>
            </td>
            <td></td>
        </tr>
        <tr>
            <td class="style8"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #990000" 
                bgcolor="White" class="style5">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" 
            tabIndex="36" Width="110px"></asp:TextBox>
                <asp:Button ID="cmd_Cari2" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CARI" Width="60px" />
            </td>
            <td class="style2"></td>
        </tr>
        <tr>
            <td class="style8"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #990000" 
                bgcolor="White" class="style5">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="TextBox2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" 
            tabIndex="36" Width="308px"></asp:TextBox>
                                        <asp:TextBox ID="TextBox1" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="19px" 
            tabIndex="36" Width="241px" Visible="False">* K1 MCQ = CONVERSION OF 20%</asp:TextBox>
            </td>
            <td class="style2"></td>
        </tr>
        <tr>
            <td class="style6">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #990000; border-bottom-style: solid; border-bottom-width: 1px;" 
                bgcolor="White" class="style3">
                &nbsp;&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style6"></td>
            <td class="style3">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style6"></td>
            <td class="style3">
        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="9pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="100%" BorderColor="#990000">
                    <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                    <RowStyle BackColor="#F4F4F4" Height="21px" />
                    <Columns>
                        <asp:TemplateField HeaderText="ANGKA GILIRAN">
                            <HeaderStyle HorizontalAlign="Center" />
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="Respon Tunggal">
                            <ItemTemplate>
                                <asp:TextBox ID="Tx_rt" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                            </ItemTemplate>
                            <HeaderTemplate>
                                RESPON TUNGGAL
                            </HeaderTemplate>
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="K1 MCQ">
                            <ItemTemplate>
                                <asp:TextBox ID="Tx_k1mcq" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                            </ItemTemplate>
                            <HeaderTemplate>
                                *K1<br/>MCQ
                            </HeaderTemplate>
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="K1 MEQ">
                            <ItemTemplate>
                                <asp:TextBox ID="Tx_k1meq" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                                <asp:TextBox ID="Tx_k1meq2" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                                <asp:TextBox ID="Tx_k1meq3" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                                <asp:TextBox ID="Tx_k1meq4" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                            </ItemTemplate>
                            <HeaderTemplate>
                                K1<br/>MEQ
                            </HeaderTemplate>
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>                        
                        <asp:TemplateField HeaderText="K2 MCQ">
                            <ItemTemplate>
                                <asp:TextBox ID="Tx_k2mcq" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                            </ItemTemplate>
                            <HeaderTemplate>
                                K2<br/>MCQ
                            </HeaderTemplate>
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="K2 MEQ">
                            <ItemTemplate>
                                <asp:TextBox ID="Tx_k2meq" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                                <asp:TextBox ID="Tx_k2meq2" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                                <asp:TextBox ID="Tx_k2meq3" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                                <asp:TextBox ID="Tx_k2meq4" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                            </ItemTemplate>
                            <HeaderTemplate>
                                K2<br/>MEQ
                            </HeaderTemplate>
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>                        
                        <asp:TemplateField HeaderText="OSCE">
                            <ItemTemplate>
                                <asp:TextBox ID="Tx_o" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                            </ItemTemplate>
                            <HeaderTemplate>
                                OSCE
                            </HeaderTemplate>
                        </asp:TemplateField>                         
                        <asp:TemplateField HeaderText="M/B">
                            <ItemTemplate>
                                <asp:TextBox ID="Tx_mb" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                            </ItemTemplate>
                            <HeaderTemplate>
                                MARKAH<br/>BERTERUSAN
                            </HeaderTemplate>
                        </asp:TemplateField> 
                        <asp:TemplateField HeaderText="Praktikal">
                            <ItemTemplate>
                                <asp:TextBox ID="Tx_p" runat="server" CssClass="std" Width="30px" 
                                    Wrap="False"></asp:TextBox>
                            </ItemTemplate>
                            <HeaderTemplate>
                                PRAKTIKAL
                            </HeaderTemplate>
                        </asp:TemplateField>                         
                        <asp:TemplateField HeaderText="Sah?" ShowHeader="False">
                            <ItemTemplate>
                                <asp:CheckBox ID="Chk_Sah" runat="server" />
                            </ItemTemplate>
                        </asp:TemplateField>
                    </Columns>
                    <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                    <SelectedRowStyle Font-Bold="True" />
                    <HeaderStyle BackColor="Maroon" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                    <AlternatingRowStyle BackColor="White" />
                </asp:GridView></td>
            <td></td>
        </tr>
        <tr>
            <td class="style6"></td>
            <td class="style3">&nbsp;</td>
            <td></td>
        </tr>                
        <tr>
            <td class="style6">&nbsp;</td>
            <td class="style3">
                <br />
                        <asp:Button ID="cmd_Jana" runat="server" Font-Names="Arial" Font-Size="8pt" 
                            Height="20px" tabIndex="3" Text="KEMASKINI" Width="100px" 
            Visible="False" />
                        <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>       
    </table>       
    </div></asp:Content>
