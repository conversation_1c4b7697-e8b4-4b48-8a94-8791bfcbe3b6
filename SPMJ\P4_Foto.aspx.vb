﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm68
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select NAMA, jp.NOKP as 'NO. PASPORT', 'Tpc-'+ cast(nopd as varchar(6)) as 'NO. PENDAFTARAN', isnull(jpf.nokp,'X') as FOTO from jt_tpc jp left outer join jt_tpc_foto jpf on jp.nokp=jpf.nokp where " & X
        Tb = "jt_tpc"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(0).Width = Unit.Pixel(30)
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        If e.Row.Cells(5).Text = "X" Then
            e.Row.BackColor = Drawing.Color.Khaki
        Else
            e.Row.BackColor = Drawing.Color.White
            e.Row.Cells(5).Text = ""
        End If
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Response.Write(Ex1 & "c:/progra~1/ljm/spmj_print.exe _tpcfoto" & Gd.SelectedRow.Cells(3).Text & "#" & Gd.SelectedRow.Cells(4).Text & "#" & Gd.SelectedRow.Cells(2).Text & " " & Ex2)
        'Timer2.Enabled = True : Timer2.Interval = 100
    End Sub

    Private Sub Isi()
        If Tx_Nama.Text.Trim = "" And Tx_NoKP.Text.Trim = "" And Tx_NoPd.Text.Trim = "" Then Exit Sub
        If Tx_NoPd2.Text.Trim = "" Then
            Cari("nama like '" & Tx_Nama.Text & "%' and jp.nokp like '" & Tx_NoKP.Text & "%' and nopd like '" & Tx_NoPd.Text & "%' order by nama")
        Else
            Cari("nama like '" & Tx_Nama.Text & "%' and jp.nokp like '" & Tx_NoKP.Text & "%' and nopd between " & Tx_NoPd.Text & " and " & Tx_NoPd2.Text & " order by nopd")
        End If
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Isi()
    End Sub
End Class