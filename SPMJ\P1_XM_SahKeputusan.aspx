<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P1_XM_SahKeputusan.aspx.vb" Inherits="SPMJ.WebForm11" 
    title="SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 28px;
        }
        .style2
        {
            height: 23px;
        }
    .style3
    {
        height: 12px;
    }
    .style4
    {
        height: 27px;
    }
        .style5
        {
            text-decoration: underline;
        }
        .style6
        {
            width: 732px;
        }
        .style7
        {
            height: 28px;
            width: 732px;
        }
        .style8
        {
            height: 23px;
            width: 732px;
        }
        .style9
        {
            height: 12px;
            width: 732px;
        }
        .style10
        {
            height: 27px;
            width: 732px;
        }
    </style></asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps; background-image: url('Image/Bg_Dot2.gif'); background-attachment: fixed;">
        <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td></td>
            <td class="style6"></td>
            <td></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td class="style6">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style1"></td>
            <td align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#E18700" class="style7">Sah Keputusan Peperiksaan</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #E18700" 
                bgcolor="White" class="style6">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #E18700" 
                bgcolor="White" class="style6">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">JENIS PEPERIKSAAN</asp:TextBox>
                <asp:DropDownList ID="Cb_Kursus" runat="server" Font-Names="Arial" 
            Font-Size="8pt" Width="250px">
                    <asp:ListItem></asp:ListItem>
                    <asp:ListItem Value="1">JURURAWAT BERDAFTAR</asp:ListItem>
                    <asp:ListItem Value="2">JURURAWAT MASYARAKAT</asp:ListItem>
                    <asp:ListItem Value="3">PENOLONG JURURAWAT</asp:ListItem>
                    <asp:ListItem Value="4">KEBIDANAN I</asp:ListItem>
        </asp:DropDownList>
                <asp:Button ID="cmd_Semak" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SEMAK" Width="60px" />
            </td>
            <td></td>
        </tr>
        <tr>
            <td class="style2"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #E18700" 
                bgcolor="White" class="style8">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj3" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="110px" Enabled="False" 
                            ReadOnly="True">MARKAH LULUS</asp:TextBox>
                <asp:TextBox ID="Tx_Lulus" runat="server" CssClass="std" Width="80px" 
                    Wrap="False" ReadOnly="True"></asp:TextBox>
            </td>
            <td class="style2"></td>
        </tr>
        <tr>
            <td class="style3"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #E18700" 
                bgcolor="White" class="style9">&nbsp;&nbsp;</td>
            <td class="style3"></td>
        </tr>
        <tr valign="bottom">
            <td class="style4"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #E18700" 
                bgcolor="#EEEEEE" class="style10" valign="bottom">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        
                 <asp:TextBox ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="170px" Enabled="False" 
                            ReadOnly="True">JUMLAH CALON KESELURUHAN</asp:TextBox>
                <asp:TextBox ID="Tx_Calon" runat="server" CssClass="std" Width="80px" 
                    Wrap="False"></asp:TextBox>
                    <asp:TextBox ID="Tx_Calon0" runat="server" CssClass="std" Width="80px" Visible=false 
                    Wrap="False"></asp:TextBox>
                    
                    
                </td>
            <td class="style4"></td>
        </tr>
       
        <tr>
            <td class="style2">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #E18700" 
                bgcolor="#EEEEEE" class="style8" valign="bottom">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                               
                     <asp:TextBox ID="TextBox4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="171px" Enabled="False" 
                            ReadOnly="True">JUMLAH CALON MENDUDUKI</asp:TextBox>
                <asp:TextBox ID="Tx_Menduduki" runat="server" CssClass="std" Width="80px" 
                    Wrap="False"></asp:TextBox>
                    &nbsp;&nbsp;&nbsp;<asp:TextBox ID="TextBox1" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="121px" Enabled="False" 
                            ReadOnly="True">JUMLAH TIDAK HADIR</asp:TextBox>
                <asp:TextBox ID="Tx_Calon_T" runat="server" CssClass="std" Width="80px" 
                    Wrap="False"></asp:TextBox>
                </td>
        </tr>
         <tr>
            <td class="style2">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #E18700" 
                bgcolor="#EEEEEE" class="style8" valign="bottom">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="170px">JUMLAH LULUS</asp:TextBox>
                <asp:TextBox ID="Tx_Calon_L" runat="server" CssClass="std" Width="80px" 
                    Wrap="False"></asp:TextBox>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj6" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="100px">JUMLAH GAGAL</asp:TextBox>
                <asp:TextBox ID="Tx_Calon_G" runat="server" CssClass="std" Width="80px" 
                    Wrap="False"></asp:TextBox>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                   
            <td class="style2">&nbsp;</td>
        </tr>
    
       <tr>
            <td></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #E18700" 
                bgcolor="#EEEEEE" valign="bottom" class="style6">&nbsp;&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style2">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #E18700; font-family: Arial; font-size: 8pt; font-variant: small-caps; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#999999" class="style8" valign="bottom" align="center">
                <br />
                <span class="style5">PERHATIAN !<br />
                </span>
                <br />
                Proses pengesahan ini akan mengunci kesemua proses yang berkaitan
                <br />
                dengan jenis peperiksaan yang dipilih seperti di atas. 
                <br />
                <br />
                Tiada sebarang
                perubahan boleh dibuat selepas proses ini dijalankan.
                <br />
                <br />
                <asp:Button ID="cmd_Tutup" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SAH KEPUTUSAN" Width="150px" 
                    Enabled="False" />
                <cc1:ConfirmButtonExtender ID="cmd_Tutup_ConfirmButtonExtender" runat="server" 
                    ConfirmText="Adakah Anda Pasti?" Enabled="True" TargetControlID="cmd_Tutup">
                </cc1:ConfirmButtonExtender>
                </td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #E18700; border-bottom-style: solid; border-bottom-width: 1px;" 
                bgcolor="#999999" class="style6">
                &nbsp;&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td></td>
            <td class="style6">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td class="style6">
        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="700px" GridLines="Horizontal" BorderColor="#E18700" Visible="False">
                    <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                    <RowStyle BackColor="White" Height="21px" />
                    <Columns>
                        <asp:TemplateField HeaderText="#">
                            <HeaderStyle HorizontalAlign="Center" />
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                    </Columns>
                    <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                    <SelectedRowStyle Font-Bold="True" />
                    <HeaderStyle BackColor="#E18700" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                    <AlternatingRowStyle BackColor="White" />
                </asp:GridView></td>
            <td></td>
        </tr>
    
    
        <tr>
            <td>&nbsp;</td>
            <td class="style6">
                <br />
                        <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>
    
    
    </table>
    
    
    </div></asp:Content>
