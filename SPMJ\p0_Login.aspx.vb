﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm30
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        ' Add security headers
        Response.Headers.Add("X-Frame-Options", "DENY")
        Response.Headers.Add("X-Content-Type-Options", "nosniff")
        Response.Headers.Add("X-XSS-Protection", "1; mode=block")
        Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin")
        Response.Headers.Add("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'")

        ' Clear any existing session for security
        If Not IsPostBack Then
            Session.Clear()
            Session.Abandon()
        End If

        If IsPostBack Then Exit Sub
        Dim x As Menu = Master.FindControl("Menu1")
        x.Visible = False

        ' Set focus to username field
        Tx_Id.Focus()
    End Sub

    Protected Sub cmd_OK_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_OK.Click
        ' Input validation
        If String.IsNullOrEmpty(Tx_Id.Text) OrElse Tx_Id.Text.Trim() = "" Then
            Msg(Me, "Sila masukkan ID Pengguna!")
            Tx_Id.Focus()
            Exit Sub
        End If

        If String.IsNullOrEmpty(Tx_Pwd.Text) OrElse Tx_Pwd.Text.Trim() = "" Then
            Msg(Me, "Sila masukkan Kata Laluan!")
            Tx_Pwd.Focus()
            Exit Sub
        End If

        ' Sanitize inputs
        Dim userId As String = SanitizeInput(Tx_Id.Text.Trim)
        Dim password As String = Tx_Pwd.Text.Trim

        ' Enhanced input validation
        If Chk_SQL(userId) = True Then
            Msg(Me, "Format ID Pengguna tidak sah!")
            RecordLoginAttempt(userId, False)
            Exit Sub
        End If

        If Chk_SQL(password) = True Then
            Msg(Me, "Format Kata Laluan tidak sah!")
            RecordLoginAttempt(userId, False)
            Exit Sub
        End If

        ' Check if account is locked due to multiple failed attempts
        If IsAccountLocked(userId) Then
            Msg(Me, "Akaun dikunci sementara disebabkan terlalu banyak percubaan log masuk yang gagal. Sila cuba lagi dalam 30 minit.")
            Exit Sub
        End If

        ' Database connection with parameterized query
        Dim Cn As New OleDbConnection
        Dim Cmd As New OleDbCommand
        Dim Rdr As OleDbDataReader

        Try
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn

            ' Use parameterized query to prevent SQL injection
            Cmd.CommandText = "SELECT id_pg, pwd, modul, akses, nama FROM pn_pengguna WHERE id_pg = ? AND status = 1"
            Cmd.Parameters.Add("@id_pg", OleDbType.VarChar).Value = userId

            Rdr = Cmd.ExecuteReader()

            If Rdr.Read Then
                Dim storedPassword As String = Rdr("pwd").ToString()
                Dim isValidPassword As Boolean = False

                ' Check if password is already hashed (new format) or plain text (legacy)
                If storedPassword.Length = 64 AndAlso System.Text.RegularExpressions.Regex.IsMatch(storedPassword, "^[a-fA-F0-9]+$") Then
                    ' Hashed password - verify using hash
                    isValidPassword = VerifyPassword(password, storedPassword)
                Else
                    ' Legacy plain text password - direct comparison and upgrade to hash
                    If storedPassword = password Then
                        isValidPassword = True
                        ' Upgrade to hashed password
                        Rdr.Close()
                        UpgradePasswordToHash(userId, password, Cn)
                    End If
                End If

                If isValidPassword Then
                    ' Successful login
                    Session("Id_PG") = Rdr("Id_PG").ToString()
                    Session("MODUL") = Rdr("MODUL").ToString()
                    Session("AKSES") = Rdr("AKSES").ToString()
                    Session("NAMA") = Rdr("NAMA").ToString()
                    Session("ORIGIN") = "yes"
                    Session("LOGIN_TIME") = DateTime.Now

                    ' Record successful login
                    RecordLoginAttempt(userId, True)

                    ' Clear password from memory
                    password = String.Empty

                    If Not Rdr.IsClosed Then Rdr.Close()
                    Cn.Close()

                    Response.Redirect("blank.aspx", False)
                Else
                    ' Invalid password
                    If Not Rdr.IsClosed Then Rdr.Close()
                    Cn.Close()
                    RecordLoginAttempt(userId, False)
                    Msg(Me, "ID Pengguna atau Kata Laluan tidak sah!")
                End If
            Else
                ' User not found
                Rdr.Close()
                Cn.Close()
                RecordLoginAttempt(userId, False)
                Msg(Me, "ID Pengguna atau Kata Laluan tidak sah!")
            End If

        Catch ex As Exception
            ' Log the error (in production, use proper logging)
            If Not Rdr Is Nothing AndAlso Not Rdr.IsClosed Then Rdr.Close()
            If Cn.State = ConnectionState.Open Then Cn.Close()
            RecordLoginAttempt(userId, False)
            Msg(Me, "Ralat sistem. Sila cuba lagi.")
        Finally
            ' Clear sensitive data
            password = String.Empty
        End Try
    End Sub

    Private Sub UpgradePasswordToHash(ByVal userId As String, ByVal plainPassword As String, ByVal connection As OleDbConnection)
        Try
            Dim hashedPassword As String = HashPassword(plainPassword)
            Dim updateCmd As New OleDbCommand()
            updateCmd.Connection = connection
            updateCmd.CommandText = "UPDATE pn_pengguna SET pwd = ? WHERE id_pg = ?"
            updateCmd.Parameters.Add("@pwd", OleDbType.VarChar).Value = hashedPassword
            updateCmd.Parameters.Add("@id_pg", OleDbType.VarChar).Value = userId
            updateCmd.ExecuteNonQuery()
        Catch ex As Exception
            ' Log error but don't fail login
        End Try
    End Sub
End Class