﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.Net.NetworkInformation

Partial Public Class WebForm35
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If IsPostBack Then Exit Sub
    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari2.Click
        Dim X As String = ""
        If Cb_Modul.SelectedIndex < 1 Then X += "Modul, "
        If Tx_NoKP.Text.Trim = "" Then X += "No Kad Pengenalan, "
        If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:  " & X) : Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Dim dt As DateTime

        If Cb_Modul.SelectedValue = 1 Then
            'peperiksaan
            'Comment Original 16052016 - OSH
            'Cmd.CommandText = "select nama,'' as nopd from pelatih where nokp = '" & Tx_NoKP.Text & "'"

            'improverment query select active records only  16052016 - OSH
            'Cmd.CommandText = "select nama,'' as nopd from pelatih where status is null and nokp = '" & Tx_NoKP.Text & "'"
            'Trim text 15082022 - OSH 
            Cmd.CommandText = "select nama,'' as nopd from pelatih where status is null and nokp = '" & Tx_NoKP.Text.Trim & "'"


            'Label1.Text = " ip addr :" & Request.UserHostAddress & " mac addr :" & getMacAddress() & ""

            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                Tx_Nama.Text = Rdr(0)
                Tx_NoPd.Text = Rdr(1)
            End If
            Rdr.Close()
        Else
            'pendaftaran penuh
            'Trim prevent space 15082022 - OSH 
            Cmd.CommandText = "select nama,j_daftar, case j_daftar when 1 then 'JB-'+cast(nopd as varchar(6)) when 2 " & _
                            "then 'JM-'+cast(nopd as varchar(6))  when 3 then 'PJ-'+cast(nopd as varchar(6)) end, " & _
                            "isnull(tkh_daftar,'') from jt_penuh where nokp = '" & Tx_NoKP.Text.Trim & "'"
            'Comment Original 15082022 - OSH
            'Cmd.CommandText = "select nama,j_daftar, case j_daftar when 1 then 'JB-'+cast(nopd as varchar(6)) when 2 " & _
            '                  "then 'JM-'+cast(nopd as varchar(6))  when 3 then 'PJ-'+cast(nopd as varchar(6)) end, " & _
            '                  "isnull(tkh_daftar,'') from jt_penuh where nokp = '" & Tx_NoKP.Text & "'"

            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                Tx_Nama.Text = Rdr(0)
                Tx_NoPd3.Text = Rdr(1)
                Tx_NoPd.Text = Rdr(2)
                'Comment Original 13082018 - OSH
                'Tx_Tkh.Text = Rdr(3)

                'FIXING DISPLAY DATE FORMAT 13082018  - OSH
                dt = Rdr(3) : Tx_Tkh.Text = dt.ToString("dd/MM/yyyy")
            End If

            Rdr.Close()
        End If
        Cn.Close()
    End Sub

    Protected Sub Cb_Modul_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Modul.SelectedIndexChanged
        Tx_Nama.Text = ""
        Tx_NoPd.Text = ""
        Tx_Tkh.Text = ""

        Tx_Nama2.Text = ""
        Tx_NoKP2.Text = ""
        Tx_NoPd2.Text = ""
        Tx_Tkh_Daftar.Text = ""

        If Cb_Modul.SelectedIndex = 1 Then Tx_Nama2.Enabled = True : Tx_NoKP2.Enabled = True : Tx_NoPd2.Enabled = False : Tx_Tkh_Daftar.Enabled = False
        If Cb_Modul.SelectedIndex = 2 Then Tx_Nama2.Enabled = True : Tx_NoKP2.Enabled = True : Tx_NoPd2.Enabled = True : Tx_Tkh_Daftar.Enabled = True

    End Sub

    Protected Sub cmdNama_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdNama.Click
        If Tx_Nama2.Text = "" Then Exit Sub
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Dim SQL, M, J As String : SQL = "" : M = "" : J = ""

        If Cb_Modul.SelectedIndex = 1 Then
            'Comment Original 17052016 - OSH
            'SQL = "update pelatih set nama = '" & Apo(Tx_Nama2.Text.Trim.ToUpper) & "' where nokp = '" & Tx_NoKP.Text & "'"

            'Record source of alter data 18052016 -OSH
            J = "E" ' Exam
            'Improverment update query 17052016 - OSH  
            'Comment Original 15082022 - OSH
            'SQL = "update pelatih set nama = '" & Apo(Tx_Nama2.Text.Trim.ToUpper) & "' where status is  null and nokp = '" & Tx_NoKP.Text & "'"
            'Trim Text 15082022 - OSH 
            SQL = "update pelatih set nama = '" & Apo(Tx_Nama2.Text.Trim.ToUpper) & "' where status is  null and nokp = '" & Tx_NoKP.Text.Trim & "'"



            M = "PINDA NAMA PELATIH"
        ElseIf Cb_Modul.SelectedIndex = 2 Then
            'Record source of alter data 18052016 -OSH
            J = "R" 'Registration
            'Comment Original 15082022 - OSH 
            'SQL = "update jt_penuh set nama = '" & Apo(Tx_Nama2.Text.Trim.ToUpper) & "' where nokp = '" & Tx_NoKP.Text & "'"
            'Trim Text 15082022 - OSH 
            SQL = "update jt_penuh set nama = '" & Apo(Tx_Nama2.Text.Trim.ToUpper) & "' where nokp = '" & Tx_NoKP.Text.Trim & "'"
            M = "PINDA NAMA PENDAFTARAN"
        End If

        'SQL += "INSERT INTO PN_PINDA_LOG (J_Pinda,J_Rekod,NoKP, Asal, Pinda, Alamat_IP, Alamat_Mesin,Log_id,Log_Tkh) VALUES" & _
        '        "('" & J & "','NAMA','" & Tx_NoKP.Text.Trim & "','" & Apo(Tx_Nama.Text.Trim) & "','" & Apo(Tx_Nama2.Text.Trim.ToUpper) & "','" & Request.UserHostAddress & "','" & getMacAddress() & "','" & Session("Id_PG") & "',getdate())"
        'Record update changed 18052016 - OSH
        'SQL += "INSERT INTO PN_PINDA_LOG (J_Pinda,J_Rekod,NoKP, Asal, Pinda, Alamat_IP,Log_id,Log_Tkh) VALUES" & _
        '               "('" & J & "','NAMA','" & Tx_NoKP.Text.Trim & "','" & Apo(Tx_Nama.Text.Trim) & "','" & Apo(Tx_Nama2.Text.Trim.ToUpper) & "','" & Request.UserHostAddress & "','" & Session("Id_PG") & "',getdate())"

        'Add MAC address capture 13082018 - OSH 
        Dim mac As String
        mac = getMacAddress()

        SQL += "INSERT INTO PN_PINDA_LOG (J_Pinda,J_Rekod,NoKP, Asal, Pinda, Alamat_IP,Alamat_Mesin,Log_id,Log_Tkh) VALUES" & _
                      "('" & J & "','NAMA','" & Tx_NoKP.Text.Trim & "','" & Apo(Tx_Nama.Text.Trim) & "','" & Apo(Tx_Nama2.Text.Trim.ToUpper) & "','" & Request.UserHostAddress & "','" & mac & "','" & Session("Id_PG") & "',getdate())"

        Cmd.CommandText = SQL
        Cmd.ExecuteNonQuery()
        Session("Msg_Tajuk") = M
        Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
        Response.Redirect("p0_mesej.aspx")
    End Sub

    Protected Sub cmdNoKP_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdNoKP.Click
        If Tx_NoKP2.Text = "" Then Exit Sub
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader : Dim SQL, J As String
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : J = ""

        If Cb_Modul.SelectedIndex = 1 Then
            'Comment Original 15082022 - OSH
            'Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP2.Text & "'"
            'Trim Text 15082022 - OSH 
            Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP2.Text.Trim & "'"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                Msg(Me, "No. Kad Pengenalan Sudah Ada!!!")
                Rdr.Close()
                Cn.Close()
                Exit Sub
            Else
                Rdr.Close()
                'Comment Original 15082022 - OSH 
                'SQL = "update pelatih set nokp = '" & Tx_NoKP2.Text & "' where nokp = '" & Tx_NoKP.Text & "' ;"
                'SQL += "update pelatih_kelayakan set nokp = '" & Tx_NoKP2.Text & "' where nokp = '" & Tx_NoKP.Text & "' ;"

                'Trim Text 15082022 - OSH 
                SQL = "update pelatih set nokp = '" & Tx_NoKP2.Text.Trim & "' where nokp = '" & Tx_NoKP.Text.Trim & "' ;"
                SQL += "update pelatih_kelayakan set nokp = '" & Tx_NoKP2.Text.Trim & "' where nokp = '" & Tx_NoKP.Text.Trim & "' ;"

                'Record source of alter data 18052016 -OSH
                J = "E" ' Exam

                'Record update changed 18052016 - OSH
                'SQL += "INSERT INTO PN_PINDA_LOG (J_Pinda,J_Rekod,NoKP, Asal, Pinda, Alamat_IP,Log_id,Log_Tkh) VALUES" & _
                '     "('" & J & "','NOKP','" & Tx_NoKP.Text.Trim & "','" & Tx_NoKP.Text.Trim & "','" & Tx_NoKP2.Text.Trim & "','" & Request.UserHostAddress & "','" & Session("Id_PG") & "',getdate())"

                'Add MAC address capture 13082018 - OSH 
                Dim mac As String
                mac = getMacAddress()

                SQL += "INSERT INTO PN_PINDA_LOG (J_Pinda,J_Rekod,NoKP, Asal, Pinda, Alamat_IP,Alamat_Mesin,Log_id,Log_Tkh) VALUES" & _
                   "('" & J & "','NOKP','" & Tx_NoKP.Text.Trim & "','" & Tx_NoKP.Text.Trim & "','" & Tx_NoKP2.Text.Trim & "','" & Request.UserHostAddress & "','" & mac & "','" & Session("Id_PG") & "',getdate())"

                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Session("Msg_Tajuk") = "PINDA NO. KAD PENGENALAN PELATIH"
                Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
                Response.Redirect("p0_mesej.aspx")
            End If

        ElseIf Cb_Modul.SelectedIndex = 2 Then
            'Comment Original 15082022 - OSH 
            'Cmd.CommandText = "select nokp from jt_penuh where nokp = '" & Tx_NoKP2.Text & "'"
            'Trim Text 15082022 - OSH 
            Cmd.CommandText = "select nokp from jt_penuh where nokp = '" & Tx_NoKP2.Text.Trim & "'"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                Msg(Me, "No. Kad Pengenalan Sudah Ada!!!")
                Rdr.Close()
                Cn.Close()
                Exit Sub
            Else
                Rdr.Close()
                'Comment Original 15082022 - OSH 
                'SQL = "update jt_penuh set nokp = '" & Tx_NoKP2.Text & "' where nokp = '" & Tx_NoKP.Text & "' ;"
                'SQL += "update jt_penuh_apc set nokp = '" & Tx_NoKP2.Text & "' where nokp = '" & Tx_NoKP.Text & "' ;"
                'SQL += "update jt_penuh_foto set nokp = '" & Tx_NoKP2.Text & "' where nokp = '" & Tx_NoKP.Text & "' ;"
                'SQL += "update jt_penuh_kelayakan set nokp = '" & Tx_NoKP2.Text & "' where nokp = '" & Tx_NoKP.Text & "' ;"
                'SQL += "update jt_penuh_nniat set nokp = '" & Tx_NoKP2.Text & "' where nokp = '" & Tx_NoKP.Text & "' ;"

                'Trim Text 15082022 - OSH 
                SQL = "update jt_penuh set nokp = '" & Tx_NoKP2.Text.Trim & "' where nokp = '" & Tx_NoKP.Text.Trim & "' ;"
                SQL += "update jt_penuh_apc set nokp = '" & Tx_NoKP2.Text.Trim & "' where nokp = '" & Tx_NoKP.Text.Trim & "' ;"
                SQL += "update jt_penuh_foto set nokp = '" & Tx_NoKP2.Text.Trim & "' where nokp = '" & Tx_NoKP.Text.Trim & "' ;"
                SQL += "update jt_penuh_kelayakan set nokp = '" & Tx_NoKP2.Text.Trim & "' where nokp = '" & Tx_NoKP.Text.Trim & "' ;"
                SQL += "update jt_penuh_nniat set nokp = '" & Tx_NoKP2.Text.Trim & "' where nokp = '" & Tx_NoKP.Text.Trim & "' ;"

                'Record source of alter data 18052016 -OSH
                J = "R" 'Registration

                'Record update changed 18052016 - OSH
                'SQL += "INSERT INTO PN_PINDA_LOG (J_Pinda,J_Rekod,NoKP, Asal, Pinda, Alamat_IP,Log_id,Log_Tkh) VALUES" & _
                '     "('" & J & "','NOKP','" & Tx_NoKP.Text.Trim & "','" & Tx_NoKP.Text.Trim & "','" & Tx_NoKP2.Text.Trim & "','" & Request.UserHostAddress & "','" & Session("Id_PG") & "',getdate());"

                'Add MAC address capture 13082018 - OSH 
                Dim mac As String
                mac = getMacAddress()

                SQL += "INSERT INTO PN_PINDA_LOG (J_Pinda,J_Rekod,NoKP, Asal, Pinda, Alamat_IP,Alamat_Mesin,Log_id,Log_Tkh) VALUES" & _
                   "('" & J & "','NOKP','" & Tx_NoKP.Text.Trim & "','" & Tx_NoKP.Text.Trim & "','" & Tx_NoKP2.Text.Trim & "','" & Request.UserHostAddress & "','" & mac & "','" & Session("Id_PG") & "',getdate());"


                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Session("Msg_Tajuk") = "PINDA NO. KAD PENGENALAN PENDAFTARAN PENUH"
                Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
                Response.Redirect("p0_mesej.aspx")
            End If
        End If
    End Sub

    Protected Sub cmdDaftar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdDaftar.Click
        If Tx_NoPd2.Text = "" Then Exit Sub
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader : Dim SQL, J As String
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : J = ""

        If Cb_Modul.SelectedIndex = 2 Then
            Cmd.CommandText = "select nopd from jt_penuh where nopd=" & CInt(Tx_NoPd2.Text) & " and j_daftar=" & CInt(Tx_NoPd3.Text)
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                Msg(Me, "No. Pendaftaran Sudah Ada!!!")
                Rdr.Close()
                Exit Sub
            Else
                Rdr.Close()
                'Comment Original 15082022 - OSH 
                'SQL = "update jt_penuh set nopd = '" & Tx_NoPd2.Text & "' where nokp = '" & Tx_NoKP.Text & "'"
                'Trim Text 15082022 - OSH 
                SQL = "update jt_penuh set nopd = '" & Tx_NoPd2.Text & "' where nokp = '" & Tx_NoKP.Text.Trim & "'"

                'Record source of alter data 18052016 -OSH
                J = "R" 'Registration

                'Record update changed 18052016 - OSH
                'SQL += "INSERT INTO PN_PINDA_LOG (J_Pinda,J_Rekod,NoKP, Asal, Pinda, Alamat_IP,Log_id,Log_Tkh) VALUES" & _
                '     "('" & J & "','NOPD','" & Tx_NoKP.Text.Trim & "','" & Tx_NoPd.Text.Trim & "','" & Tx_NoPd2.Text.Trim & "','" & Request.UserHostAddress & "','" & Session("Id_PG") & "',getdate())"

                'Add MAC address capture 13082018 - OSH 
                Dim mac As String
                mac = getMacAddress()

                SQL += "INSERT INTO PN_PINDA_LOG (J_Pinda,J_Rekod,NoKP, Asal, Pinda, Alamat_IP,Alamat_Mesin,Log_id,Log_Tkh) VALUES" & _
                     "('" & J & "','NOPD','" & Tx_NoKP.Text.Trim & "','" & Tx_NoPd.Text.Trim & "','" & Tx_NoPd2.Text.Trim & "','" & Request.UserHostAddress & "','" & mac & "','" & Session("Id_PG") & "',getdate())"


                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Cn.Close()
                Session("Msg_Tajuk") = "PINDA REKOD PEPERIKSAAN "
                Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
                Response.Redirect("p0_mesej.aspx")
            End If
        End If
    End Sub

    Protected Sub cmdTkh_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdTkh.Click
        If Tx_Tkh_Daftar.Text = "" Then Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim SQL, J As String
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : J = ""

        'Comment Ori 05082014 - OSH
        ' Cmd.CommandText = "update jt_penuh set tkh_daftar = " & Chk_Tkh(Tx_Tkh_Daftar.Text) & " where nokp = '" & Tx_NoKP.Text & "'"


        'Fixing Update parameter IC number /passport 05082014 - OSH
        'Cmd.CommandText = "update jt_penuh set tkh_daftar = " & Chk_Tkh(Tx_Tkh_Daftar.Text) & " where nokp = '" & Tx_NoKP.Text & "'"

        'FIXING DATE FORMAT 09082018 - OSH
        Dim a, a1 As DateTime
        Dim b, b1 As String

        If Tx_Tkh_Daftar.Text.Trim <> String.Empty Then
            b = Tx_Tkh_Daftar.Text.Trim
            a = DateTime.ParseExact(b, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            b = a.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)

        Else
            b = "NULL"
        End If

        If Tx_Tkh.Text.Trim <> String.Empty Then
            b1 = Tx_Tkh.Text.Trim
            a1 = DateTime.ParseExact(b1, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            b1 = a1.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)

        Else
            b1 = "NULL"
        End If


        SQL = "update jt_penuh set tkh_daftar = '" & b & "' where nokp = '" & Tx_NoKP.Text & "';"
        'Record source of alter data 18052016 -OSH
        J = "R" 'Registration

        ''Record update changed 18052016 - OSH
        'SQL += "INSERT INTO PN_PINDA_LOG (J_Pinda,J_Rekod,NoKP, Asal, Pinda, Alamat_IP,Log_id,Log_Tkh) VALUES" & _
        '     "('" & J & "','TARIKH DAFTAR','" & Tx_NoKP.Text.Trim & "','" & Chk_Tkh(Tx_Tkh.Text) & "','" & Chk_Tkh(Tx_Tkh_Daftar.Text) & "','" & Request.UserHostAddress & "','" & Session("Id_PG") & "',getdate());"

        'Fixing date format  - OSH
        'SQL += "INSERT INTO PN_PINDA_LOG (J_Pinda,J_Rekod,NoKP, Asal, Pinda, Alamat_IP,Log_id,Log_Tkh) VALUES" & _
        '     "('" & J & "','TARIKH DAFTAR','" & Tx_NoKP.Text.Trim & "','" & b & "','" & b1 & "','" & Request.UserHostAddress & "','" & Session("Id_PG") & "',getdate());"

        'Add MAC address capture 13082018 - OSH 
        Dim mac As String
        mac = getMacAddress()
        SQL += "INSERT INTO PN_PINDA_LOG (J_Pinda,J_Rekod,NoKP, Asal, Pinda, Alamat_IP,Alamat_Mesin, Log_id,Log_Tkh) VALUES" & _
             "('" & J & "','TARIKH DAFTAR','" & Tx_NoKP.Text.Trim & "','" & b & "','" & b1 & "','" & Request.UserHostAddress & "','" & mac & "','" & Session("Id_PG") & "',getdate());"

        Cmd.CommandText = SQL
        Cmd.ExecuteNonQuery()
        Session("Msg_Tajuk") = "PINDA TARIKH PENDAFTARAN PENUH"
        Session("Msg_Isi") = "Rekod Telah Dikemaskini..."
        Response.Redirect("p0_mesej.aspx")
    End Sub

    Function getMacAddress()
        Dim nics() As NetworkInterface = NetworkInterface.GetAllNetworkInterfaces()
        Return nics(0).GetPhysicalAddress.ToString
    End Function

End Class