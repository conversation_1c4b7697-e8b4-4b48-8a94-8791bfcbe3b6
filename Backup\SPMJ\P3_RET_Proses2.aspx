﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="P3_RET_Proses2.aspx.vb" Inherits="SPMJ.P3_RET_Proses2" 
    title="Proses Pengekalan Nama - SPMJ" %>
<%@ Register assembly="AjaxControlToolkit" namespace="AjaxControlToolkit" tagprefix="cc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style3
        {
            height: 23px;
            width: 640px;
        }
        .style4
        {
            width: 640px;
        }
        .style6
        {
            width: 206px;
        }
        .style7
        {
            width: 640px;
            height: 42px;
        }
        </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%" 
            style="font-variant: small-caps">
        <tr>
            <td rowspan="22" class="style6">&nbsp;</td>
            <td class="style3"></td>
            <td rowspan="22">&nbsp;</td>
        </tr>
        <tr>
            <td class="style4">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                </td>
        </tr>
        <tr>
            <td class="style3" align="center" bgcolor="#336699" 
                
                style="font-family: Arial; font-size: 8pt; font-weight: bolder; color: #FFFFFF;">
                proses pengekalan nama</td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF">
                &nbsp;</td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" ReadOnly="True">NAMA</asp:TextBox>
                <asp:TextBox ID="Tx_Nama" runat="server" CssClass="std" Width="190px" 
                    Wrap="False" ReadOnly="True"></asp:TextBox>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" ReadOnly="True">NO. KP</asp:TextBox>
                                                 <asp:TextBox ID="Tx_NoKP" runat="server" 
                    CssClass="std" Width="190px" 
                                                     Wrap="False" ReadOnly="True"></asp:TextBox>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="Cb_Sbj1" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" ReadOnly="True">NO. PENDAFTARAN</asp:TextBox>
                                                 <asp:TextBox ID="Tx_NoPd" runat="server" 
                    CssClass="std" Width="190px" 
                                                     Wrap="False" ReadOnly="True"></asp:TextBox>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                        <asp:TextBox ID="TextBox2" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px" Enabled="False" ReadOnly="True">TARIKH PENDAFTARAN</asp:TextBox>
                                                 <asp:TextBox ID="Tx_Tkh_Daftar" runat="server" 
                    CssClass="std" Width="190px" 
                                                     Wrap="False" ReadOnly="True"></asp:TextBox>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF">
                &nbsp;</td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
        <table style="width: 550px" align="center"><tr><td>
            <asp:GridView ID="Gd_RON" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="8pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="100%" GridLines="Horizontal" BorderColor="#336699">
                    <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                    <RowStyle BackColor="White" Height="21px" />
                    <Columns>
                        <asp:TemplateField ShowHeader="False">
                        <ItemTemplate>
                        <asp:Button ID="Button1" runat="server" CausesValidation="False" 
                        CommandName="Select" Font-Names="Arial" Font-Size="8pt" Height="19px" 
                        Text="SEMAK" Width="50px" onclick="Button1_Click" />
                        </ItemTemplate>
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="PILIH" ShowHeader="False">
                            <EditItemTemplate>
                                <asp:CheckBox ID="chkBATAL" runat="server" />
                            </EditItemTemplate>
                            <ItemTemplate>
                                <asp:CheckBox ID="chkBATAL" runat="server" />
                            </ItemTemplate>
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="#">
                            <HeaderStyle HorizontalAlign="Center" />
                            <ItemStyle HorizontalAlign="Center" Width="30px" />
                        </asp:TemplateField>
                    </Columns>
                    <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                    <SelectedRowStyle Font-Bold="False" />
                    <HeaderStyle BackColor="#528BC5" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                    <AlternatingRowStyle BackColor="White" />
                </asp:GridView></td></tr><tr><td>
                &nbsp;</td></tr></table></td>
        </tr>
        <tr>
            <td class="style4" 
                
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; border-top-style: solid; border-top-width: 1px;">
                &nbsp;&nbsp;</td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj5" 
    runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px">TEMPOH</asp:TextBox>
                        <asp:DropDownList ID="Cb_Tempoh" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="101px">
                            <asp:ListItem>1 TAHUN</asp:ListItem>
                            <asp:ListItem>2 TAHUN</asp:ListItem>
                            <asp:ListItem>3 TAHUN</asp:ListItem>
                        </asp:DropDownList>
                        &nbsp;<br />
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj6" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="130px">MULAI TAHUN</asp:TextBox>
                        <asp:TextBox ID="Tx_Tahun" runat="server" CssClass="std" Width="95px" 
                            Wrap="False" MaxLength="4"></asp:TextBox>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj7" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px">NO. RESIT</asp:TextBox>
                <asp:TextBox ID="Tx_NoResit" runat="server" CssClass="std" Width="95px" 
                    Wrap="False"></asp:TextBox>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj8" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px">TARIKH RESIT</asp:TextBox>
                                                <asp:TextBox ID="Tx_TkhResit" runat="server" 
                    CssClass="std" Width="95px"></asp:TextBox>
                                                <cc1:MaskedEditExtender ID="Tx_TkhResit_MaskedEditExtender" runat="server" 
                                                    Enabled="True" Mask="99/99/9999" MaskType="Date" 
                                                    TargetControlID="Tx_TkhResit" 
                    UserDateFormat="DayMonthYear">
                                                </cc1:MaskedEditExtender>
                                                <cc1:CalendarExtender ID="Tx_TkhResit_CalendarExtender" runat="server" 
                                                    Enabled="True" Format="dd/MM/yyyy" PopupPosition="Right" 
                                                    TargetControlID="Tx_TkhResit">
                                                </cc1:CalendarExtender>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj9" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px">AMAUN</asp:TextBox>
                <asp:TextBox ID="Tx_Amaun" runat="server" CssClass="std" Width="95px" 
                    Wrap="False"></asp:TextBox>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="TextBox1" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px">NEGARA BEKERJA</asp:TextBox>
                <asp:DropDownList ID="Cb_Negara" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="309px">
                        </asp:DropDownList>
                </td>
        </tr>
        <tr>
            <td class="style4" 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="TextBox3" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px">SEBAB</asp:TextBox>
                <asp:DropDownList ID="Cb_Sebab" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="309px">
                        </asp:DropDownList>
                </td>
        </tr>
        <tr>
            <td class="style7" align="left" valign="top"
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="TextBox5" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px">CATATAN</asp:TextBox>
                <asp:TextBox ID="Tx_Catatan" runat="server" CssClass="std" Width="309px" 
                    Wrap="False" MaxLength="100" Height="52px" TextMode="MultiLine"></asp:TextBox>
                </td>
        </tr>
         <tr>
            <td class="style7" align="left" valign="top"
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="TextBox4" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="130px">JUSTIFIKASI</asp:TextBox>
                <asp:TextBox ID="TX_Justifikasi" runat="server" CssClass="std" Width="309px" 
                    Wrap="False" MaxLength="100" Height="52px" TextMode="MultiLine"></asp:TextBox>
                </td>
        </tr>
        <tr>
            <td class="style4" style="border-right-style: solid; border-left-style: solid; 
                border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
                &nbsp;</td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; ">
              
                                        <asp:TextBox ID="Cb_Sbj10" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="23px"></asp:TextBox>
                <asp:Button ID="cmd_Simpan" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="SIMPAN" Width="73px" />&nbsp;
                <asp:Button ID="cmd_CetakDftr" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CETAK SURAT (PENDAFTAR)" Width="153px" />&nbsp;
                <asp:Button ID="cmd_CetakSU" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="CETAK SURAT (SETIAUSAHA)" Width="157px" />
                     <asp:Button ID="cmd_Hantar_Batal" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="BATAL" Width="157px" />
                </td>
        </tr>
        <tr>
            <td class="style4" 
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #336699; background-color: #FFFFFF; border-bottom-style: solid; border-bottom-width: 1px;">
                &nbsp;</td>
        </tr?
        <tr>
            <td class="style4">
                <br />
            </td>
        </tr>
    </table>
    </div></asp:Content>
    
