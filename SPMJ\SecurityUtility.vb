Imports System.Data.OleDb
Imports System.Security.Cryptography
Imports System.Text

Public Class SecurityUtility

    ' Password migration utility for upgrading existing plain text passwords
    Public Shared Sub MigrateAllPasswordsToHash()
        Dim Cn As New OleDbConnection
        Dim Cmd As New OleDbCommand
        Dim Rdr As OleDbDataReader
        Dim updateCmd As New OleDbCommand

        Try
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn
            updateCmd.Connection = Cn

            ' Get all users with plain text passwords (not 64-character hex strings)
            Cmd.CommandText = "SELECT id_pg, pwd FROM pn_pengguna WHERE status = 1"
            Rdr = Cmd.ExecuteReader()

            Dim usersToUpdate As New List(Of KeyValuePair(Of String, String))

            While Rdr.Read()
                Dim userId As String = Rdr("id_pg").ToString()
                Dim password As String = Rdr("pwd").ToString()

                ' Check if password is not already hashed
                If Not (password.Length = 64 AndAlso System.Text.RegularExpressions.Regex.IsMatch(password, "^[a-fA-F0-9]+$")) Then
                    usersToUpdate.Add(New KeyValuePair(Of String, String)(userId, password))
                End If
            End While

            Rdr.Close()

            ' Update passwords to hashed versions
            For Each user In usersToUpdate
                Dim hashedPassword As String = HashPassword(user.Value)
                updateCmd.Parameters.Clear()
                updateCmd.CommandText = "UPDATE pn_pengguna SET pwd = ? WHERE id_pg = ?"
                updateCmd.Parameters.Add("@pwd", OleDbType.VarChar).Value = hashedPassword
                updateCmd.Parameters.Add("@id_pg", OleDbType.VarChar).Value = user.Key
                updateCmd.ExecuteNonQuery()
            Next

        Catch ex As Exception
            ' Log error
            Throw New Exception("Password migration failed: " & ex.Message)
        Finally
            If Not Rdr Is Nothing AndAlso Not Rdr.IsClosed Then Rdr.Close()
            If Cn.State = ConnectionState.Open Then Cn.Close()
        End Try
    End Sub

    ' Session security validation
    Public Shared Function ValidateSession(ByVal page As System.Web.UI.Page) As Boolean
        Try
            ' Check if session exists
            If page.Session("Id_PG") Is Nothing OrElse String.IsNullOrEmpty(page.Session("Id_PG").ToString()) Then
                Return False
            End If

            ' Check session timeout (30 minutes)
            If page.Session("LOGIN_TIME") IsNot Nothing Then
                Dim loginTime As DateTime = CType(page.Session("LOGIN_TIME"), DateTime)
                If DateTime.Now.Subtract(loginTime).TotalMinutes > 30 Then
                    page.Session.Clear()
                    page.Session.Abandon()
                    Return False
                End If
            End If

            ' Check origin validation
            If page.Session("ORIGIN") Is Nothing OrElse page.Session("ORIGIN").ToString() <> "yes" Then
                Return False
            End If

            Return True

        Catch ex As Exception
            Return False
        End Try
    End Function

    ' Secure session cleanup
    Public Shared Sub SecureLogout(ByVal page As System.Web.UI.Page)
        Try
            ' Clear all session data
            page.Session.Clear()
            page.Session.Abandon()

            ' Clear authentication cookies if any
            If page.Request.Cookies("ASP.NET_SessionId") IsNot Nothing Then
                Dim cookie As New HttpCookie("ASP.NET_SessionId")
                cookie.Expires = DateTime.Now.AddDays(-1)
                page.Response.Cookies.Add(cookie)
            End If

            ' Add cache control headers
            page.Response.Cache.SetCacheability(HttpCacheability.NoCache)
            page.Response.Cache.SetExpires(DateTime.Now.AddDays(-1))
            page.Response.Cache.SetNoStore()

        Catch ex As Exception
            ' Log error but continue
        End Try
    End Sub

    ' Generate secure random token for additional security
    Public Shared Function GenerateSecureToken() As String
        Dim rng As RNGCryptoServiceProvider = Nothing
        Try
            rng = New RNGCryptoServiceProvider()
            Dim tokenBytes(31) As Byte
            rng.GetBytes(tokenBytes)
            Return Convert.ToBase64String(tokenBytes)
        Finally
            If rng IsNot Nothing Then
                ' In .NET 3.5, RNGCryptoServiceProvider doesn't have Clear method
                ' The object will be disposed by garbage collector
                rng = Nothing
            End If
        End Try
    End Function

    ' Log security events (basic implementation)
    Public Shared Sub LogSecurityEvent(ByVal eventType As String, ByVal userId As String, ByVal details As String)
        Try
            ' In a production environment, this should log to a secure audit trail
            ' For now, we'll use a simple database log
            Dim Cn As New OleDbConnection
            Dim Cmd As New OleDbCommand

            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn

            ' Create security log table if it doesn't exist
            Try
                Cmd.CommandText = "CREATE TABLE security_log (id COUNTER PRIMARY KEY, event_time DATETIME, event_type VARCHAR(50), user_id VARCHAR(50), details MEMO, ip_address VARCHAR(50))"
                Cmd.ExecuteNonQuery()
            Catch
                ' Table already exists
            End Try

            ' Insert log entry
            Cmd.Parameters.Clear()
            Cmd.CommandText = "INSERT INTO security_log (event_time, event_type, user_id, details, ip_address) VALUES (?, ?, ?, ?, ?)"
            Cmd.Parameters.Add("@event_time", OleDbType.Date).Value = DateTime.Now
            Cmd.Parameters.Add("@event_type", OleDbType.VarChar).Value = eventType
            Cmd.Parameters.Add("@user_id", OleDbType.VarChar).Value = userId
            Cmd.Parameters.Add("@details", OleDbType.LongVarChar).Value = details
            Cmd.Parameters.Add("@ip_address", OleDbType.VarChar).Value = HttpContext.Current.Request.UserHostAddress
            Cmd.ExecuteNonQuery()

            Cn.Close()

        Catch ex As Exception
            ' Don't fail the application if logging fails
        End Try
    End Sub

    ' Input sanitization for database queries
    Public Shared Function SanitizeForDatabase(ByVal input As String) As String
        If String.IsNullOrEmpty(input) Then Return String.Empty

        ' Remove SQL injection patterns
        input = input.Replace("'", "''")
        input = input.Replace("--", "")
        input = input.Replace("/*", "")
        input = input.Replace("*/", "")
        input = input.Replace(";", "")

        Return input.Trim()
    End Function

    ' Validate user permissions
    Public Shared Function HasPermission(ByVal session As HttpSessionState, ByVal requiredModule As String, ByVal requiredAccess As Integer) As Boolean
        Try
            If session("MODUL") Is Nothing OrElse session("AKSES") Is Nothing Then
                Return False
            End If

            Dim userModules As String = session("MODUL").ToString()
            Dim userAccess As Integer = CInt(session("AKSES"))

            ' Check if user has required access level
            If userAccess < requiredAccess Then
                Return False
            End If

            ' Check module permissions (simplified check)
            ' In a real implementation, this would be more sophisticated
            Return True

        Catch ex As Exception
            Return False
        End Try
    End Function

End Class
