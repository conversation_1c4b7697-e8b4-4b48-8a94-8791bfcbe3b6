﻿Public Partial Class Main
    Inherits System.Web.UI.MasterPage

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Timer1.Enabled = False
    End Sub

    Protected Sub Menu1_MenuItemClick(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.MenuEventArgs) Handles Menu1.MenuItemClick
        'Dim a As TextBox
        'a = ContentPlaceHolder1.FindControl("textbox1")
        'a.Text = "WaLLa!"

        Select Case e.Item.Value

            Case "b1"
                If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
                Session("PINDA") = False
                Response.Redirect("p2_daftar.aspx")
            Case "c6"
                If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
                If Not Akses_Pg("P3", "Slip", Session("AKSES"), Session("MODUL")) Then
                    Session("Msg_Tajuk") = "PENDAFTARAN TPC"
                    Session("Msg_Isi") = "Akses Terhad"
                    Response.Redirect("p0_Mesej.aspx")
                    Response.Flush()
                    Exit Sub
                End If
                'Response.Redirect("p4_cetak_tpc.aspx")
                Session("SPMJ_PRINT") = "arsb_tpc"
                Response.Redirect("blank.aspx")
            Case "b5"
                If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
                If Not Akses_Pg("P2", "Slip", Session("AKSES"), Session("MODUL")) Then
                    Session("Msg_Tajuk") = "PENDAFTARAN PENUH"
                    Session("Msg_Isi") = "Akses Terhad"
                    Response.Redirect("p0_Mesej.aspx")
                    Response.Flush()
                    Exit Sub
                End If
                Session("SPMJ_PRINT") = "arsb_penuhs"
                Response.Redirect("blank.aspx")
            Case "b7"
                If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
                If Not Akses_Pg("P2", "Slip", Session("AKSES"), Session("MODUL")) Then
                    Session("Msg_Tajuk") = "PENDAFTARAN PENUH"
                    Session("Msg_Isi") = "Akses Terhad"
                    Response.Redirect("p0_Mesej.aspx")
                    Response.Flush()
                    Exit Sub
                End If
                Session("SPMJ_PRINT") = "arsb_penuht"
                Response.Redirect("blank.aspx")
            Case "b8"
                If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
                If Not Akses_Pg("P2", "Foto", Session("AKSES"), Session("MODUL")) Then
                    Session("Msg_Tajuk") = "PENDAFTARAN PENUH"
                    Session("Msg_Isi") = "Akses Terhad"
                    Response.Redirect("p0_Mesej.aspx")
                    Response.Flush()
                    Exit Sub
                End If
                Session("SPMJ_PRINT") = "arsb_penuhfoto"
                Response.Redirect("blank.aspx")
            Case "c3"
                If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
                If Not Akses_Pg("P3", "Foto", Session("AKSES"), Session("MODUL")) Then
                    Session("Msg_Tajuk") = "PENDAFTARAN TPC"
                    Session("Msg_Isi") = "Akses Terhad"
                    Response.Redirect("p0_Mesej.aspx")
                    Response.Flush()
                    Exit Sub
                End If
                Session("SPMJ_PRINT") = "arsb_tpcfoto"
                Response.Redirect("blank.aspx")
            Case "d5"
                If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
                If Not Akses_Pg("P4", "Slip", Session("AKSES"), Session("MODUL")) Then
                    Session("Msg_Tajuk") = "PEMBAHARUAN APC"
                    Session("Msg_Isi") = "Akses Terhad"
                    Response.Redirect("p0_Mesej.aspx")
                    Response.Flush()
                    Exit Sub
                End If
                Session("SPMJ_PRINT") = "arsb_apc"
                Response.Redirect("blank.aspx")
            Case "d7"
                If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
                If Not Akses_Pg("P4", "Slip2", Session("AKSES"), Session("MODUL")) Then
                    Session("Msg_Tajuk") = "PEMBAHARUAN APC"
                    Session("Msg_Isi") = "Akses Terhad"
                    Response.Redirect("p0_Mesej.aspx")
                    Response.Flush()
                    Exit Sub
                End If
                Session("SPMJ_PRINT") = "arsb_2apc"
                Response.Redirect("blank.aspx")
            Case "a6"
                If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
                If Not Akses_Pg("P1", "Slip_No", Session("AKSES"), Session("MODUL")) Then
                    Session("Msg_Tajuk") = "PEPERIKSAAN"
                    Session("Msg_Isi") = "Akses Terhad"
                    Response.Redirect("p0_Mesej.aspx")
                    Response.Flush()
                    Exit Sub
                End If
                Session("SPMJ_PRINT") = "arsb_ag"
                Response.Redirect("blank.aspx")
            Case "a11"
                If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
                If Not Akses_Pg("P1", "Slip_Keputusan", Session("AKSES"), Session("MODUL")) Then
                    Session("Msg_Tajuk") = "PEPERIKSAAN"
                    Session("Msg_Isi") = "Akses Terhad"
                    Response.Redirect("p0_Mesej.aspx")
                    Response.Flush()
                    Exit Sub
                End If
                Session("SPMJ_PRINT") = "arsb_kpts"
                Response.Redirect("blank.aspx")
                'Add nurses registration list 15122017
            Case "b9"
                Response.Redirect("P2_SN_Penuh2.aspx")
            Case "c1"
                Session("PINDA") = False
                Response.Redirect("p4_daftar.aspx")
            Case "c10"
                Response.Redirect("LP_STAT_TPC_Warga.aspx")
            Case "c11"
                Response.Redirect("LP_STAT_TPC_Majikan.aspx")
            Case "c12"
                Response.Redirect("LP_STAT_TPC_Jantina.aspx")
            Case "c13"
                Response.Redirect("LP_STAT_TPC_Jawatan.aspx")
                'Case "c11"
                '    Response.Redirect("LP_STAT_TPC_Belum_Institusi.aspx")
                'Case "c12"
                '    Response.Redirect("LP_STAT_TPC_Belum_Jantina.aspx")
                'Case "c13"
                '    Response.Redirect("LP_STAT_TPC_Belum_Jawatan.aspx")
            Case "c14"
                Response.Redirect("LP_STAT_TPC_Lulus_JT.aspx")
            Case "c15"
                Response.Redirect("LP_STAT_TPC_Lulus_LE.aspx")
            Case "c16"
                Response.Redirect("LP_STAT_TPC_Lulus_PJ.aspx")
            Case "c17"
                Response.Redirect("LP_STAT_TPC_Lulus_IK.aspx")
            Case "c18"
                Response.Redirect("LP_STAT_TPC_Lulus_Warga.aspx")
                'Peperiksaan JWTA
            Case "c19"
                Response.Redirect("P4_Peperiksaan_Daftar.aspx")
            'Case "c20"
            '    Response.Redirect(".aspx")
            'Case "c21"
            '    Response.Redirect(".aspx")
            Case "d2"
                Response.Redirect("P3_APC_Cari2.aspx") 'add search for KPSL and JM 09072024 - OSH 
            Case "d8"
                Response.Redirect("P3_RET_Batal.aspx") 'add RON cancelation 02092020 - OSH 
            Case "d10"
                'Response.Write("<script language='javascript'>win=window.open('P3_Lpr_APC.aspx',null,'width=600,height=400,top=250,left=250','true');</script>")
                'Response.Redirect("P3_ST_APC.aspx")
                Response.Redirect("P3_ST_APC_2.aspx")
            Case "d11"
                Response.Redirect("LP_STAT_APC_Bangsa.aspx")
            Case "d12"
                Response.Redirect("LP_STAT_APC_Jantina.aspx")
            Case "d13"
                Response.Redirect("LP_STAT_APC_Sektor.aspx")
            Case "d14"
                Response.Redirect("LP_STAT_APC_Jawatan.aspx")
            Case "d15"
                Response.Redirect("LP_STAT_APC_Tajaan.aspx")
            Case "d16"
                Response.Redirect("LP_STAT_APC_Gred.aspx")
            Case "d17"
                Response.Redirect("LP_STAT_APC_Kekal_Nama.aspx")
            Case "d18"
                Response.Redirect("LP_STAT_APC_Tempoh.aspx")
            Case "d19"
                Response.Redirect("LP_RET_Negara.aspx")
            Case "d20"
                Response.Redirect("LP_RET_Sebab.aspx")
                'MONTHLY RON BY CATEGEORY 07072020 - OSH 
            Case "d21"
                Response.Redirect("P3_STAT_RON_CATEGEOTY_J.aspx")
                'MONTHLY RON BASED ON YEARS OF RETAIN 07072020 - OSH 
            Case "d22"
                Response.Redirect("P3_STAT_RON_YEARS_DETAIL_J.aspx")
            Case "z1"
                'Comment Original 23092020 - OSH 
                'Response.Redirect("pn_pengguna.aspx")
                'IMPROVE ACL 23092020 - OSH
                Response.Redirect("pn_pengguna2.aspx")
            Case "z1a"
                Response.Redirect("pn_pengguna_kolej.aspx")
            Case "z2"
                'Comment Original 05012024  - OSH
                Response.Redirect("pn_siri.aspx")
                'Enroll 05012024 - OSH
                'Response.Redirect("pn_siri2.aspx")
            Case "z3"
                Response.Redirect("pn_kolej.aspx")
            Case "z3a"
                Response.Redirect("pn_nota_kolej.aspx")
            Case "z4"
                Response.Redirect("pn_nopd.aspx")
            Case "z5"
                Response.Redirect("pn_negara.aspx")
            Case "z6"
                Response.Redirect("pn_etnik.aspx")
            Case "z7"
                Response.Redirect("pn_disiplin.aspx")
            Case "z8"
                Response.Redirect("pn_gred.aspx")
            Case "z9"
                Response.Redirect("pn_tajaan.aspx")
               'Add Facility 08052024 - OSH  
            Case "z10"
                Response.Redirect("PN_Tpt_Amalan_J.aspx")
            Case "z12"
                Response.Redirect("pn_noapc.aspx")
            Case "z14"
                Response.Redirect("pn_cpd.aspx")
            Case "z15"
                Response.Redirect("pn_ikhtisas.aspx")
            Case "z16"
                Response.Redirect("pn_akademik.aspx")
            Case "z19"
                Response.Redirect("pn_sebab_pengekalan.aspx")
            Case "z20"
                Response.Redirect("pn_pwd.aspx")
            Case "z21"
                Response.Redirect("pn_subjek.aspx")
            Case "d6"
                Response.Redirect("p3_cetak.aspx")
                'Session("SPMJ_PRINT") = "apc_akuan"
                'Response.Redirect("blank.aspx")
            Case "a120"
                'Comment Original 21082018 - OSH 
                'Response.Redirect("p1_calonbaru.aspx")
                'CALON LULUS 21082018 - OSH 
                Response.Redirect("RP_STAT_EXAM_Lulus.aspx")
            Case "a121"
                'comment original 17082018 - OSH
                'Response.Redirect("p1_calonulang.aspx")
                'CALON ULANGAN 21082018 - OSH 
                Response.Redirect("RP_Calon_Ulangan.aspx")
            Case "a123"
                'COMMENT ORIGINAL 17082018 -OSH
                'Response.Redirect("p1_calontumpang.aspx")
                'CALON TUMPANG 17082018 - OSH
                Response.Redirect("RP_Calon_Tumpang.aspx")
            Case "a122"
                Response.Redirect("p1_statxm.aspx")
            Case "a124"
                Response.Redirect("p1_calonmarkah.aspx")
                'Comment Original 14092018
                'Calon Gagal 01112017
                'Case "a125"
                '    Response.Redirect("PNR_Calon_G.aspx")
                'Add Fail Stat 14122017
            Case "a126"
                Response.Redirect("RP_STAT_EXAM_Gagal.aspx")
                'Add New Pass Stat 15122017
            Case "a127"
                Response.Redirect("RP_STAT_EXAM_Lulus.aspx")
            Case "a128"
                Response.Redirect("RP_STAT_EXAM_Markah_R.aspx")
            Case "a129"
                Response.Redirect("RP_STAT_EXAM_Markah_T.aspx")
            Case "a130"
                Response.Redirect("RP_Calon_Tumpang.aspx")
            Case "a131"
                Response.Redirect("RP_Calon_Ulangan.aspx")
            Case Else
                Response.Redirect("blank.aspx")
        End Select
    End Sub

    'Private Sub Timer1_Tick(ByVal sender As Object, ByVal e As System.EventArgs) Handles Timer1.Tick
    '    Response.Redirect("blank.aspx")
    '    'Timer1.Enabled = False
    'End Sub
End Class