﻿Imports System
Imports System.Data
Imports System.Configuration
Imports System.IO
Imports System.Web
Imports System.Web.Security
Imports System.Web.UI
Imports System.Web.UI.WebControls
Imports System.Web.UI.WebControls.WebParts
Imports System.Web.UI.HtmlControls

Public Class GridViewExportUtil

    Public Shared Sub Export(ByVal fileName As String, ByVal gv As GridView)
        HttpContext.Current.Response.Clear()
        HttpContext.Current.Response.AddHeader("content-disposition", String.Format("attachment; filename={0}", fileName))
        HttpContext.Current.Response.ContentType = "application/ms-excel"
        Dim sw As StringWriter = New StringWriter
        Dim nokp As Int16 = 0, id_xm As Int16 = 0, mark As Int16 = 0, ag As Int16 = 0

        Dim htw As HtmlTextWriter = New HtmlTextWriter(sw)
        '  Create a form to contain the grid
        Dim table As Table = New Table
        table.GridLines = gv.GridLines
        'remove any hidden id by azidi

        For i As Int16 = gv.HeaderRow.Cells.Count - 1 To 0 Step -1
            If (InStr(gv.HeaderRow.Cells(i).Text, "ID_XM") Or InStr(gv.HeaderRow.Cells(i).Text, "Id_XM") Or InStr(gv.HeaderRow.Cells(i).Text, "id_xm")) Then id_xm = i : gv.HeaderRow.Cells.RemoveAt(i)
            'If (InStr(gv.HeaderRow.Cells(i).Text, "ID_XM")) Then gv.Columns.RemoveAt(i)
        Next

        '  add the header row to the table
        If (Not (gv.HeaderRow) Is Nothing) Then
            GridViewExportUtil.PrepareControlForExport(gv.HeaderRow)
            table.Rows.Add(gv.HeaderRow)
            For i As Int16 = 0 To gv.HeaderRow.Cells.Count - 1
                If (InStr(gv.HeaderRow.Cells(i).Text, "AG") > 0 Or InStr(gv.HeaderRow.Cells(i).Text, "ANGKA GILIRAN") > 0) Then ag = i : Exit For
            Next
        End If
        If (Not (gv.HeaderRow) Is Nothing) Then
            GridViewExportUtil.PrepareControlForExport(gv.HeaderRow)
            table.Rows.Add(gv.HeaderRow)
            For i As Int16 = 0 To gv.HeaderRow.Cells.Count - 1
                If (InStr(gv.HeaderRow.Cells(i).Text, "KP") > 0 Or InStr(gv.HeaderRow.Cells(i).Text, "KAD PENGENALAN") > 0) Then nokp = i : Exit For
            Next
        End If
        If (Not (gv.HeaderRow) Is Nothing) Then
            GridViewExportUtil.PrepareControlForExport(gv.HeaderRow)
            table.Rows.Add(gv.HeaderRow)
            For i As Int16 = 0 To gv.HeaderRow.Cells.Count - 1
                If InStr(gv.HeaderRow.Cells(i).Text, "MARKAH") > 0 Then mark = i : Exit For
            Next
        End If
        '  add each of the data rows to the table
        For Each row As GridViewRow In gv.Rows
            GridViewExportUtil.PrepareControlForExport(row)
            If ag > 0 Then gv.Rows.Item(row.DataItemIndex).Cells(ag).Text = "=""" + gv.Rows.Item(row.DataItemIndex).Cells(ag).Text + """ "
            If nokp > 0 Then gv.Rows.Item(row.DataItemIndex).Cells(nokp).Text = "=""" + gv.Rows.Item(row.DataItemIndex).Cells(nokp).Text + """ " '"=text(" + gv.Rows.Item(row.DataItemIndex).Cells(nokp).Text + ",""####00000000"")" ####00000000
            If mark > 0 Then gv.Rows.Item(row.DataItemIndex).Cells(mark).Text = "=""" + gv.Rows.Item(row.DataItemIndex).Cells(mark).Text + """ "
            If id_xm > 0 Then row.Cells.RemoveAt(id_xm)
            table.Rows.Add(row)
        Next
        '  add the footer row to the table
        If (Not (gv.FooterRow) Is Nothing) Then
            GridViewExportUtil.PrepareControlForExport(gv.FooterRow)
            table.Rows.Add(gv.FooterRow)
        End If
        '  render the table into the htmlwriter
        table.RenderControl(htw)
        '  render the htmlwriter into the response
        HttpContext.Current.Response.Write(sw.ToString)
        HttpContext.Current.Response.End()
    End Sub

    ' Replace any of the contained controls with literals
    Private Shared Sub PrepareControlForExport(ByVal control As Control)
        Dim i As Integer = 0
        Do While (i < control.Controls.Count)
            Dim current As Control = control.Controls(i)
            If (TypeOf current Is LinkButton) Then
                control.Controls.Remove(current)
                control.Controls.AddAt(i, New LiteralControl(CType(current, LinkButton).Text))
            ElseIf (TypeOf current Is ImageButton) Then
                control.Controls.Remove(current)
                control.Controls.AddAt(i, New LiteralControl(CType(current, ImageButton).AlternateText))
            ElseIf (TypeOf current Is HyperLink) Then
                control.Controls.Remove(current)
                control.Controls.AddAt(i, New LiteralControl(CType(current, HyperLink).Text))
            ElseIf (TypeOf current Is DropDownList) Then
                control.Controls.Remove(current)
                control.Controls.AddAt(i, New LiteralControl(CType(current, DropDownList).SelectedItem.Text))
            ElseIf (TypeOf current Is CheckBox) Then
                control.Controls.Remove(current)
                control.Controls.AddAt(i, New LiteralControl(CType(current, CheckBox).Checked))
                'TODO: Warning!!!, inline IF is not supported ?
            End If
            If current.HasControls Then
                GridViewExportUtil.PrepareControlForExport(current)
            End If
            i = (i + 1)
        Loop
    End Sub

    'Private Shared Sub PrepareControlForExportMaster(ByVal control As Control, ByVal ListColumnsToRemove As Int16)
    '    control.Controls.RemoveAt(ListColumnsToRemove)
    '    GridViewExportUtil.PrepareControlForExport(control)
    'End Sub

End Class
