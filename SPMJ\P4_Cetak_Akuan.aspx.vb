﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm49
    Inherits System.Web.UI.Page

    Public x As String

    Public Sub Surat_Individu_Akuan()
        x = ""
        x += Header_Surat(1, 1)

        'Rujukan
        x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 8pt; '><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 8pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 8pt; color:#151B8D;'> Your Ref :</td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 8pt; color:#000000;'> </td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 8pt;'></td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 8pt; color:#151B8D;'> Our Ref &nbsp; :</td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 8pt; color:#000000;'> " + Replace(Session("tpc_nopd"), "PC-", "") + "</td>"
        x += "</tr><tr>"
        x += "<td style='width:57.12%;border: none; margin-left: 0px; font-family: Arial; font-size: 8pt;'></td>"
        x += "<td style='border: none; margin-right: 0px; font-family: Arial; font-size: 8pt; color:#151B8D;'> Date &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : </td>"
        x += "<td style='border: none; margin-left: 0px; font-family: Arial; font-size: 8pt; color:#000000;'> " + Now.ToLongDateString + "</td>"
        x += "</tr></table>"

        'Penerima
        x += "<div style='font-family: Arial; font-size: 8pt;'>"
        x += "<br/>" + StrConv(Session("tpc_nama"), VbStrConv.ProperCase)
        x += "<br/>" + StrConv(Session("tpc_majikan"), VbStrConv.ProperCase)
        x += "<br/>" + StrConv(Session("tpc_alamat"), VbStrConv.ProperCase)
        If Session("tpc_alamat1") = "" Or Session("tpc_alamat1") = "&nbsp;" Then  Else x += "<br/>" + StrConv(Session("tpc_alamat1"), VbStrConv.ProperCase)
        If Session("tpc_alamat2") = "" Or Session("tpc_alamat2") = "&nbsp;" Then  Else x += "<br/>" + StrConv(Session("tpc_alamat2"), VbStrConv.ProperCase)
        x += "<br/>" + Session("tpc_poskod") + " " + StrConv(Session("tpc_bandar"), VbStrConv.ProperCase)
        x += "<br/>" + StrConv(Session("tpc_negeri"), VbStrConv.ProperCase) + "."

        x += "<br/><br/>"
        x += "<div style='font-family: Arial; font-size: 8pt; text-align:justify;'><b>TEMPORARY PRACTISING CERTIFICATE."
        x += "</b></div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 8pt; text-align:justify;'>With reference to the above and your application."
        x += "</div>"

        x += "<div style='font-family: Arial; font-size: 8pt; text-align:justify;'>"
        x += "<br/>2.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Enclose herewith are the following :"
        x += "</div>"

        x += "<div style='font-family: Arial; font-size: 8pt;'>"
        x += "<br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;i.&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Temporary Practising Certificate No:&nbsp; &nbsp; &nbsp;<b>" + Replace(Session("tpc_nopd"), "PC-", "") + "</b>&nbsp; &nbsp; &nbsp;&nbsp; &nbsp; Expiry Date:&nbsp; &nbsp; &nbsp;<b>" + Session("tpc_tkh") + " - " + Session("tkh_tamat") + "</b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 8pt;'>"
        x += "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ii.&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Receipt No &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;<b>" + Session("tpc_noresit") + " (for amount of RM 70.00)</b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 8pt; text-align:justify;'>"
        If Chk_Akhir.Checked Then
            x += "3.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>You are reminded that this is the final year for you to renew your TPC.</b>"
        Else
            x += "3.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>This TPC is valid for a period of not exceeding twelve (12) months. You are required to renew your TPC two (2) months before expiry date as failure of which, the Nursing Board Malaysia can cancel your TPC.</b>"
        End If
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 8pt; text-align:justify;'>"
        x += "4.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Kindly acknowledge receipt by completing the form below and return to Nursing Board Malaysia as soon as possible."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 8pt;'>"
        x += "Thank you."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 8pt;'>"
        x += "Registrar"
        x += "<br>Nursing Board Malaysia"
        x += "<br>Ministry Of Health."
        x += "</div>"
        x += "<br><div style='font-family: Arial; font-size: 8pt; text-align:center;'>"
        x += "<br>(This letter is computer generated. No signature is required.)"
        x += "<br><b>............................................................................Cut Here.................................................................................</b>"
        x += "</div>"


        ' Cut Here
        x += "<table style='border: none; margin-left: 0px; font-family: Arial; font-size: 8pt;'>"
        x += "<tr>"
        x += "<td><div style ='margin-left:3.0in' ;align = 'left'>Name</div></td>"
        x += "<td><div align = 'left'>:.....................................................</div></td>"
        x += "<tr>"
        x += "<td><div style ='margin-left:3.0in' ;align = 'left'>Place of Work</div></td>"
        x += "<td><div align = 'left'>:....................................................."
        x += "</div></td>"
        x += "</tr>"
        x += "</table>"


        x += "<div style='font-family: Arial; font-size: 8pt;'>"
        x += "<br>Registrar,"
        x += "<br>Nursing Board Malaysia,"
        x += "<br>Ministry of Health Malaysia,"
        x += "<br>Level 3, Block E1, Parcel E, Precint 1,"
        x += "<br>Federal Government Administrative Centre,"
        x += "<br>62590 Putrajaya."
        x += "<br></div>"

        x += "<br/>"
        x += "<div  style='font-family: Arial; font-size: 8pt;'>Madam,"
        x += "</div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 8pt; text-align:justify;'><b>TEMPORARY PRACTISING CERTIFICATE."
        x += "</b></div>"

        x += "<br/>"
        x += "<div style='font-family: Arial; font-size: 8pt; text-align:justify;'>This is confirm that I have received the following:-"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 8pt;'>"
        x += "   &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; i.&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Temporary Practising Certificate No ....................................&nbsp; &nbsp; &nbsp;Expiry Date:......................."
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 8pt;'>"
        x += "   &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;ii.&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Receipt .................................................................................&nbsp; &nbsp; &nbsp;<b>(for amount of RM70.00)</b>"
        x += "</div>"

        x += "<br/><div style='font-family: Arial; font-size: 8pt;'>"
        x += "   &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Signature:..............................................................................&nbsp; &nbsp; &nbsp;Date:.................................."
        x += "</div>"

        x += Footer_Surat()

        'Response.Write(x)
        'Exit Sub

        Dim fileName As String = "Surat.doc"
        Response.Clear()
        Response.Buffer = True
        ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)

        'SQL = "select distinct NAMA, tp.NOKP as 'NO. KP/PASPORT', sm_alamat, sm_poskod, sm_bandar, dc_negeri, nopd as 'NOPD', tpc_noresit, CONVERT(varchar(12), tpc_tkh, 103) as 'TARIKH TPC', " & _
        '      "CONVERT(varchar(12), tkh_tamat, 103) as 'TARIKH TAMAT' from jt_tpc tp inner join jt_tpc_tpc jtt on tp.nokp=jtt.nokp and year(jtt.tpc_tkh)=year(getdate()) inner join " & _
        '      "jt_tpc_majikan jtm on tp.nokp=jtm.nokp left outer join pn_negeri pn on tp.sm_negeri=pn.id_negeri where " & X & "  order by nama"
        SQL = "select distinct NAMA, tp.NOKP as 'NO. KP/PASPORT', sm_alamat, sm_poskod, sm_bandar, dc_negeri, nopd as 'NOPD', tpc_noresit, CONVERT(varchar(12), tpc_tkh, 103) as 'TARIKH TPC', " & _
              "CONVERT(varchar(12), tkh_tamat, 103) as 'TARIKH TAMAT',pta.dc_amalan, pta.Alamat,pta.Alamat1,pta.Alamat2,pta.Poskod,pta.Bandar,Dc_NEGERI from jt_tpc tp " & _
              "inner join jt_tpc_tpc jtt on tp.nokp=jtt.nokp and year(jtt.tpc_tkh)=year(getdate()) inner join jt_tpc_majikan jtm on tp.nokp=jtm.nokp left join pn_tpt_amalan pta on jtm.Tpt_Amalan=pta.Id_Amalan " & _
              "left outer join pn_negeri pn on pta.negeri=pn.id_negeri where " & X & " order by nama"
        Tb = "tmp_penuh"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Tx_Nama.Text.Trim = "" And Tx_NoKP.Text.Trim = "" And Tx_NoPd.Text.Trim = "" Then Exit Sub
        Cari("nama like '" & Tx_Nama.Text & "%' and tp.nokp like '" & Tx_NoKP.Text & "%' and nopd like '" & Tx_NoPd.Text & "%'")
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(4).Visible = False
        e.Row.Cells(5).Visible = False
        e.Row.Cells(6).Visible = False
        e.Row.Cells(7).Visible = False
        e.Row.Cells(9).Visible = False
        e.Row.Cells(10).Visible = False
        e.Row.Cells(12).Visible = False
        e.Row.Cells(13).Visible = False
        e.Row.Cells(14).Visible = False
        e.Row.Cells(15).Visible = False
        e.Row.Cells(16).Visible = False
        e.Row.Cells(17).Visible = False
        e.Row.Cells(18).Visible = False
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Session("tpc_nama") = Gd.SelectedRow.Cells(2).Text
        Session("tpc_nokp") = Gd.SelectedRow.Cells(3).Text        
        Session("tpc_nopd") = "TPC-" + Gd.SelectedRow.Cells(8).Text
        Session("tpc_noresit") = Gd.SelectedRow.Cells(9).Text
        Session("tpc_tkh") = Gd.SelectedRow.Cells(10).Text
        Session("tkh_tamat") = Gd.SelectedRow.Cells(11).Text
        Session("tpc_majikan") = Gd.SelectedRow.Cells(12).Text
        Session("tpc_alamat") = Gd.SelectedRow.Cells(13).Text
        Session("tpc_alamat1") = Gd.SelectedRow.Cells(14).Text
        Session("tpc_alamat2") = Gd.SelectedRow.Cells(15).Text
        Session("tpc_poskod") = Gd.SelectedRow.Cells(16).Text
        Session("tpc_bandar") = Gd.SelectedRow.Cells(17).Text
        Session("tpc_negeri") = Gd.SelectedRow.Cells(18).Text
        Surat_Individu_Akuan()
    End Sub
End Class