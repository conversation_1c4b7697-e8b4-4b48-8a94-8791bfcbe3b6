﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_XM_Distribution
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Laporan", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'KATEGORI
        Cb_Kategori.Items.Clear()
        Cb_Kategori.Items.Add("(SEMUA SEKTOR)")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = ""
        Cb_Kategori.Items.Add("KERAJAAN")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "1"
        Cb_Kategori.Items.Add("IPTA")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "3"
        Cb_Kategori.Items.Add("SWASTA")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "2"

        'KOLEJ
        Cmd.CommandText = "select dc_kolej, id_kolej from pn_kolej where (jenis < 3) order by dc_kolej"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("(SEMUA KOLEJ)")
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        'KURSUS
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("(SEMUA JENIS PENDAFTARAN)")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = ""
        Cb_Kursus.Items.Add("JURURAWAT BERDAFTAR")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "1"
        Cb_Kursus.Items.Add("JURURAWAT MASYARAKAT")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "2"
        Cb_Kursus.Items.Add("PENOLONG JURURAWAT")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "3"
        Cb_Kursus.Items.Add("KEBIDANAN I")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "4"

    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click       
        Dim SQL, SQL_pilih, X, Id_XM As String
        Dim Jwtn, Jwtn2, Jwtn3 As Integer

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'ID XM
        X = "" : Id_XM = ""
        If Cb_Kolej.SelectedIndex > 0 Then X += " where p.id_kolej=" & Cb_Kolej.SelectedValue
        If Cb_Kursus.SelectedIndex > 0 Then
            If X.Length = 0 Then X += " where " Else X += " and "
            X += " j_xm=" & Cb_Kursus.SelectedIndex
        End If
        If Tx_Tahun.Text <> "" And Tx_Siri.Text <> "" Then
            If X.Length = 0 Then
                X += " where tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text
            Else
                X += " and tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text
            End If
        End If

        If X = "" Then
        Else
            SQL = "select top 1 px.id_xm from xm_calon xc  inner join pn_xm px on xc.id_xm=px.id_xm inner join pelatih p on p.nokp=xc.nokp " & _
                  " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM " & _
                   "left join pn_kolej pk on pk.id_kolej=xc.id_pusat " & X & " order by tahun desc, siri desc, j_xm desc"
            Cmd.CommandText = SQL : Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then Id_XM = Rdr(0) : X = " and xc.id_xm=" & Id_XM Else X = ""
            Rdr.Close()
        End If

        SQL_pilih = "" : SQL_pilih += X
        'update by azidi 13/08/2012
        'If Cb_Kategori.SelectedIndex < 1 Then SQL += "" Else SQL_pilih += " and pnk.jenis=" & Cb_Kategori.SelectedIndex 'Kategori
        If Cb_Kategori.SelectedIndex < 1 Then
            SQL_pilih += ""
        ElseIf Cb_Kategori.SelectedIndex = 1 Then
            SQL_pilih += " and ( pnk.id_KOLEJ>=0 and  pnk.id_KOLEJ<=100) " ' & Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 2 Then
            SQL_pilih += "  and ( pnk.id_KOLEJ>=101 and  pnk.id_KOLEJ<=199) " '& Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 3 Then
            SQL_pilih += "  and ( pnk.id_KOLEJ>=200  and  pnk.JENIS<3) " ' Cb_Kategori.SelectedValue
        End If

        If Cb_Kolej.SelectedIndex < 1 Then SQL_pilih += "" Else SQL_pilih += " and pnk.id_kolej=" & Cb_Kolej.SelectedValue 'Kolej        
        If Cb_Kursus.SelectedIndex < 1 Then
        Else
            If Cb_Kursus.SelectedIndex = 1 Then Jwtn = 1 : Jwtn2 = 5 : Jwtn3 = 8
            If Cb_Kursus.SelectedIndex = 2 Then Jwtn = 2 : Jwtn2 = 2 : Jwtn3 = 2
            If Cb_Kursus.SelectedIndex = 3 Then Jwtn = 3 : Jwtn2 = 3 : Jwtn3 = 3
            If Cb_Kursus.SelectedIndex = 4 Then Jwtn = 4 : Jwtn2 = 4 : Jwtn3 = 4
            SQL_pilih += " and (p.j_kursus = " & Jwtn & " or p.j_kursus = " & Jwtn2 & " or p.j_kursus = " & Jwtn3 & ")" 'kursus
        End If

        SQL = "delete from temp_ret; "
        SQL += "insert temp_ret "
        SQL += "select markah_jum as negara, count(markah_jum) as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7,null as b8, null as b9, null as b10, "
        SQL += "null as b11, null as b12,null as x, null as nokp, null as total from pelatih p inner join xm_calon xc on p.nokp=xc.nokp left outer join pn_xm px on px.id_xm=xc.id_xm "
        'Comment Ori 15092015 -OSH
        'SQL += "left outer join pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null  and  not(xc.keputusan='T') and xc.ulangan is not null " & SQL_pilih & " group by markah_jum"

        ''Fixing multiple records display per canidate 15092015 - OSH
        SQL += "left outer join pn_kolej pnk on p.id_kolej=pnk.id_kolej where p.nokp is not null and xc.keputusan is not null  and  not(xc.keputusan='T') and xc.ulangan is not null and p.status is null " & SQL_pilih & " group by markah_jum"
        SQL += "; "

        Cmd.CommandText = SQL
        Cmd.ExecuteNonQuery()
        Cn.Close()
        Jana()
    End Sub

    Public Sub Jana()        
        Dim Tajuk, Tajuk2, Tahun, Siri, X As String
        Dim i, total, low, high, percent, cumm As Double
        Tahun = "0" : Siri = "0"
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        If Tx_Tahun.Text <> "" And Tx_Siri.Text <> "" Then X = " and tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text Else X = ""

        Cmd.CommandText = "select top 1 tahun, siri, id_xm from pn_xm where j_xm=" & Cb_Kursus.SelectedIndex & X & " order by tahun desc, siri desc, j_xm desc"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            Tahun = Rdr(0)
            Siri = Rdr(1)
        End While
        Rdr.Close()

        Tajuk = "STATISTIK TABURAN MARKAH PEPERIKSAAN BAGI " & Cb_Kategori.SelectedItem.Text & ", " & Cb_Kolej.SelectedItem.Text
        Tajuk2 = "KURSUS " & Cb_Kursus.SelectedItem.Text & _
        IIf(Tahun > "", " TAHUN " & Tahun, "") & _
        IIf(Siri > "", " SIRI " & Siri, "")

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='8' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='8' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>NO</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>MARKAH</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>FREKUENSI</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>KUMULATIF TERENDAH</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>KUMULATIF TERTINGGI</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>PERATUS</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>SAHIH (%)</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>KUMULATIF (%)</td>"
        Header += "</tr>"
        Response.Write(Header)

        Cmd.CommandText = "select isnull(sum(b1),'0') as b1 from temp_ret"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            total = Rdr(0)
        End While
        Rdr.Close()

        'track temp ret
        Cmd.CommandText = "select negara, b1 from temp_ret"
        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "temp_ret")
        Rdr.Close()

        Header = ""
        For Each dr As DataRow In Ds.Tables(0).Rows
            i += 1
            Header += "<tr>"
            Header += "    <td style='vertical-align: middle; text-align: left;'>" & i & "</td> "
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(0) & "</td> "
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & dr.Item(1) & "</td> "
            low += CDbl(dr.Item(1))
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & low & "</td> "
            high = total - low
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & high & "</td> "
            percent = (CInt(dr.Item(1)) / total) * 100
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & percent & "</td> "
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & percent & "</td> "
            cumm += percent
            Header += "    <td style='vertical-align: middle; text-align: right;' >" & cumm & "</td> "
            Header += "</tr>"
        Next
        Response.Write(Header)
        Response.Write("</table>")

        Response.End()
        Response.Flush()
    End Sub

    Protected Sub Cb_Kategori_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kategori.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Dim SQL As String

        'update by azidi 13/08/2012
        'If Cb_Kategori.SelectedIndex < 1 Then SQL = "< 3" Else SQL = "=" & Cb_Kategori.SelectedValue
        If Cb_Kategori.SelectedIndex < 1 Then
            SQL = "jenis< 3"
        ElseIf Cb_Kategori.SelectedIndex = 1 Then
            SQL += " ( id_KOLEJ>=0 and  id_KOLEJ<=100) " ' & Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 2 Then
            SQL += " ( id_KOLEJ>=101 and  id_KOLEJ<=199) " '& Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 3 Then
            SQL += " ( id_KOLEJ>=200  and  JENIS<3) " ' Cb_Kategori.SelectedValue
        End If

        'KOLEJ
        Cmd.CommandText = "select dc_kolej, id_kolej from pn_kolej where " & SQL & " order by dc_kolej" 'jenis
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("(SEMUA)")
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub
End Class