﻿Imports System.Globalization
Imports System.IO
Imports System.Data
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.Text
Imports iTextSharp.text
Imports iTextSharp.text.pdf
Imports iTextSharp.text.html
Imports iTextSharp.text.html.simpleparser

Partial Public Class P3_ST_APC_2
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        'Add Secure Check Point 06072020 - OSH 
        'If Not Akses_Pg("P1", "Laporan", Session("AKSES"), Session("MODUL")) Then
        '    Session("Msg_Tajuk") = "APC"
        '    Session("Msg_Isi") = "Akses Terhad"
        '    Response.Redirect("p0_Mesej.aspx")
        '    Exit Sub
        'End If

        'Add APC year future and past 4 year 13112018 - OSH  
        For i = Now.Year + 1 To Now.Year - 5 Step -1
            Cb_Tahun.Items.Add(i)
        Next


        'Add loading 
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select Nama, id_pg as 'ID PENGGUNA' from pn_pengguna where modul like '__1%' and status ='1' order by nama"
        Rdr = Cmd.ExecuteReader()
        Cb_Pengguna.Items.Clear()
        Cb_Pengguna.Items.Add("")
        While Rdr.Read
            Cb_Pengguna.Items.Add(Rdr(0))
            Cb_Pengguna.Items.Item(Cb_Pengguna.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()
        Dim Requestor As String = "" 'Add name varible 06072020 - OSH 
        'Dim tahun As Int16  'Comment Original 13112018 - OSH

        Dim Cn3 As New OleDbConnection : Dim Cmd3 As New OleDbCommand : Dim Rdr3 As OleDbDataReader
        Cn3.ConnectionString = ServerId : Cn3.Open() : Cmd3.Connection = Cn3

        Cmd3.CommandText = "select Nama from pn_pengguna where id_pg = '" & Session("Id_PG") & "' and status ='1' order by nama"
        Rdr3 = Cmd3.ExecuteReader()
        While Rdr3.Read
            Requestor = Rdr3(0)
        End While
        Rdr3.Close()
        Cn3.Close()

        Dim ch As New Chunk("DIJANA OLEH : " & Requestor.ToString & "  PADA  " & DateAndTime.Today & "  MASA " & DateAndTime.TimeOfDay)
        Dim ph As New Phrase(ch)
        Dim p As New Paragraph()
        p.Add(ph)

        'Year Report
        If Tx_Tkh_M.Text <> "" And Tx_Tkh_A.Text <> "" And Cb_Tahun.SelectedValue <> "" And Cb_Pengguna.SelectedValue = "" Then

            'FIXING DATE FORMAT 24072018 - OSH
            Dim a1, a2 As DateTime
            Dim b1, b2 As String
            Dim provider As CultureInfo = CultureInfo.InvariantCulture

            a1 = Date.ParseExact(Tx_Tkh_M.Text, "dd/MM/yyyy", provider)
            a2 = Date.ParseExact(Tx_Tkh_A.Text, "dd/MM/yyyy", provider)

            b1 = a1.ToString("yyyy-MM-dd") 'Tarikh Mula
            b2 = a2.ToString("yyyy-MM-dd") 'Tarih Tamat

            cmd.CommandType = CommandType.StoredProcedure
            cmd.CommandText = "SPMJ_stat_apc"

            'Comment Original 13112018 - OSH
            'If Cb_Tahun.SelectedValue = "a" Then
            '    tahun = Year(Date.Now)
            'ElseIf Cb_Tahun.SelectedValue = "b" Then
            '    tahun = Year(Date.Now) + 1
            'End If
            'Session("APC_RPT") = tahun

            'Add year headed title report 18062020 - OSH  
            Session("RPT_Year") = CInt(Cb_Tahun.Text.Trim)

            'Varible for dates 06072020 - OSH
            Session("BEGIN_date") = Tx_Tkh_M.Text.Trim
            Session("END_date") = Tx_Tkh_A.Text.Trim

            cmd.Parameters.Add("@tahun_apc", SqlDbType.Int, 4).Value = CInt(Cb_Tahun.Text.Trim)

            'Comment Original 13112018 - OSH
            'cmd.Parameters.Add("@tahun_apc", SqlDbType.Int, 4).Value = tahun
            'comment Ori 24072018 -OSH
            'cmd.Parameters.Add("@tkh_apc_mula", SqlDbType.Date).Value = Tx_Tkh_M.Text
            'cmd.Parameters.Add("@tkh_apc_tamat", SqlDbType.Date).Value = Tx_Tkh_A.Text

            cmd.Parameters.Add("@tkh_apc_mula", SqlDbType.Date).Value = b1
            cmd.Parameters.Add("@tkh_apc_tamat", SqlDbType.Date).Value = b2
            cmd.Connection = con
            Try
                con.Open()
                Dim GridView1 = New GridView()
                GridView1.EmptyDataText = "No Records Found"
                GridView1.DataSource = cmd.ExecuteReader()
                GridView1.DataBind()

                'Dump to PDF
                Response.ContentType = "application/pdf"
                Response.AddHeader("content-disposition", "attachment;filename=APC_STAT.pdf")


             
                Response.Cache.SetCacheability(HttpCacheability.NoCache)
                Dim sw As New StringWriter()
                Dim hw As New HtmlTextWriter(sw)
                GridView1.RenderControl(hw)
                Dim sr As New StringReader(sw.ToString())
                Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)
                Dim htmlparser As New HTMLWorker(pdfDoc)
                'OPEN PDF writer for dumping 
                Dim pdfWrite As PdfWriter = PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
                'Add header
                Dim ev As New itsEvents
                pdfWrite.PageEvent = ev

                'Add Footer 06072020 - OSH
                Dim footerPDF As New PageFooter
                pdfWrite.PageEvent = footerPDF

                'PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
                pdfDoc.Open()
                htmlparser.Parse(sr)
                'Add generate user and datetime details 06072020 - OSH   
                pdfDoc.Add(New Paragraph(" "))
                pdfDoc.Add(p)
                pdfDoc.Close()
                Response.Write(pdfDoc)
                Response.End()
            Catch ex As Exception
                Throw ex
            Finally
                con.Close()
                con.Dispose()
            End Try

            'Add Processor Report 18102018 - OSH 
        ElseIf Tx_Tkh_M.Text <> "" And Tx_Tkh_A.Text <> "" And Cb_Tahun.SelectedValue <> "" And Cb_Pengguna.SelectedValue <> "" Then


            Dim a1, a2 As DateTime
            Dim b1, b2 As String
            Dim provider As CultureInfo = CultureInfo.InvariantCulture

            Dim Cn2 As New OleDbConnection : Dim Cmd2 As New OleDbCommand : Dim Rdr2 As OleDbDataReader
            Cn2.ConnectionString = ServerId : Cn2.Open() : Cmd2.Connection = Cn2

            'Populate session varible with values 06072020 - OSH 
            Cmd2.CommandText = "select Nama from pn_pengguna where id_pg = '" & Cb_Pengguna.Text.Trim & "' and status ='1' order by nama"
            Rdr2 = Cmd2.ExecuteReader()

            While Rdr2.Read
                Session("RPT_Processor") = Rdr2(0)
            End While
            Rdr2.Close()
            Cn2.Close()

            a1 = Date.ParseExact(Tx_Tkh_M.Text, "dd/MM/yyyy", provider)
            a2 = Date.ParseExact(Tx_Tkh_A.Text, "dd/MM/yyyy", provider)

            b1 = a1.ToString("yyyy-MM-dd") 'Tarikh Mula
            b2 = a2.ToString("yyyy-MM-dd") 'Tarih Tamat

            cmd.CommandType = CommandType.StoredProcedure
            cmd.CommandText = "SPMJ_stat_apc_PERSON"

            'Improve APC years list 13112018 - OSH
            cmd.Parameters.Add("@tahun_apc", SqlDbType.Int, 4).Value = CInt(Cb_Tahun.Text.Trim)

            'Add Page Year and person 30102019 -OSH 
            Session("RPT_Year") = CInt(Cb_Tahun.Text.Trim)
            'Session("RPT_Processor") = Cb_Pengguna.Text.Trim

            'Varible for dates 06072020 - OSH
            Session("BEGIN_date") = Tx_Tkh_M.Text.Trim
            Session("END_date") = Tx_Tkh_A.Text.Trim

            'Comment Original 13112018 - OSH
            'If Cb_Tahun.SelectedValue = "a" Then
            '    tahun = Year(Date.Now)
            'ElseIf Cb_Tahun.SelectedValue = "b" Then
            '    tahun = Year(Date.Now) + 1
            'End If
            'Session("APC_RPT") = tahun

            'cmd.Parameters.Add("@tahun_apc", SqlDbType.Int, 4).Value = tahun
            ''comment Ori 24072018 -OSH
            'cmd.Parameters.Add("@tkh_apc_mula", SqlDbType.Date).Value = Tx_Tkh_M.Text
            'cmd.Parameters.Add("@tkh_apc_tamat", SqlDbType.Date).Value = Tx_Tkh_A.Text

            cmd.Parameters.Add("@tkh_apc_mula", SqlDbType.Date).Value = b1
            cmd.Parameters.Add("@tkh_apc_tamat", SqlDbType.Date).Value = b2
            cmd.Parameters.Add("@person", SqlDbType.VarChar, 12).Value = Cb_Pengguna.SelectedValue.Trim
            cmd.Connection = con
            Try
                con.Open()
                Dim GridView1 = New GridView()
                GridView1.EmptyDataText = "No Records Found"
                GridView1.DataSource = cmd.ExecuteReader()
                GridView1.DataBind()

                'Dump to PDF
                Response.ContentType = "application/pdf"
                Response.AddHeader("content-disposition", "attachment;filename=APC_STAT_Person.pdf")
                Response.Cache.SetCacheability(HttpCacheability.NoCache)
                Dim sw As New StringWriter()
                Dim hw As New HtmlTextWriter(sw)
                GridView1.RenderControl(hw)
                Dim sr As New StringReader(sw.ToString())
                Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)
                Dim htmlparser As New HTMLWorker(pdfDoc)
                'OPEN PDF writer for dumping 
                Dim pdfWrite As PdfWriter = PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
                'Add Header 06072020 - OSH
                Dim headerPDF As New HeaderPerson
                pdfWrite.PageEvent = headerPDF

                'Add Footer 06072020 - OSH
                Dim footerPDF As New PageFooter
                pdfWrite.PageEvent = footerPDF

                'PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
                pdfDoc.Open()
                htmlparser.Parse(sr)
                pdfDoc.Close()
                Response.Write(pdfDoc)
                Response.End()

            Catch ex As Exception
                Throw ex
            Finally
                con.Close()
                con.Dispose()
            End Try

        End If
    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari2.Click
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()
        Dim tahun As Int16

        'If Tx_Tahun.Text = "" Then
        '    MsgBox("Sila isi maklumat tahun", MsgBoxStyle.Information)
        'ElseIf Tx_Tkh_Mula.Text = "" Then
        '    MsgBox("Sila isi maklumat tahun", MsgBoxStyle.Information)
        'ElseIf Tx_Tkh_Tamat.Text = "" Then
        '    MsgBox("Sila isi maklumat tahun", MsgBoxStyle.Information)
        'End If

        'FIXING DATE FORMAT 24072018 - OSH
        Dim c, c1 As DateTime
        Dim d, d1 As String
        If Tx_Tkh_M.Text.Trim <> String.Empty Then
            d = Tx_Tkh_M.Text.Trim
            c = DateTime.ParseExact(d, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            d = c.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            d = "'" & d & "'"
        Else
            c = "NULL"
        End If


        If Tx_Tkh_A.Text.Trim <> String.Empty Then
            d1 = Tx_Tkh_A.Text.Trim
            c1 = DateTime.ParseExact(d1, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            c1 = c1.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            c1 = "'" & d1 & "'"
        Else
            d1 = "NULL"
        End If

        If Cb_Tahun.SelectedValue = "a" Then
            tahun = Year(Date.Now)
        ElseIf Cb_Tahun.SelectedValue = "b" Then
            tahun = Year(Date.Now) + 1
        End If

        Dim table As New PdfPTable(3)
        table.TotalWidth = 400.0F
        table.LockedWidth = True

        Dim header As New PdfPCell(New Phrase("Header"))
        header.Colspan = 3
        table.AddCell(header)
        table.AddCell("Cell 1")
        table.AddCell("Cell 2")
        table.AddCell("Cell 3")


        Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)

        'Comment Original 09082018 - OSH
        'cmd.Parameters.Add("@tahun_apc", SqlDbType.Int, 4).Value = tahun
        'cmd.Parameters.Add("@tkh_apc_mula", SqlDbType.Date).Value = Tx_Tkh_M.Text
        'cmd.Parameters.Add("@tkh_apc_tamat", SqlDbType.Date).Value = Tx_Tkh_A.Text

        'Fixing date coversion 09082018 -OSH
        cmd.Parameters.Add("@tahun_apc", SqlDbType.Int, 4).Value = tahun
        cmd.Parameters.Add("@tkh_apc_mula", SqlDbType.Date).Value = d
        cmd.Parameters.Add("@tkh_apc_tamat", SqlDbType.Date).Value = d1
        cmd.Connection = con

        Try
            con.Open()
            Dim rdr As SqlDataReader = cmd.ExecuteReader()

            If rdr.HasRows Then
                While rdr.Read()
                    table.AddCell(rdr(0).ToString)
                    table.AddCell(rdr(1).ToString)
                    table.AddCell(rdr(2).ToString)
                End While
            End If

            'Dump to pdf
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            pdfDoc.Open()
            pdfDoc.Add(table)
            pdfDoc.Close()
            Response.ContentType = "application/pdf"
            Response.AddHeader("content-disposition", _
           "attachment;filename=NestedTable.pdf")
            Response.Cache.SetCacheability(HttpCacheability.NoCache)
            Response.Write(pdfDoc)
            Response.End()
        Catch ex As Exception

        End Try
    End Sub
End Class