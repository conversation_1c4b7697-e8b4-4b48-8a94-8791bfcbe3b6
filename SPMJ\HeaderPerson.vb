﻿Imports iTextSharp.text.pdf
Imports iTextSharp.text
Imports System.Web

Public Class HeaderPerson
    Inherits PdfPageEventHelper

    Public Overrides Sub OnStartPage(ByVal writer As iTextSharp.text.pdf.PdfWriter, ByVal document As iTextSharp.text.Document)
        Dim year As Integer
        Dim name As String
        Dim beginDate As String
        Dim endDate As String

        If Not (HttpContext.Current.Session("RPT_Year") And (HttpContext.Current.Session("RPT_Processor")) Is Nothing) Then
            'Fixing passing value year to local varible 18062020 -OSH
            year = CInt(System.Web.HttpContext.Current.Session("RPT_Year"))
            'Add name of processor to report 18062020 - OSH
            name = CStr(HttpContext.Current.Session("RPT_Processor"))

            'Add dates for report 06072020 -OSH  
            beginDate = HttpContext.Current.Session("BEGIN_date")
            endDate = HttpContext.Current.Session("END_date")


            'Dim ch As New Chunk("LAPORAN PENGELUARAN APC " & year.ToString & " OLEH " & name.ToString & "   TEMPOH (" & beginDate.ToString & " SEHINGGA " & endDate.ToString & ")")
            Dim ch As New Chunk("LAPORAN PENGELUARAN APC " & year.ToString & " OLEH " & name.ToString)
            Dim ch2 As New Chunk("TEMPOH (" & beginDate.ToString & " SEHINGGA " & endDate.ToString & ")")
            'Add Improve Paragraph - Center 30102019 - OSH  
            Dim ph As New Phrase(ch)
            'Add Phrase 06072020 - OSH 
            Dim ph2 As New Phrase(ch2)
            Dim p As New Paragraph()
            p.Add(ph)
            p.SpacingBefore = 20
            p.SpacingAfter = 20
            p.Alignment = 1 'center
            p.Add(Chunk.NEWLINE)
            p.Add(ph2)
            document.Add(p)

        End If
    End Sub

End Class
