﻿Imports System.Data.OleDb

Partial Public Class WebForm13
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        ' Validate current password input
        If String.IsNullOrEmpty(Tx_Pwd.Text) OrElse Tx_Pwd.Text.Trim() = "" Then
            Msg(Me, "Sila masukkan kata laluan semasa!")
            Tx_Pwd.Focus()
            Exit Sub
        End If

        ' Validate new password input
        If String.IsNullOrEmpty(Tx_Pwd2.Text) OrElse Tx_Pwd2.Text.Trim() = "" Then
            Msg(Me, "Sila masukkan kata laluan baru!")
            Tx_Pwd2.Focus()
            Exit Sub
        End If

        ' Validate password strength
        If Not ValidatePasswordStrength(Tx_Pwd2.Text) Then
            Msg(Me, "Kata laluan baru mesti mengandungi sekurang-kurangnya 8 aksara, termasuk huruf besar, huruf kecil, dan nombor!")
            Tx_Pwd2.Focus()
            Exit Sub
        End If

        ' Check if new password is different from current
        If Tx_Pwd.Text = Tx_Pwd2.Text Then
            Msg(Me, "Kata laluan baru mesti berbeza daripada kata laluan semasa!")
            Tx_Pwd2.Focus()
            Exit Sub
        End If

        Dim Cn As New OleDbConnection
        Dim Cmd As New OleDbCommand
        Dim Rdr As OleDbDataReader

        Try
            Cn.ConnectionString = ServerId
            Cn.Open()
            Cmd.Connection = Cn

            ' Get current password from database for verification
            Cmd.CommandText = "SELECT pwd FROM pn_pengguna WHERE id_pg = ?"
            Cmd.Parameters.Add("@id_pg", OleDbType.VarChar).Value = Session("Id_PG").ToString()
            Rdr = Cmd.ExecuteReader()

            If Rdr.Read Then
                Dim storedPassword As String = Rdr("pwd").ToString()
                Dim isCurrentPasswordValid As Boolean = False

                ' Check if stored password is hashed or plain text
                If storedPassword.Length = 64 AndAlso System.Text.RegularExpressions.Regex.IsMatch(storedPassword, "^[a-fA-F0-9]+$") Then
                    ' Hashed password - verify using hash
                    isCurrentPasswordValid = VerifyPassword(Tx_Pwd.Text, storedPassword)
                Else
                    ' Legacy plain text password - direct comparison
                    isCurrentPasswordValid = (storedPassword.ToUpper() = Tx_Pwd.Text.ToUpper())
                End If

                Rdr.Close()

                If isCurrentPasswordValid Then
                    ' Hash the new password
                    Dim hashedNewPassword As String = HashPassword(Tx_Pwd2.Text)

                    ' Update password with parameterized query
                    Cmd.Parameters.Clear()
                    Cmd.CommandText = "UPDATE pn_pengguna SET pwd = ? WHERE id_pg = ?"
                    Cmd.Parameters.Add("@pwd", OleDbType.VarChar).Value = hashedNewPassword
                    Cmd.Parameters.Add("@id_pg", OleDbType.VarChar).Value = Session("Id_PG").ToString()
                    Cmd.ExecuteNonQuery()

                    ' Clear password fields
                    Tx_Pwd.Text = ""
                    Tx_Pwd2.Text = ""

                    Msg(Me, "Kata laluan telah berjaya dikemaskini!")
                Else
                    Msg(Me, "Kata laluan semasa tidak betul!")
                    Tx_Pwd.Focus()
                End If
            Else
                Msg(Me, "Pengguna tidak dijumpai!")
            End If

        Catch ex As Exception
            Msg(Me, "Ralat sistem. Sila cuba lagi.")
        Finally
            If Not Rdr Is Nothing AndAlso Not Rdr.IsClosed Then Rdr.Close()
            If Cn.State = ConnectionState.Open Then Cn.Close()
        End Try
    End Sub
End Class