﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports System.Net
Imports System.IO

Public Class P3_APC_LANTIK_KPSL
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If IsPostBack Then Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Channel KPSL Nurses Custom Process 27062024 - OSH 
        Cmd.CommandText = "select nokp, nama from jt_penuh where j_daftar = 1 and xm_jenis = 'KPSL' and nokp= '" & Session("KPSL_IC").ToString.Trim & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read() Then
            If Not IsDBNull(Rdr("nokp")) Then Tx_NoKP.Text = Rdr("nokp") : Tx_NoKP.Enabled = False : Tx_NoKP.ReadOnly = True
            If Not IsDBNull(Rdr("nama")) Then Tx_Nama.Text = Rdr("nama") : Tx_Nama.Enabled = False : Tx_Nama.ReadOnly = True
        End If
        Rdr.Close()

        'Populate CPD from passing value 19082024 - OSH 
        If Session("KPSL_CPD") <> "" Then Tx_CPD.Text = Session("KPSL_CPD").ToString
    End Sub

    Protected Sub cmdHantar_Click(sender As Object, e As EventArgs) Handles cmdHantar.Click

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader : Dim SQL As String = ""
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'APV Year Types Variable  
        Dim aY As Int16 : Dim pCPD As Integer

        'Tarikh Lantikan JB
        Dim y As DateTime
        Dim z As String
        If Tx_Tkh_Lantik_JB.Text.Trim <> String.Empty Then
            z = Tx_Tkh_Lantik_JB.Text.Trim
            y = DateTime.ParseExact(z, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            z = y.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            z = "'" & z & "'"
        Else
            z = "NULL"
        End If

        If Tx_NoKP.Text <> "" And Tx_No_Rujukan.Text <> "" And Tx_Tkh_Lantik_JB.Text <> "" Then
            SQL = "insert into pn_kpsl_lantikan_jb (nokp, norujukan, tkh_lantikan_jb, log_id, log_tkh) values (" &
                "'" & Tx_NoKP.Text.Trim & "'," &
                "'" & Tx_No_Rujukan.Text.Trim & "'," &
                "" & z & "," &
                "'" & Session("Id_PG") & "'," &
                "getdate());"
            aY = "1" 'RN
        Else
            aY = "2" 'CN
        End If

        If Tx_CPD.Text = "" Then
            Msg(Me, "Sila Markah CPD !") : Exit Sub
        ElseIf CInt(Tx_CPD.Text.Trim) >= 0 Then
            pCPD = Tx_CPD.Text.Trim
        Else
            Msg(Me, "Sila Masukkan Angka Sahaja!") : Exit Sub
        End If

        Try
            'Comment Original 07082024 - OSH
            'Cmd.CommandText = "Select * from jt_penuh_apc where (apc_tahun = year(getdate()) Or apc_tahun = Year(getdate()) + 1)  and ret = '0' and Apc_Batal = '0' and j_daftar = 1 and nokp = '" & Tx_NoKP.Text & "'"
            'Rdr = Cmd.ExecuteReader
            'If Rdr.Read Then
            '    Msg(Me, "APC Tahun Semasa atau Hadapan Telah Wujud !")
            '    Rdr.Close()
            '    Cn.Close()
            '    Exit Sub
            '    Response.Redirect("P3_APC_Cari2.aspx")
            'Else
            '    Rdr.Close()
            'End If

            Cmd.CommandText = "select * from tmp_adv_apc where nokp = '" & Tx_NoKP.Text & "'"
            Rdr = Cmd.ExecuteReader
            If Rdr.Read Then
                Msg(Me, "Rekod telah wujud")
                Rdr.Close()
                Cn.Close()
                Response.Redirect("P3_APC_Cari2.aspx")
            Else
                Rdr.Close()
            End If

            SQL += "insert tmp_adv_apc (id_pg,nokp,j_daftar,cpd,kpsl) values ('" & Session("Id_PG") & "','" & Tx_NoKP.Text & "','" & aY & "','" & pCPD & "',1);"
            If aY = 1 Then
                SQL += "update jt_penuh_apc set apc_batal = 1 where ret = 0 and apc_batal = 0 and apc_tahun = year(getdate()) and j_daftar = 2 and  nokp = '" & Tx_NoKP.Text.Trim & "'; "
                SQL += "update jt_penuh_apc set apc_batal = 1 where ret = 0 and apc_batal = 0 and apc_tahun = year(getdate())+1 and j_daftar = 2 and  nokp = '" & Tx_NoKP.Text.Trim & "'; "
            End If
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Cn.Close() : Response.Redirect("P3_APC_Cari2.aspx")
        Catch ex As Exception
            Cn.Close()
            Msg(Me, ex.Message)
        End Try

    End Sub

End Class