﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_TPC_Warga
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'TAHUN APC
        For i = Now.Year To Now.Year - 5 Step -1
            Cb_Tahun.Items.Add(i)
        Next

        'Status
        With Cb_Status
            .Items.Clear()
            .Items.Add("BERDAFTAR ")
            .Items.Item(.Items.Count - 1).Value = "1"
            .Items.Add("BELUM BERDAFTAR")
            .Items.Item(.Items.Count - 1).Value = "2"
        End With
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click        
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim SQL As String
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        SQL = "delete from z; "
        SQL += "insert into z "
        If Cb_Status.SelectedValue = 1 Then
            SQL += "select warganegara as kolej ,count(warganegara) as ag,null as lulus,null as gagal, null as tumpang, null as m80, null as m45, null as m0 "
            SQL += "from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp "
            SQL += "left join pn_negara pn on jt.warganegara=pn.id_negara where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " group by warganegara "
            SQL += "insert into z "
            SQL += "select warganegara as kolej ,null as ag,count(warganegara) as lulus,null as gagal, null as tumpang, null as m80, null as m45, null as m0 "
            SQL += "from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp "
            SQL += "left join pn_negara pn on jt.warganegara=pn.id_negara where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and jantina=1 group by warganegara "
            SQL += "insert into z "
            SQL += "select warganegara as kolej ,null as ag,null as lulus,count(warganegara) as gagal, null as tumpang, null as m80, null as m45, null as m0 "
            SQL += "from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp "
            SQL += "left join pn_negara pn on jt.warganegara=pn.id_negara where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and jantina=2 group by warganegara "
        Else
            SQL += "select warganegara as kolej, count(warganegara) as ag, null as lulus, null as gagal, null as tumpang, null as m80, null as m45, null as m0  from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp where year(mohon_tkh)=" & Cb_Tahun.Text & ") group by warganegara "
            SQL += "union select warganegara as kolej, null as ag, count(warganegara) as lulus, null as gagal, null as tumpang, null as m80, null as m45, null as m0  from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp where year(mohon_tkh)=" & Cb_Tahun.Text & " and jantina=1) group by warganegara "
            SQL += "union select warganegara as kolej, null as ag, null as lulus, count(warganegara) as gagal, null as tumpang, null as m80, null as m45, null as m0  from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp where year(mohon_tkh)=" & Cb_Tahun.Text & " and jantina=2) group by warganegara "
            SQL += "; "
        End If
        Cmd.CommandText = SQL
        Cmd.ExecuteNonQuery()
        Cn.Close()

        If Cb_Status.SelectedValue = 1 Then Jana_Daftar() Else Jana_Belum()
    End Sub

    Public Sub Jana_Belum()
        Dim Tajuk, Tajuk2, Negara As String, L, P, X As Integer
        Dim a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13, a14, a15, a16, a17, T As Integer

        Tajuk = "LAPORAN STATISTIK PENDAFTARAN TPC " & Cb_Status.SelectedItem.Text & ", TAHUN " & Cb_Tahun.Text
        Tajuk2 = "MENGIKUT WARGANEGARA DAN NEGERI AMALAN"

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' text-align: center; style='width:100%;'>"
        Header += "<tr>"
        Header += "    <td colspan='53' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='53' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td rowspan='2' style='vertical-align: middle; text-align: left;'>NEGARA</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>JOHOR</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>KEDAH</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>KELANTAN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>MELAKA</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>NEGERI SEMBILAN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>PAHANG</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>PULAU PINANG</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>PERAK</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>PERLIS</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>SABAH</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>SARAWAK</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>SELANGOR</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>TERENGGANU</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>WP KUALA LUMPUR</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>WP LABUAN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>WP PUTRAJAYA</td>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: center;'>JUMLAH KESELURUHAN</td>"
        Header += "</tr>"
        Header += "<tr style='vertical-align: middle; text-align: center;'>"
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td><td>JUMLAH</td> "
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        'Negara
        Cmd.CommandText = "select id_negara, isnull(sum(ag),'0') as ag, isnull(sum(lulus),'0') as lulus, isnull(sum(gagal),'0') as gagal " & _
                          "from z z left outer join pn_negara pn on z.kolej=pn.id_negara group by id_negara order by gagal desc"
        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "negara")
        Rdr.Close()

        Header = ""
        For Each dr As DataRow In Ds.Tables(0).Rows
            Header += "<tr>"
            Cmd.CommandText = "select dc_negara from pn_negara where id_negara=" & dr.Item(0)
            Rdr = Cmd.ExecuteReader() : Negara = ""
            If Rdr.Read Then Negara = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & Negara & "</td> "

            'Johor
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=1 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=1 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a1 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Kedah
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=2 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=2 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a2 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Kelantan
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=3 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=3 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=3)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a3 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Melaka
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=4 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=4 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=4)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a4 += L + P + X
            Header += "    <td>" & X & "</td> "

            'N9
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=5 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=5 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=5)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a5 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Pahang
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=6 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=6 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=6)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a6 += L + P + X
            Header += "    <td>" & X & "</td> "

            'P.Pinang
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=7 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=7 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=7)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a7 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Perak
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=8 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=8 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=8)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a8 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Perlis
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=9 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=9 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=9)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a9 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Sabah
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=10 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=10 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=10)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a10 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Sarawak
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=11 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=11 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=11)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a11 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Selangor
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=12 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=12 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=12)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a12 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Terengganu
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=13 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=13 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=13)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a13 += L + P + X
            Header += "    <td>" & X & "</td> "

            'KL
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=14 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=14 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=14)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a14 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Labuan
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=15 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=15 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=15)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a15 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Putrajaya
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=16 and jantina=1)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=16 and jantina=2)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(nokp) from tmp_tpc where nokp in (select distinct tt.nokp from tmp_tpc tt inner join tmp_tpc_majikan ttm on tt.nokp=ttm.nokp " & _
                              "left join pn_tpt_amalan pta on ttm.tpt_amalan=pta.id_amalan where year(mohon_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=16)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a16 += L + P + X
            Header += "    <td>" & X & "</td> "

            Header += "    <td>" & dr.Item(2) & "</td> " 'L
            Header += "    <td>" & dr.Item(3) & "</td> " 'P
            X = 0 : X = CInt(dr.Item(1)) - CInt(dr.Item(2)) - CInt(dr.Item(3))
            Header += "    <td>" & X & "</td> " 'X
            a17 += CInt(dr.Item(1))

            T = 0 : T = X + CInt(dr.Item(2)) + CInt(dr.Item(3))
            Header += "    <td>" & T & "</td> " 'Total
            Header += "</tr>"
        Next
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>JUMLAH KESELURUHAN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a1 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a2 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a3 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a4 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a5 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a6 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a7 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a8 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a9 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a10 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a11 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a12 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a13 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a14 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a15 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a16 & "</td> "
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: center;'> " & a17 & "</td> "
        Header += "</tr>"
        Response.Write(Header)
        Response.Write("</table>")

        Response.End()
        Response.Flush()
    End Sub

    Public Sub Jana_Daftar()
        Dim Tajuk, Tajuk2, Negara As String, L, P, X As Integer
        Dim a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13, a14, a15, a16, a17, T As Integer

        Tajuk = "LAPORAN STATISTIK PENDAFTARAN TPC " & Cb_Status.SelectedItem.Text & ", TAHUN " & Cb_Tahun.Text
        Tajuk2 = "MENGIKUT WARGANEGARA DAN NEGERI AMALAN"

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' text-align: center; style='width:100%;'>"
        Header += "<tr>"
        Header += "    <td colspan='53' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='53' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td rowspan='2' style='vertical-align: middle; text-align: left;'>NEGARA</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>JOHOR</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>KEDAH</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>KELANTAN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>MELAKA</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>NEGERI SEMBILAN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>PAHANG</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>PULAU PINANG</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>PERAK</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>PERLIS</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>SABAH</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>SARAWAK</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>SELANGOR</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>TERENGGANU</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>WP KUALA LUMPUR</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>WP LABUAN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'>WP PUTRAJAYA</td>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: center;'>JUMLAH KESELURUHAN</td>"
        Header += "</tr>"
        Header += "<tr style='vertical-align: middle; text-align: center;'>"
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td> "
        Header += "    <td>L</td><td>P</td><td>X</td><td>JUMLAH</td> "
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        'Negara
        Cmd.CommandText = "select id_negara, isnull(sum(ag),'0') as ag, isnull(sum(lulus),'0') as lulus, isnull(sum(gagal),'0') as gagal " & _
                          "from z z left outer join pn_negara pn on z.kolej=pn.id_negara group by id_negara order by gagal desc"
        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "negara")
        Rdr.Close()

        Header = ""
        For Each dr As DataRow In Ds.Tables(0).Rows
            Header += "<tr>"
            Cmd.CommandText = "select dc_negara from pn_negara where id_negara=" & dr.Item(0)
            Rdr = Cmd.ExecuteReader() : Negara = ""
            If Rdr.Read Then Negara = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & Negara & "</td> "

            'Johor
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=1 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=1 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a1 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Kedah
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=2 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=2 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a2 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Kelantan
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=3 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=3 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=3 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a3 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Melaka
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=4 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=4 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=4 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a4 += L + P + X
            Header += "    <td>" & X & "</td> "

            'N9
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=5 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=5 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=5 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a5 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Pahang
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=6 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=6 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=6 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a6 += L + P + X
            Header += "    <td>" & X & "</td> "

            'P.Pinang
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=7 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=7 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=7 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a7 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Perak
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=8 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=8 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=8 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a8 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Perlis
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=9 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=9 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=9 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a9 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Sabah
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=10 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=10 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=10 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a10 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Sarawak
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=11 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=11 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=11 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a11 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Selangor
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=12 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=12 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=12 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a12 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Terengganu
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=13 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=13 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=13 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a13 += L + P + X
            Header += "    <td>" & X & "</td> "

            'KL
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=14 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=14 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=14 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a14 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Labuan
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=15 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=15 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=15 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a15 += L + P + X
            Header += "    <td>" & X & "</td> "

            'Putrajaya
            L = 0 : P = 0 : X = 0
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=16 and jantina=1 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then L = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & L & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=16 and jantina=2 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then P = Rdr(0)
            Rdr.Close()
            Header += "    <td>" & P & "</td> "
            Cmd.CommandText = "select count(warganegara) from jt_tpc jt inner join jt_tpc_tpc jtt on jt.nokp=jtt.nokp inner join jt_tpc_majikan jtm on jt.nokp=jtm.nokp and jtt.id_amalan=jtm.tpt_amalan and jtt.nokp=jtm.nokp " & _
                              "left join pn_negara pn on jt.warganegara=pn.id_negara left join pn_tpt_amalan pta on jtt.id_amalan=pta.id_amalan where status=1 and year(tpc_tkh)=" & Cb_Tahun.Text & " and warganegara=" & dr.Item(0) & " and pta.negeri=16 "
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then X = Rdr(0) : X = X - L - P
            Rdr.Close()
            a16 += L + P + X
            Header += "    <td>" & X & "</td> "

            Header += "    <td>" & dr.Item(2) & "</td> " 'L
            Header += "    <td>" & dr.Item(3) & "</td> " 'P
            X = 0 : X = CInt(dr.Item(1)) - CInt(dr.Item(2)) - CInt(dr.Item(3))
            Header += "    <td>" & X & "</td> " 'X
            a17 += CInt(dr.Item(1))

            T = 0 : T = X + CInt(dr.Item(2)) + CInt(dr.Item(3))
            Header += "    <td>" & T & "</td> " 'Total
            Header += "</tr>"
        Next
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>JUMLAH KESELURUHAN</td>"
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a1 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a2 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a3 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a4 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a5 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a6 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a7 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a8 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a9 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a10 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a11 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a12 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a13 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a14 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a15 & "</td> "
        Header += "    <td colspan='3' style='vertical-align: middle; text-align: center;'> " & a16 & "</td> "
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: center;'> " & a17 & "</td> "
        Header += "</tr>"
        Response.Write(Header)
        Response.Write("</table>")
        Response.End()
        Response.Flush()

    End Sub
End Class