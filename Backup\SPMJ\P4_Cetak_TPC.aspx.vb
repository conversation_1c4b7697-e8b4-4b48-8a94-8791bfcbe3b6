﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm52
    Inherits System.Web.UI.Page
    Public x As String

    Public Sub Front()

        x += "<table class=MsoNormalTable border=0 cellpadding=0 style='mso-cellspacing:1.5pt'>"
        x += "<tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;height:33.0pt'>"
        x += "<td width=78 rowspan=3 valign=top style='width:58.5pt;padding:.75pt .75pt .75pt .75pt;height:33.0pt'>"
        x += "<p class=MsoNormal align=center style='text-align:center'><span"
        x += "><img width=59 height=74"
        x += "id='_x0000_i1025' src='" + s + "ljm2.gif'><br>"
        x += "<img width=66 height=83 id='_x0000_i1026'"
        x += "src='" + s + "imgc.gif'></span></p>"
        x += "</td>"
        x += "<td colspan=2 style='font-size:11pt;font-family:<PERSON><PERSON>;width:141.75pt;padding:.75pt .75pt .75pt .75pt;height:33pt'>"
        x += "<b>Nursing Board Malaysia<br>"
        x += "Ministry of Health Malaysia</b>"

        x += "</td>"
        x += "</tr>"
        x += "<tr style='mso-yfti-irow:1;height:52.5pt;width:200pt;'>"
        x += "<td width=250 colspan=2 style='font-size:8.0pt;line-height:120%;font-family:Arial Narrow;padding:.75pt .75pt .75pt .75pt;height:52.5pt'><b>"
        x += "Temporary Practising Certificate No. 237<br>"
        x += "Name : " + Session("tpc_nama") + "<br>"
        x += "Passport No : " + Session("tpc_nokp") + "<br>"
        x += "Valid Date : " + Session("tpc_tkh") + " - " + DateAdd(DateInterval.Day, -1, DateAdd(DateInterval.Year, 1, Session("tpc_tkh"))) + "</b>"
        x += "</td>"
        x += "</tr>"

        x += "<tr style='mso-yfti-irow:2;mso-yfti-lastrow:yes;height:30.75pt'>"
        x += "<td width=52 style='font-size:8.0pt;font-family:Arial Narrow;width:39.0pt;padding:.75pt .75pt .75pt .75pt;height:30.75pt'>"
        x += "</td>"
        x += "<td width=135 style='text-align:center;font-size:8.0pt;font-family:Arial Narrow;width:101.25pt;padding:.75pt .75pt .75pt .75pt;height:30.75pt'>"
        x += ".............................<br>"
        x += "Registrar<br>"
        x += "Nursing Board Malaysia</b>"
        x += "</td>"
        x += "</tr>"

        x += "</table>"
    End Sub

    Public Sub Back()

        x += "<table class=MsoNormalTable border=0 cellpadding=0 style='mso-cellspacing:1.5pt'>"
        x += "<tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;height:33.0pt'>"
        x += "<td width=78 rowspan=3 valign=top style='width:58.5pt;padding:.75pt .75pt .75pt .75pt;height:33.0pt'>"
        x += "<p class=MsoNormal align=center style='text-align:center'><span"
        x += "><img width=59 height=74"
        x += "id='_x0000_i1025' src='http://localhost:1612/surat/ljm2.gif'><br>"
        x += "<img width=66 height=83 id='_x0000_i1026'"
        x += "src='http://localhost:1612/surat/imgc.gif'></span></p>"
        x += "</td>"
        x += "<td colspan=2 style='font-size:11pt;font-family:Arial Narrow;width:141.75pt;padding:.75pt .75pt .75pt .75pt;height:33pt'>"
        x += "<b>Nursing Board Malaysia<br>"
        x += "Ministry of Health Malaysia</b>"

        x += "</td>"
        x += "</tr>"
        x += "<tr style='mso-yfti-irow:1;height:52.5pt;width:200pt;'>"
        x += "<td width=250 colspan=2 style='font-size:8.0pt;line-height:120%;font-family:Arial Narrow;padding:.75pt .75pt .75pt .75pt;height:52.5pt'><b>"
        x += "Temporary Practising Certificate No. 237<br>"
        x += "Name : " + Session("tpc_nama") + "<br>"
        x += "Passport No : " + Session("tpc_nokp") + "<br>"
        x += "Valid Date : " + Session("tpc_tkh") + " - " + DateAdd(DateInterval.Day, -1, DateAdd(DateInterval.Year, 1, Session("tpc_tkh"))) + "</b>"
        x += "</td>"
        x += "</tr>"

        x += "<tr style='mso-yfti-irow:2;mso-yfti-lastrow:yes;height:30.75pt'>"
        x += "<td width=52 style='font-size:8.0pt;font-family:Arial Narrow;width:39.0pt;padding:.75pt .75pt .75pt .75pt;height:30.75pt'>"
        x += "</td>"
        x += "<td width=135 style='text-align:center;font-size:8.0pt;font-family:Arial Narrow;width:101.25pt;padding:.75pt .75pt .75pt .75pt;height:30.75pt'>"
        x += ".............................<br>"
        x += "Registrar<br>"
        x += "Nursing Board Malaysia</b>"
        x += "</td>"
        x += "</tr>"

        x += "</table>"
    End Sub

    Public Sub Sijil_TPC()
        x = ""
        x = "<html xmlns:v='urn:schemas-microsoft-com:vml' xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word'>"
        x += "<head><style type='text/css'><!-- .indent_1x { padding-left: 100pt;padding-right: 50pt;} --></style>"
        x += "<style type='text/css'><!-- .indent_3x { padding-left: 400pt;padding-right: 50pt;} --></style>"
        x += "</head><body>"

        'x += "<table width='100%' style='border: none;margin-left: 0px; font-family: Arial; font-size: 8pt;'><tr>"
        ''x += "<td></td>"
        'x += "<td style='width:1%;border: none; margin-left: 0px; font-family: Arial; font-size: 8pt;'><img width=80 height=100 src='" & s & "ljm2.gif'><br><img width=80 height=100 src='" & s & "imgc.gif'>"
        'x += "<td>"
        'x += "Nursing Board Malaysia<br/>"
        'x += "Ministry of Health<br/>"
        'x += "Temporary Practising Certificate No " + Session("tpc_nopd") + "<br/>"
        'x += "Name : " + Session("tpc_name") + "<br/>"
        'x += "Passport No : " + Session("tpc_nokp") + "<br/>"
        'x += "Valid Date : " + DateAdd(DateInterval.Day, -1, DateAdd(DateInterval.Year, 1, Session("tpc_tkh"))) + "<br/>"
        'x += "<br/>"
        'x += ".............................<br/>"
        'x += "Registrar<br/>"
        'x += "Nursing Board Malaysia<br/>"
        'x += "</td>"
        'x += "</tr><table>"

        'x += "<br style='page-break-before:always'>"
        x += "<br>"
        Front()
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        x += "<br>"
        Back()
        x += "</body>"
        x += "</html>"

        Dim fileName As String = "Surat.doc"
        Response.Clear()
        Response.Buffer = True
        ' You can add whatever you want to add as the HTML and it will be generated as Ms Word docs
        Response.AppendHeader("Content-Type", "application/msword")
        Response.AppendHeader("Content-disposition", "attachment; filename=" + fileName)
        Response.Write(x)
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Session("tpc_nama") = "James Bond"
        'Session("tpc_nokp") = "770707-07-0007"
        'Session("tpc_nopd") = "237"
        'Session("tpc_tkh") = Now.ToShortDateString
        'Sijil_TPC()

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
    End Sub

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        SQL = "select NAMA, tp.NOKP as 'NO. KP/PASPORT', sm_alamat, sm_poskod, sm_bandar, dc_negeri, nopd, tpc_noresit, tpc_tkh from jt_tpc tp inner join jt_tpc_tpc jtt on tp.nokp=jtt.nokp and year(jtt.tpc_tkh)=year(getdate()) left outer join pn_negeri pn on tp.sm_negeri=pn.id_negeri where " & X & "  order by nama"
        Tb = "tmp_penuh"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Tx_Nama.Text.Trim = "" And Tx_NoKP.Text.Trim = "" And Tx_NoPd.Text.Trim = "" Then Exit Sub
        Cari("nama like '" & Tx_Nama.Text & "%' and tp.nokp like '" & Tx_NoKP.Text & "%' and nopd like '" & Tx_NoPd.Text & "%'")
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        e.Row.Cells(4).Visible = False
        e.Row.Cells(5).Visible = False
        e.Row.Cells(6).Visible = False
        e.Row.Cells(7).Visible = False
        e.Row.Cells(9).Visible = False
        e.Row.Cells(10).Visible = False
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Session("tpc_nama") = Gd.SelectedRow.Cells(2).Text
        Session("tpc_nokp") = Gd.SelectedRow.Cells(3).Text
        'Session("tpc_alamat") = Gd.SelectedRow.Cells(4).Text
        'Session("tpc_poskod") = Gd.SelectedRow.Cells(5).Text
        'Session("tpc_bandar") = Gd.SelectedRow.Cells(6).Text
        'Session("tpc_negeri") = Gd.SelectedRow.Cells(7).Text
        Session("tpc_nopd") = "TPC-" + Gd.SelectedRow.Cells(8).Text
        'Session("tpc_noresit") = Gd.SelectedRow.Cells(9).Text
        Session("tpc_tkh") = Format(CDate(Gd.SelectedRow.Cells(10).Text), "dd/MM/yyyy")
        Sijil_TPC()
        'Surat_Individu_Akuan()
    End Sub
End Class