﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class PN_Pengguna2
    Inherits System.Web.UI.Page

    Public Sub Cari(ByVal X As String)
        Dim SQL, Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)

        'Comment Original 23092020 - OSH
        'SQL = "select id_pg as 'ID PENGGUNA', '**********' as 'KATA LALUAN', case akses when 0 then 'Biasa' when 1 then 'Penyelia' when 2 then 'Pengurus' end as 'AKSES', NAMA, MODUL, case status when 1 then 'A' else 'TA' end as 'STATUS' from pn_pengguna where status = 1 order by nama"

        ''Improve roles list query 23092020 - OSH 
        'SQL = "select id_pg as 'ID PENGGUNA', '**********' as 'KATA LALUAN', case akses when 0 then 'BIASA' when 1 then 'PEMPROSES' when 2 then 'PENYELIA' when 3 then 'PENGURUS' end as 'AKSES', NAMA, MODUL, case status when 1 then 'A' else 'TA' end as 'STATUS' from pn_pengguna where status = 1 order by nama"

        'Improve roles list query 26032021 - OSH 
        SQL = "select id_pg as 'ID PENGGUNA', '**********' as 'KATA LALUAN', case akses when 0 then 'BIASA' when 1 then 'PEMPROSES' when 2 then 'PENYELIA' when 3 then 'PENGURUS' when 4 then 'PENTADBIR' end as 'AKSES', NAMA, MODUL, case status when 1 then 'A' else 'TA' end as 'STATUS' from pn_pengguna where status = 1 order by nama"


        Tb = "pn_pengguna"

        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("PN", "Main2", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PENYELENGGARAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        'POLULATE ITEMS AND VALUES OF NEW CATAGEORY ACCESS LEVEL 23092020 - OSH   
        Cb_Akses.Items.Add("BIASA")
        Cb_Akses.Items.Item(0).Value = "0"
        Cb_Akses.Items.Add("PEMPROSES")
        Cb_Akses.Items.Item(1).Value = "1"
        Cb_Akses.Items.Add("PENYELIA")
        Cb_Akses.Items.Item(2).Value = "2"
        Cb_Akses.Items.Add("PENGURUS")
        Cb_Akses.Items.Item(3).Value = "3"
        Cb_Akses.Items.Add("PENTADBIR")
        Cb_Akses.Items.Item(4).Value = "4"

        Cari("")
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        ''e.Row.Cells(1).Visible = False
        e.Row.Cells(0).Width = Unit.Pixel(30)
        e.Row.Cells(6).Visible = False
        'e.Row.Cells(4).Visible = False
        'e.Row.Cells(5).Visible = False
        'e.Row.Cells(7).Visible = False
        ''e.Row.Cells(8).Visible = False
        'e.Row.Cells(9).Visible = False
        'e.Row.Cells(10).Visible = False
        'e.Row.Cells(11).Visible = False

        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        If Session("AKSES") = 2 Then cmd_Cari3.Visible = True
        If Gd.SelectedRow.Cells(4).Text = "Biasa" Then Cb_Akses.SelectedValue = 0
        If Gd.SelectedRow.Cells(4).Text = "Pemproses" Then Cb_Akses.SelectedValue = 1
        If Gd.SelectedRow.Cells(4).Text = "Penyelia" Then Cb_Akses.SelectedValue = 2
        If Gd.SelectedRow.Cells(4).Text = "Pengurus" Then Cb_Akses.SelectedValue = 3
        Tx_IdPg.Text = Gd.SelectedRow.Cells(2).Text
        Tx_Nama.Text = Gd.SelectedRow.Cells(5).Text
        Tx_Pwd.Text = Gd.SelectedRow.Cells(3).Text
        ch_m1.Checked = CInt(Mid(Gd.SelectedRow.Cells(6).Text, 1, 1))
        ch_m2.Checked = CInt(Mid(Gd.SelectedRow.Cells(6).Text, 2, 1))
        ch_m3.Checked = CInt(Mid(Gd.SelectedRow.Cells(6).Text, 3, 1))
        ch_m4.Checked = CInt(Mid(Gd.SelectedRow.Cells(6).Text, 4, 1))
        ch_m5.Checked = CInt(Mid(Gd.SelectedRow.Cells(6).Text, 5, 1))
        ch_m6.Checked = CInt(Mid(Gd.SelectedRow.Cells(6).Text, 6, 1))
        If Gd.SelectedRow.Cells(7).Text = "A" Then Cb_Akses.SelectedIndex = 1 Else Cb_Akses.SelectedIndex = 0
        Session("PINDA") = True
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        If Cb_Akses.SelectedIndex < 0 Then Cb_Akses.Focus() : Exit Sub
        'If Cb_Sektor.SelectedIndex < 1 Then Exit Sub
        If Tx_Nama.Text = "" Then Tx_Nama.Focus() : Exit Sub
        If Tx_IdPg.Text = "" Then Tx_IdPg.Focus() : Exit Sub
        If Tx_Pwd.Text = "" Then Tx_Pwd.Focus() : Exit Sub

        Dim SQL As String, Modul As Long

        If ch_m1.Checked Then Modul += 100000
        If ch_m2.Checked Then Modul += 10000
        If ch_m3.Checked Then Modul += 1000
        If ch_m4.Checked Then Modul += 100
        If ch_m5.Checked Then Modul += 10
        If ch_m6.Checked Then Modul += 1


        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        If Session("PINDA") Then
            'Comment Original 09102019 - OSH 
            'SQL = "update pn_pengguna set akses='" & Cb_Akses.SelectedIndex & "', nama='" & Apo(Tx_Nama.Text.Trim.ToUpper) & "', modul='" & Format(Modul, "000000") & "',tkh_daftar=getdate(), status = " & Cb_Status.SelectedIndex

            SQL = Nothing
            If Cb_Status.SelectedValue = 1 Then 'active 
                'Update  
                SQL = "update pn_pengguna set akses='" & Cb_Akses.SelectedIndex & "', nama='" & Apo(Tx_Nama.Text.Trim.ToUpper) & "', modul='" & Format(Modul, "000000") & "',nokp= '" & Session("Id_PG") & "',tkh_daftar=getdate(), status = " & Cb_Status.SelectedIndex
            ElseIf Cb_Status.SelectedValue = 0 Then ' dis -active 
                SQL = "update pn_pengguna set akses='" & Cb_Akses.SelectedIndex & "', nama='" & Apo(Tx_Nama.Text.Trim.ToUpper) & "', modul='" & Format(Modul, "000000") & "',nokp= '" & Session("Id_PG") & "',tkh_batal=getdate(), status = " & Cb_Status.SelectedIndex
            End If
            SQL += " where id_pg='" & Tx_IdPg.Text & "'"
            Try
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
                Msg(Me, "Rekod Telah Disimpan...")
                Cari("")
            Catch ex As Exception
                Msg(Me, "Error...")
            End Try
            Cn.Close()
            Exit Sub
        End If

        Cmd.CommandText = "select id_pg from pn_pengguna where id_pg = '" & Tx_IdPg.Text & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then Msg(Me, "Rekod dengan kod pengguna yang sama telah ada.") : Exit Sub
        Rdr.Close()

        'Register
        SQL = "insert pn_pengguna (id_pg, pwd, akses, nama, modul, status, tkh_daftar) "
        SQL += "select '" & Tx_IdPg.Text & "', '" & Tx_Pwd.Text & "',  '" & Cb_Akses.SelectedIndex & "',  '" & Apo(Tx_Nama.Text.Trim.ToUpper) & "',  '" & Format(Modul, "000000") & "',1, getdate()"
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Msg(Me, "Rekod Telah Disimpan...")
            Cari("")
        Catch ex As Exception
            Msg(Me, "Error...")
        End Try
        Cn.Close()
    End Sub

    Protected Sub cmd_Cari2_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari2.Click
        Cb_Akses.SelectedIndex = 0
        Cb_Status.SelectedIndex = 1
        Tx_IdPg.Text = ""
        Tx_Nama.Text = ""
        Tx_Pwd.Text = ""
        ch_m1.Checked = False
        ch_m2.Checked = False
        ch_m3.Checked = False
        ch_m4.Checked = False
        ch_m5.Checked = False
        ch_m6.Checked = False
        Session("PINDA") = False
    End Sub

    Protected Sub cmd_Cari3_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari3.Click
        If Tx_Nama.Text = "" Then Exit Sub
        If Tx_IdPg.Text = "" Then Exit Sub
        If Tx_Pwd.Text = "" Then Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim SQL As String
        'Fixing Reset Password query 16082018 - OSH
        SQL = "update pn_pengguna set pwd= '" & Tx_IdPg.Text.Trim & "', nokp= '" & Session("Id_PG") & "', tkh_reset = getdate() where id_pg = '" & Tx_IdPg.Text.Trim & "' And status = 1 "

        'Comment Original 16082018 - OSH
        'SQL = "update pn_pengguna set pwd=id_pg where id_pg = '" & Tx_IdPg.Text & "'"
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Msg(Me, "Kata Laluan telah dikemaskini..")
        Catch ex As Exception
            Msg(Me, "Error...")
        End Try
        Cn.Close()
        cmd_Cari3.Visible = False
    End Sub

End Class