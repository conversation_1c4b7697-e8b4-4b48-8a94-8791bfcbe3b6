﻿<%@ Page Language="vb" AutoEventWireup="false" MasterPageFile="~/Main.Master" CodeBehind="LP_XM.aspx.vb" Inherits="SPMJ.LP_XM" 
    title="SPMJ" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="server">
    <style type="text/css">
        .style1
        {
            height: 23px;
        }
        .style2
        {
            height: 7px;
        }
        .style3
        {
            height: 24px;
            width: 184px;
        }
        .style4
        {
            height: 24px;
            width: 507px;
        }
        .style5
        {
            width: 507px;
        }
        .style7
        {
            width: 99px;
        }
        .style8
        {
            height: 23px;
            width: 99px;
        }
        .style9
        {
            height: 7px;
            width: 99px;
        }
        .style10
        {
            width: 834px;
        }
        .style11
        {
            height: 23px;
            width: 834px;
        }
        .style12
        {
            height: 7px;
            width: 834px;
        }
        .style13
        {
            width: 184px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <div style="height: 100%; width: 100%; font-variant: small-caps;">
    <table id="T1" cellpadding="-1" cellspacing="-1" width="100%">
        <tr>
            <td class="style7"></td>
            <td class="style10"></td>
            <td></td>
        </tr>
        <tr>
            <td class="style7">&nbsp;</td>
            <td class="style10">
                <asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server">
                </asp:ScriptManagerProxy>
                         </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style8"></td>
            <td align="center" 
                style="font-variant: small-caps; font-family: Arial; font-size: 8pt; color: #FFFFFF; font-weight: bold;" 
                bgcolor="#719548" class="style11">Laporan&nbsp; - Modul Peperiksaan</td>
            <td class="style1"></td>
        </tr>
        <tr>
            <td class="style7"></td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style10">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style7">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style10">
                <asp:UpdatePanel ID="UpdatePanel3" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj10" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="148px" Enabled="False" 
                            ReadOnly="True">TEMPAT LATIHAN</asp:TextBox>
                        <asp:DropDownList ID="Cb_Kolej" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="530px">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td class="style7">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style10">
                <asp:UpdatePanel ID="UpdatePanel5" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj12" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="148px" Enabled="False" 
                            ReadOnly="True">JENIS KURSUS</asp:TextBox>
                        <asp:DropDownList ID="Cb_Kursus" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="250px">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td class="style7">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style10">
                <asp:UpdatePanel ID="UpdatePanel7" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj14" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="148px" Enabled="False" 
                            ReadOnly="True">JANTINA</asp:TextBox>
                        <asp:DropDownList ID="Cb_Jantina" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="250px">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td class="style7">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style10">
                <asp:UpdatePanel ID="UpdatePanel6" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj13" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="148px" Enabled="False" 
                            ReadOnly="True">BANGSA</asp:TextBox>
                        <asp:DropDownList ID="Cb_Bangsa" runat="server" Font-Names="Arial" 
                            Font-Size="8pt" Width="250px">
                        </asp:DropDownList>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td class="style7">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style10">
                <asp:UpdatePanel ID="UpdatePanel8" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="Cb_Sbj15" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="148px" Enabled="False" 
                            ReadOnly="True">ANGKA GILIRAN</asp:TextBox>
                        <asp:TextBox ID="Tx_AG1" runat="server" CssClass="std" Width="80px" 
                            Wrap="False"></asp:TextBox>
                        <asp:TextBox ID="Cb_Sbj16" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="10px">-</asp:TextBox>
                        <asp:TextBox ID="Tx_AG2" runat="server" CssClass="std" Width="80px" 
                            Wrap="False"></asp:TextBox>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td class="style7">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style10">
                <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                    <ContentTemplate>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:TextBox ID="TextBox1" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="148px" Enabled="False" 
                            ReadOnly="True">TAHUN & SIRI PEPERIKSAAN</asp:TextBox>
                        <asp:TextBox ID="Tx_Tahun" runat="server" CssClass="std" Width="80px" 
                            Wrap="False"></asp:TextBox>
                        <asp:TextBox ID="TextBox3" runat="server" BackColor="Transparent" 
                            BorderColor="Black" BorderStyle="Groove" BorderWidth="0px" Font-Names="Arial" 
                            Font-Size="8pt" Height="17px" tabIndex="36" Width="10px">&amp;</asp:TextBox>
                        <asp:TextBox ID="Tx_Siri" runat="server" CssClass="std" Width="80px" 
                            Wrap="False"></asp:TextBox>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td align="center">&nbsp;</td>
        </tr>
        <tr>
            <td class="style9">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style12">
                &nbsp;</td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style9">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style12">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <table style="border-width: 1px; border-color: #000000; width:100%; border-top-style: solid; border-bottom-style: solid;">
                            <tr>
                                <td class="style3">
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                </td>
                                <td align="char" class="style4" width="10%">
                                    <asp:CheckBoxList ID="Chk_Pilih" runat="server" Font-Names="Arial" 
                                        Font-Size="8pt" RepeatColumns="2" Width="93%">
                                        <asp:ListItem Value="xc.ag">ANGKA GILIRAN</asp:ListItem>
                                        <asp:ListItem Value="nama">NAMA</asp:ListItem>
                                        <asp:ListItem Value="p.nokp">NO. KP</asp:ListItem>
                                        <asp:ListItem Value="jantina">JANTINA</asp:ListItem>
                                        <asp:ListItem Value="bangsa">BANGSA</asp:ListItem>
                                        <asp:ListItem Value="warga">WARGANEGARA</asp:ListItem>
                                        <asp:ListItem Value="p.id_kolej">TEMPAT LATIHAN</asp:ListItem>
                                        <asp:ListItem Value="tajaan">TAJAAN</asp:ListItem>
                                        <asp:ListItem Value="j_kursus">JENIS KURSUS</asp:ListItem>
                                        <asp:ListItem Value="cast(p.sesi_bulan as varchar(2)) + cast(sesi_tahun as varchar(4)) as sesi">SESI 
                                        PENGAMBILAN</asp:ListItem>
                                        <asp:ListItem Value="p.tkh_latihan_mula">TARIKH MULA LATIHAN</asp:ListItem>
                                        <asp:ListItem>TARIKH TAMAT LATIHAN</asp:ListItem>
                                        <asp:ListItem>TARIKH PEPERIKSAAN</asp:ListItem>                                        
                                        <asp:ListItem>PEMARKAHAN</asp:ListItem>
                                        <asp:ListItem Value="xc.keputusan">KEPUTUSAN</asp:ListItem>
                                        <asp:ListItem>PUSAT PEPERIKSAAN ASAL</asp:ListItem>
                                        <asp:ListItem>PUSAT PEPERIKSAAN TUMPANG</asp:ListItem>
                                        <asp:ListItem>GRED</asp:ListItem>
                                    </asp:CheckBoxList>
                                </td>
                            </tr>
                            <tr>
                                <td class="style13">
                                    &nbsp;</td>
                                <td class="style5">
                                    &nbsp;</td>
                            </tr>
                </table>
            </td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style9">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; font-size: 3px;" 
                bgcolor="White" class="style12">
                &nbsp;</td>
            <td class="style2">&nbsp;</td>
        </tr>
        <tr>
            <td class="style7">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300" 
                bgcolor="White" class="style10">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                        <asp:TextBox ID="Cb_Sbj5" runat="server" BackColor="Transparent" 
                                            BorderColor="Black" BorderStyle="Groove" 
            BorderWidth="0px" Font-Names="Arial" 
                                            Font-Size="8pt" Height="17px" 
            tabIndex="36" Width="220px"></asp:TextBox>
                <asp:Button ID="cmd_Cari1" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="JANA" Width="80px" />
            &nbsp;<asp:Button ID="cmd_Cari2" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="EXCEL" Width="80px" />
            &nbsp;<asp:Button ID="cmd_Cari3" runat="server" Font-Names="Arial" Font-Size="8pt" 
                    Height="20px" tabIndex="3" Text="EXCEL" Width="80px" />
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style7">&nbsp;</td>
            <td 
                
                style="border-right-style: solid; border-left-style: solid; border-right-width: 1px; border-left-width: 1px; border-color: #003300; border-bottom-style: solid; border-bottom-width: 1px;" 
                bgcolor="White" class="style10">
                &nbsp;&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="style7"></td>
            <td class="style10">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style7"></td>
            <td class="style10">
                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                    <ContentTemplate>
                        <asp:GridView ID="Gd" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="9pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="100%" BorderColor="#719548">
                            <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                            <RowStyle BackColor="#F4F4F4" Height="21px" />
                            <Columns>
                                <asp:TemplateField HeaderText="#">
                                    <HeaderStyle HorizontalAlign="Center" />
                                    <ItemStyle HorizontalAlign="Center" Width="30px" />
                                </asp:TemplateField>
                            </Columns>
                            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                            <SelectedRowStyle Font-Bold="True" />
                            <HeaderStyle BackColor="#7EA851" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                            <AlternatingRowStyle BackColor="White" />
                        </asp:GridView>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td></td>
        </tr>
        <tr>
            <td class="style7"></td>
            <td class="style10">&nbsp;</td>
            <td></td>
        </tr>
        <tr>
            <td class="style7"></td>
            <td class="style10">
                <asp:UpdatePanel ID="UpdatePanel4" runat="server">
                    <ContentTemplate>
                        <asp:GridView ID="Gd1" 
                            runat="server" CellPadding="1" Font-Names="Arial" 
                    Font-Size="9pt" ForeColor="Black" HorizontalAlign="Left" 
                    Width="100%" BorderColor="#719548">
                            <FooterStyle BackColor="#A9E100" Font-Bold="True" ForeColor="White" />
                            <RowStyle BackColor="#F4F4F4" Height="21px" />
                            <Columns>
                                <asp:TemplateField HeaderText="#">
                                    <HeaderStyle HorizontalAlign="Center" />
                                    <ItemStyle HorizontalAlign="Center" Width="30px" />
                                </asp:TemplateField>
                            </Columns>
                            <PagerStyle BackColor="#284775" ForeColor="White" HorizontalAlign="Center" />
                            <SelectedRowStyle Font-Bold="True" />
                            <HeaderStyle BackColor="#7EA851" Font-Bold="True" ForeColor="White" 
                        HorizontalAlign="Left" Height="21px" CssClass="menu_small" />
                            <AlternatingRowStyle BackColor="White" />
                        </asp:GridView>
                    </ContentTemplate>
                </asp:UpdatePanel>
            </td>
            <td></td>
        </tr>
        <tr>
            <td class="style7">&nbsp;</td>
            <td class="style10">
                <br />
                        <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
                <br />
            </td>
            <td>&nbsp;</td>
        </tr>
    </table>
    </div>
</asp:Content>