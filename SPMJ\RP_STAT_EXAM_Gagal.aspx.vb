﻿Imports System.IO
Imports System.Data
Imports System.Data.SqlClient
Imports System.Text
Imports iTextSharp.text
Imports iTextSharp.text.pdf
Imports iTextSharp.text.html
Imports iTextSharp.text.html.simpleparser

Partial Public Class RP_STAT_EXAM_Gagal
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Add Access Cotrol Check 03062020 - OSH 
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Laporan", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        'JENIS PEPERIKSAAN
        With Cb_Peperiksaan
            .Items.Clear()
            .Items.Add("JURURAWAT BERDAFTAR")
            .Items.Item(.Items.Count - 1).Value = "1"
            .Items.Add("JURURAWAT MASYARAKAT")
            .Items.Item(.Items.Count - 1).Value = "2"
            .Items.Add("PENOLONG JURURAWAT")
            .Items.Item(.Items.Count - 1).Value = "3"
            .Items.Add("KEBIDAAN 1")
            .Items.Item(.Items.Count - 1).Value = "4"
        End With

    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        'Comment Original 17082018 - OSH
        'Dim con As New SqlConnection(ServerId_SQL)
        'Dim cmd As New SqlCommand()
        'cmd.CommandType = CommandType.StoredProcedure
        'cmd.CommandText = "stat_fail_cannidate"
        'cmd.Parameters.Add("@x_J_Kursus", SqlDbType.Int, 4).Value = Cb_Peperiksaan.SelectedValue
        'cmd.Connection = con
        'Try
        '    con.Open()
        '    Dim GridView1 = New GridView()
        '    GridView1.EmptyDataText = "No Records Found"
        '    GridView1.DataSource = cmd.ExecuteReader()
        '    GridView1.DataBind()

        '    'Dump to PDF
        '    Response.ContentType = "application/pdf"
        '    Response.AddHeader("content-disposition", "attachment;filename=calon_gagal.pdf")
        '    Response.Cache.SetCacheability(HttpCacheability.NoCache)
        '    Dim sw As New StringWriter()
        '    Dim hw As New HtmlTextWriter(sw)
        '    GridView1.RenderControl(hw)
        '    Dim sr As New StringReader(sw.ToString())
        '    Dim pdfDoc As New Document(PageSize.A4.Rotate, 10.0F, 10.0F, 10.0F, 0.0F)
        '    Dim htmlparser As New HTMLWorker(pdfDoc)
        '    'Add New 
        '    Dim pdfWrite As PdfWriter = PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
        '    Dim ev As New itsEvents
        '    pdfWrite.PageEvent = ev
        '    'PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
        '    pdfDoc.Open()
        '    htmlparser.Parse(sr)
        '    pdfDoc.Close()
        '    Response.Write(pdfDoc)
        '    Response.End()
        'Catch ex As Exception
        '    Throw ex
        'Finally
        '    con.Close()
        '    con.Dispose()
        'End Try

        'Add New Itextsharp 5 17082018 - OSH
        Dim con As New SqlConnection(ServerId_SQL)
        Dim cmd As New SqlCommand()
        Dim Tajuk As String = ""


        ' Declare Paper Size
        Dim pdfDoc As New Document(PageSize.A4, 10.0F, 10.0F, 10.0F, 0.0F)

        'Declare table and column size
        Dim table As New PdfPTable(8)

        'Declare Font Type and SIze
        Dim font As New Font(font.FontFamily.TIMES_ROMAN, 10, font.NORMAL)

        'Declare Font Type and SIze - DATA 
        Dim font2 As New Font(font.FontFamily.TIMES_ROMAN, 8, font.NORMAL)

        'Check select
        If Cb_Peperiksaan.Text = "" Then
            MsgBox("Sila Pilih Jenis Peperiksaan")
        End If

        If Cb_Peperiksaan.SelectedValue = "1" Then
            Tajuk = "SENARAI CALON GAGAL BAGI PEPERIKSAAN LEMBAGA JURURAWAT MALAYSIA BAGI JURURAWAT"

        ElseIf Cb_Peperiksaan.SelectedValue = "2" Then
            Tajuk = "SENARAI CALON GAGAL BAGI PEPERIKSAAN AKHIR JURURAWAT MASYARAKAT"

        ElseIf Cb_Peperiksaan.SelectedValue = "3" Then
            Tajuk = "SENARAI CALON GAGAL BAGI PEPERIKSAAN AKHIR PENOLONG JURURAWAT"

        ElseIf Cb_Peperiksaan.SelectedValue = "4" Then
            Tajuk = "SENARAI CALON GAGAL BAGI PEPERIKSAAN KEBIDANAN BAHAGIAN I"
        End If

        'Set Cells Sizes - Width
        Dim TWidth() As Single = {40.0F, 60.0F, 90.0F, 110.0F, 60.0F, 110.0F, 90.0F, 60.0F}

        'repeat header
        table.HeaderRows = 2
        table.HorizontalAlignment = 1 '1: center

        'leave a gap before and after the table
        table.SpacingBefore = 20.0F
        table.SpacingAfter = 30.0F
        table.SetTotalWidth(TWidth) 'assign length sizes to table 


        Dim cell As New PdfPCell(New Phrase(Tajuk))

        cell.Colspan = 8
        cell.Border = 0
        cell.HorizontalAlignment = 1
        table.AddCell(cell)


        cmd.CommandType = CommandType.StoredProcedure
        cmd.CommandText = "SPMJ_EX_Fail_Canididate"
        cmd.Parameters.Add("@x_J_Kursus", SqlDbType.Int, 4).Value = Cb_Peperiksaan.SelectedValue
        cmd.Connection = con

        Try
            con.Open()
            Dim rdr As SqlDataReader = cmd.ExecuteReader()
            Dim i As Integer = 1

            'Comment Original 15082018 - OSH 
            'table.AddCell(New PdfPCell(New Phrase("NO.", font)))
            'table.AddCell(New PdfPCell(New Phrase("NAMA", font)))
            'table.AddCell(New PdfPCell(New Phrase("NO. KAD PENGENALAN", font)))
            'table.AddCell(New PdfPCell(New Phrase("KURSUS", font)))

            'Fixing Text Alighment 15082018 - OSH 
            Dim CH1 As New PdfPCell(New Phrase("NO", font))
            Dim CH2 As New PdfPCell(New Phrase("ANGKA GILIRAN", font))
            Dim CH3 As New PdfPCell(New Phrase("NAMA", font))
            Dim CH4 As New PdfPCell(New Phrase("NO. KAD PENGENALAN", font))
            Dim CH5 As New PdfPCell(New Phrase("KURSUS", font))
            Dim CH6 As New PdfPCell(New Phrase("INSTITUSI PENGAJIAN", font))
            Dim CH7 As New PdfPCell(New Phrase("KEPUTUSAN", font))
            Dim CH8 As New PdfPCell(New Phrase("TAHUN", font))

            CH1.HorizontalAlignment = Element.ALIGN_CENTER
            CH2.HorizontalAlignment = Element.ALIGN_CENTER
            CH3.HorizontalAlignment = Element.ALIGN_CENTER
            CH4.HorizontalAlignment = Element.ALIGN_CENTER
            CH5.HorizontalAlignment = Element.ALIGN_CENTER
            CH6.HorizontalAlignment = Element.ALIGN_CENTER
            CH7.HorizontalAlignment = Element.ALIGN_CENTER
            CH8.HorizontalAlignment = Element.ALIGN_CENTER

            CH1.VerticalAlignment = Element.ALIGN_MIDDLE
            CH2.VerticalAlignment = Element.ALIGN_MIDDLE
            CH3.VerticalAlignment = Element.ALIGN_MIDDLE
            CH4.VerticalAlignment = Element.ALIGN_MIDDLE
            CH5.VerticalAlignment = Element.ALIGN_MIDDLE
            CH6.VerticalAlignment = Element.ALIGN_MIDDLE
            CH7.VerticalAlignment = Element.ALIGN_MIDDLE
            CH8.VerticalAlignment = Element.ALIGN_MIDDLE


            table.AddCell(CH1)
            table.AddCell(CH2)
            table.AddCell(CH3)
            table.AddCell(CH4)
            table.AddCell(CH5)
            table.AddCell(CH6)
            table.AddCell(CH7)
            table.AddCell(CH8)

            If rdr.HasRows Then
                While rdr.Read()
                    
                    'Add Text Alighment 15082018 - OSH 
                    Dim CD1 As New PdfPCell(New Phrase(i, font2)) 'NO
                    Dim CD2 As New PdfPCell(New Phrase(rdr(0).ToString, font2)) 'ANGKA GILIRAN
                    Dim CD3 As New PdfPCell(New Phrase(rdr(1).ToString, font2)) 'NAMA
                    Dim CD4 As New PdfPCell(New Phrase(rdr(2).ToString, font2)) 'NO. KAD PENGENALAN
                    Dim CD5 As New PdfPCell(New Phrase(rdr(3).ToString, font2)) 'KURSUS
                    Dim CD6 As New PdfPCell(New Phrase(rdr(4).ToString, font2)) 'INSTITUSI PENGAJIAN"
                    Dim CD7 As New PdfPCell(New Phrase(rdr(5).ToString, font2)) 'KEPUTUSAN
                    Dim CD8 As New PdfPCell(New Phrase(rdr(6).ToString, font2)) 'TAHUN

                    CD1.HorizontalAlignment = Element.ALIGN_CENTER
                    CD2.HorizontalAlignment = Element.ALIGN_CENTER
                    CD3.HorizontalAlignment = Element.ALIGN_LEFT
                    CD4.HorizontalAlignment = Element.ALIGN_CENTER
                    CD5.HorizontalAlignment = Element.ALIGN_CENTER
                    CD6.HorizontalAlignment = Element.ALIGN_LEFT
                    CD7.HorizontalAlignment = Element.ALIGN_CENTER
                    CD8.HorizontalAlignment = Element.ALIGN_CENTER

                    CD1.VerticalAlignment = Element.ALIGN_MIDDLE
                    CD2.VerticalAlignment = Element.ALIGN_LEFT
                    CD3.VerticalAlignment = Element.ALIGN_MIDDLE
                    CD4.VerticalAlignment = Element.ALIGN_MIDDLE
                    CD5.VerticalAlignment = Element.ALIGN_MIDDLE
                    CD6.VerticalAlignment = Element.ALIGN_LEFT
                    CD7.VerticalAlignment = Element.ALIGN_MIDDLE
                    CD8.VerticalAlignment = Element.ALIGN_MIDDLE

                    table.AddCell(CD1)
                    table.AddCell(CD2)
                    table.AddCell(CD3)
                    table.AddCell(CD4)
                    table.AddCell(CD5)
                    table.AddCell(CD6)
                    table.AddCell(CD7)
                    table.AddCell(CD8)
                    i = i + 1
                End While
            Else
                Msg(Me, "TIADA MAKLUMAT")
            End If
            rdr.Close()

            'Dump to pdf
            PdfWriter.GetInstance(pdfDoc, Response.OutputStream)
            pdfDoc.Open()
            pdfDoc.Add(table)
            pdfDoc.Close()
            HttpContext.Current.Response.ContentType = "application/pdf"
            HttpContext.Current.Response.AddHeader("content-disposition", _
           "attachment;filename=SENARAI_CALON_GAGAL" & DateTime.Now.ToString & ".pdf")
            HttpContext.Current.Response.Cache.SetCacheability(HttpCacheability.NoCache)
            HttpContext.Current.Response.Write(pdfDoc)
            HttpContext.Current.ApplicationInstance.CompleteRequest()

        Catch ex As Exception
            'Response.Write(ex.Message)
            Msg(Me, ex.Message)
        End Try
        con.Close()
        con.Dispose()


    End Sub
End Class


'Public Class itsEvents
'    Inherits PdfPageEventHelper

'    Public Overrides Sub OnStartPage(ByVal writer As iTextSharp.text.pdf.PdfWriter, ByVal document As iTextSharp.text.Document)
'        'Dim ch As New Chunk("This is my Stack Overflow Header on page " & writer.PageNumber)
'        Dim ch As New Chunk("Laporan Statistik Calon Gagal ")
'        document.Add(ch)
'    End Sub
'End Class