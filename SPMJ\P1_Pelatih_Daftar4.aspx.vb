﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class P1_Pelatih_Daftar4
    Inherits System.Web.UI.Page
    Dim Totalx, CLx, PLx, CLP, LP As Integer

    Public Sub Isi_Subjek(ByVal X As Int16)
        Cb_Muet.Visible = True : Cb_Muetx.Visible = False
        Cb_Luar.Visible = True : Cb_Luar.SelectedIndex = 0 : Cb_Luarx.Visible = False
        Cb_SbjM.Visible = True : Cb_SbjM.SelectedIndex = 0 : Cb_SbjMx.Visible = False

        Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
        Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
        Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
        Cb_Sbj5x.Visible = False : Cb_Sbj6x.Visible = False : Cb_Sbj7x.Visible = False : Cb_Sbj8x.Visible = False : Cb_Sbj9x.Visible = False
        Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
        Bt4.Visible = True : Bt5.Visible = False : Bt6.Visible = False : Bt7.Visible = False : Bt8.Visible = False
        Textbox8.Visible = False : Textbox9.Visible = False : Textbox10.Visible = False : Textbox11.Visible = False : Textbox12.Visible = False

        Cb_SbjT1.Items.Clear() : Cb_SbjT2.Items.Clear() : Cb_SbjT3.Items.Clear() : Cb_SbjT4.Items.Clear() : Cb_SbjT5.Items.Clear()
        Cb_SbjT1.Items.Add("") : Cb_SbjT2.Items.Add("") : Cb_SbjT3.Items.Add("") : Cb_SbjT4.Items.Add("") : Cb_SbjT5.Items.Add("")
        Cb_SbjT1.Visible = True : Cb_SbjT2.Visible = True : Cb_SbjT3.Visible = True : Cb_SbjT4.Visible = True : Cb_SbjT5.Visible = True
        Cb_SbjT1x.Visible = False : Cb_SbjT2x.Visible = False : Cb_SbjT3x.Visible = False : Cb_SbjT4x.Visible = False : Cb_SbjT5x.Visible = False

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        If X = 1 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'PMR' order by dc_subjek"
        If X = 2 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'SPM' order by dc_subjek"
        If X = 3 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'STPM' order by dc_subjek"
        If X = 0 Then Exit Sub
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            If Cb_Kursus.SelectedIndex <> 4 Then

                If Rdr(0) = "MUET" Then
                Else
                    Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
                    Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT1.Items.Add(Rdr(0)) : Cb_SbjT1.Items.Item(Cb_SbjT1.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT2.Items.Add(Rdr(0)) : Cb_SbjT2.Items.Item(Cb_SbjT2.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT3.Items.Add(Rdr(0)) : Cb_SbjT3.Items.Item(Cb_SbjT3.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT4.Items.Add(Rdr(0)) : Cb_SbjT4.Items.Item(Cb_SbjT4.Items.Count - 1).Value = Rdr(1)
                    Cb_SbjT5.Items.Add(Rdr(0)) : Cb_SbjT5.Items.Item(Cb_SbjT5.Items.Count - 1).Value = Rdr(1)
                End If
            Else
                Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
                Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
            End If
        End While
        Rdr.Close()
        Cn.Close()

        If X = 1 Then
            Gred_PMR(Cb_Kpts1)
            Gred_PMR(Cb_Kpts2)
            Gred_PMR(Cb_Kpts3)
            Gred_PMR(Cb_Kpts4)
            Gred_PMR(Cb_Kpts5)
            Gred_PMR(Cb_Kpts6)
            Gred_PMR(Cb_Kpts7)
            Gred_PMR(Cb_Kpts8)
            Gred_PMR(Cb_Kpts9)

        ElseIf X = 2 Then
            Gred_SPM(Cb_Kpts1)
            Gred_SPM(Cb_Kpts2)
            Gred_SPM(Cb_Kpts3)
            Gred_SPM(Cb_Kpts4)
            Gred_SPM(Cb_Kpts5)
            Gred_SPM(Cb_Kpts6)
            Gred_SPM(Cb_Kpts7)
            Gred_SPM(Cb_Kpts8)
            Gred_SPM(Cb_Kpts9)

        ElseIf X = 3 Then
            Gred_STPM(Cb_KptsT1)
            Gred_STPM(Cb_KptsT2)
            Gred_STPM(Cb_KptsT3)
            Gred_STPM(Cb_KptsT4)
            Gred_STPM(Cb_KptsT5)
        Else
        End If
    End Sub

    Public Sub Isi_Subjek_2(ByVal X As Int16)
        Cb_Muet.Visible = True : Cb_Muetx.Visible = False
        Cb_Luar.Visible = True : Cb_Luar.SelectedIndex = 0 : Cb_Luarx.Visible = False
        Cb_SbjM.Visible = True : Cb_SbjM.SelectedIndex = 0 : Cb_SbjMx.Visible = False

        Cb_Sbj5.Items.Clear() : Cb_Sbj6.Items.Clear() : Cb_Sbj7.Items.Clear() : Cb_Sbj8.Items.Clear() : Cb_Sbj9.Items.Clear()
        Cb_Sbj5.Items.Add("") : Cb_Sbj6.Items.Add("") : Cb_Sbj7.Items.Add("") : Cb_Sbj8.Items.Add("") : Cb_Sbj9.Items.Add("")
        Cb_Sbj5.Visible = False : Cb_Sbj6.Visible = False : Cb_Sbj7.Visible = False : Cb_Sbj8.Visible = False : Cb_Sbj9.Visible = False
        Cb_Sbj5x.Visible = False : Cb_Sbj6x.Visible = False : Cb_Sbj7x.Visible = False : Cb_Sbj8x.Visible = False : Cb_Sbj9x.Visible = False
        Cb_Kpts5.Visible = False : Cb_Kpts6.Visible = False : Cb_Kpts7.Visible = False : Cb_Kpts8.Visible = False : Cb_Kpts9.Visible = False
        Bt4.Visible = True : Bt5.Visible = False : Bt6.Visible = False : Bt7.Visible = False : Bt8.Visible = False
        Textbox8.Visible = False : Textbox9.Visible = False : Textbox10.Visible = False : Textbox11.Visible = False : Textbox12.Visible = False

        'Cb_SbjT1.Items.Clear() : Cb_SbjT2.Items.Clear() : Cb_SbjT3.Items.Clear() : Cb_SbjT4.Items.Clear() : Cb_SbjT5.Items.Clear()
        'Cb_SbjT1.Items.Add("") : Cb_SbjT2.Items.Add("") : Cb_SbjT3.Items.Add("") : Cb_SbjT4.Items.Add("") : Cb_SbjT5.Items.Add("")
        'Cb_SbjT1.Visible = True : Cb_SbjT2.Visible = True : Cb_SbjT3.Visible = True : Cb_SbjT4.Visible = True : Cb_SbjT5.Visible = True
        'Cb_SbjT1x.Visible = False : Cb_SbjT2x.Visible = False : Cb_SbjT3x.Visible = False : Cb_SbjT4x.Visible = False : Cb_SbjT5x.Visible = False

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        'If X = 1 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'PMR' order by dc_subjek"
        If X = 2 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'SPM' order by dc_subjek"
        'If X = 3 Then Cmd.CommandText = "select dc_subjek, id_subjek from pn_subjek where id_jenis = 'STPM' order by dc_subjek"
        If X = 0 Then Exit Sub
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            Cb_Sbj5.Items.Add(Rdr(0)) : Cb_Sbj5.Items.Item(Cb_Sbj5.Items.Count - 1).Value = Rdr(1)
            Cb_Sbj6.Items.Add(Rdr(0)) : Cb_Sbj6.Items.Item(Cb_Sbj6.Items.Count - 1).Value = Rdr(1)
            Cb_Sbj7.Items.Add(Rdr(0)) : Cb_Sbj7.Items.Item(Cb_Sbj7.Items.Count - 1).Value = Rdr(1)
            Cb_Sbj8.Items.Add(Rdr(0)) : Cb_Sbj8.Items.Item(Cb_Sbj8.Items.Count - 1).Value = Rdr(1)
            Cb_Sbj9.Items.Add(Rdr(0)) : Cb_Sbj9.Items.Item(Cb_Sbj9.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()


        If X = 2 Then
            Gred_SPM(Cb_Kpts1)
            Gred_SPM(Cb_Kpts2)
            Gred_SPM(Cb_Kpts3)
            Gred_SPM(Cb_Kpts4)
            Gred_SPM(Cb_Kpts5)
            Gred_SPM(Cb_Kpts6)
            Gred_SPM(Cb_Kpts7)
            Gred_SPM(Cb_Kpts8)
            Gred_SPM(Cb_Kpts9)
        End If
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Pelatih_Daftar", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        UICulture = "en-GB"
        Culture = "en-GB"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'Comment Original 20122019 -OSH  
        'If Cb_NoKP.SelectedIndex = 0 Then Tx_NoKP.MaxLength = 12 : Fn_Negara(1)

        'switch states to country for international students 20122019 - OSH 
        If Cb_NoKP.SelectedIndex = 0 Then Tx_NoKP.MaxLength = 12 : Fn_Negara(1) : Fn_Negeri(1)


        'NEGERI
        'Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        'Rdr = Cmd.ExecuteReader()
        'Cb_TP_Negeri.Items.Clear()
        'Cb_TP_Negeri.Items.Add("")
        'Cb_W_Negeri.Items.Clear()
        'Cb_W_Negeri.Items.Add("")
        'While Rdr.Read
        '    Cb_TP_Negeri.Items.Add(Rdr(0))
        '    Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr(1)
        '    Cb_W_Negeri.Items.Add(Rdr(0))
        '    Cb_W_Negeri.Items.Item(Cb_W_Negeri.Items.Count - 1).Value = Rdr(1)
        'End While
        'Rdr.Close()

        'KURSUS
        'Comment Original 26092019 - OSH 
        Cmd.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS ORDER BY ID_KURSUS"

        ' Improvement options list without degree 26092019 - OSH 
        'Cmd.CommandText = "SELECT Dc_KURSUS, Id_KURSUS FROM PN_KURSUS WHERE ID_KURSUS NOT IN (5) ORDER BY ID_KURSUS"
        Rdr = Cmd.ExecuteReader()
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("")
        While Rdr.Read
            Cb_Kursus.Items.Add(Rdr(0))
            Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'BULAN
        Cb_Sesi_Bulan.Items.Add("(BULAN)") : Cb_Sesi_Bln.Items.Add("(BULAN)")
        Cb_Sesi_Bulan.Items.Add("JANUARI") : Cb_Sesi_Bln.Items.Add("JANUARI")
        Cb_Sesi_Bulan.Items.Add("FEBRUARI") : Cb_Sesi_Bln.Items.Add("FEBRUARI")
        Cb_Sesi_Bulan.Items.Add("MAC") : Cb_Sesi_Bln.Items.Add("MAC")
        Cb_Sesi_Bulan.Items.Add("APRIL") : Cb_Sesi_Bln.Items.Add("APRIL")
        Cb_Sesi_Bulan.Items.Add("MEI") : Cb_Sesi_Bln.Items.Add("MEI")
        Cb_Sesi_Bulan.Items.Add("JUN") : Cb_Sesi_Bln.Items.Add("JUN")
        Cb_Sesi_Bulan.Items.Add("JULAI") : Cb_Sesi_Bln.Items.Add("JULAI")
        Cb_Sesi_Bulan.Items.Add("OGOS") : Cb_Sesi_Bln.Items.Add("OGOS")
        Cb_Sesi_Bulan.Items.Add("SEPTEMBER") : Cb_Sesi_Bln.Items.Add("SEPTEMBER")
        Cb_Sesi_Bulan.Items.Add("OKTOBER") : Cb_Sesi_Bln.Items.Add("OKTOBER")
        Cb_Sesi_Bulan.Items.Add("NOVEMBER") : Cb_Sesi_Bln.Items.Add("NOVEMBER")
        Cb_Sesi_Bulan.Items.Add("DISEMBER") : Cb_Sesi_Bln.Items.Add("DISEMBER")

        'TAHUN
        Cb_Sesi_Tahun.Items.Add("(TAHUN)")
        Cb_Sesi_Thn.Items.Add("(TAHUN)")
        'For i = 0 To 8
        For i = 0 To 5
            'For i = 0 To 6
            Cb_Sesi_Tahun.Items.Add(Year(Now) - i)
            Cb_Sesi_Thn.Items.Add(Year(Now) - i)
        Next

        'KOLEJ
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ WHERE JENIS=" & RadioButtonList1.SelectedValue & " ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'AGAMA -06082013-OSH
        Cmd.CommandText = "SELECT DC_AGAMA, ID_AGAMA FROM SPMJ_REF_AGAMA ORDER BY ID_AGAMA"
        Rdr = Cmd.ExecuteReader()
        Cb_Agama.Items.Clear()
        Cb_Agama.Items.Add("")
        While Rdr.Read
            Cb_Agama.Items.Add(Rdr(0))
            Cb_Agama.Items.Item(Cb_Agama.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'TAJAAN
        Cmd.CommandText = "SELECT DC_TAJAAN, Id_TAJAAN FROM PN_TAJAAN ORDER BY DC_TAJAAN"
        Rdr = Cmd.ExecuteReader()
        Cb_Tajaan.Items.Clear()
        Cb_Tajaan.Items.Add("")
        While Rdr.Read
            Cb_Tajaan.Items.Add(Rdr(0))
            Cb_Tajaan.Items.Item(Cb_Tajaan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

    End Sub

    Protected Sub Bt4_Click(ByVal sender As Object, ByVal e As EventArgs) Handles Bt4.Click

        'If Panel5.Visible = True Then
        '    If Cb_Kpts2.Enabled = True And Cb_Kpts2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #2!") : Exit Sub
        'Else
        '    If Cb_Kpts1.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #1!") : Exit Sub
        '    If Cb_Kpts2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #2!") : Exit Sub
        'End If
        'If Cb_Kpts3.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #3!") : Exit Sub
        Dim L As Integer = 0

        If Session("POLISI") = "LAMA" Then

            If Cb_Kpts1.Enabled = True And Cb_Kpts1.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #1!") : Exit Sub ' 24042012 add skip if that combobox disable
            If Cb_Kpts2.Enabled = True And Cb_Kpts2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #2!") : Exit Sub
            If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1
            If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1
            If L > 2 Then  Else Exit Sub
            'If L > 1 Then  Else Exit Sub  : suggest on request
        End If

        If Session("POLISI") = "BARU" Then
            If Panel5.Visible = True Then
                If Cb_Kpts2.Enabled = True And Cb_Kpts2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #2!") : Exit Sub
                If Cb_Kpts3.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #3!") : Exit Sub
            Else
                If Cb_Kpts1.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #1!") : Exit Sub
                If Cb_Kpts2.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #2!") : Exit Sub
                If Cb_Kpts3.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #3!") : Exit Sub
            End If
        End If

        Cb_Sbj5.Visible = True
        Cb_Kpts5.Visible = True
        Bt5.Visible = True
        Bt4.Visible = False
        Textbox8.Visible = True
    End Sub

    Private Sub Bt5_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Bt5.Click
        If Cb_Sbj5.SelectedIndex < 1 Or Cb_Kpts5.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #5!") : Exit Sub
        Cb_Sbj6.Visible = True
        Cb_Kpts6.Visible = True
        Bt6.Visible = True
        Bt5.Visible = False
        Textbox9.Visible = True
    End Sub

    Private Sub Bt6_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Bt6.Click
        If Cb_Sbj6.SelectedIndex < 1 Or Cb_Kpts6.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #6!") : Exit Sub
        Cb_Sbj7.Visible = True
        Cb_Kpts7.Visible = True
        Bt7.Visible = True
        Bt6.Visible = False
        Textbox10.Visible = True
    End Sub

    Private Sub Bt7_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Bt7.Click
        If Cb_Sbj7.SelectedIndex < 1 Or Cb_Kpts7.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #7!") : Exit Sub
        Cb_Sbj8.Visible = True
        Cb_Kpts8.Visible = True
        Bt8.Visible = True
        Bt7.Visible = False
        Textbox11.Visible = True
    End Sub

    Private Sub Bt8_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles Bt8.Click
        If Cb_Sbj8.SelectedIndex < 1 Or Cb_Kpts8.SelectedIndex < 1 Then Msg(Me, "Sila Lengkapkan Maklumat Subjek #8!") : Exit Sub
        Cb_Sbj9.Visible = True
        Cb_Kpts9.Visible = True
        Bt8.Enabled = False
        Textbox12.Visible = True
    End Sub

    Public Sub Fn_Polisi()
        If Cb_Sesi_Thn.SelectedIndex > 0 Then
            If Cb_Sesi_Bln.SelectedIndex > 0 Then
                'Uncompleted codes 12092018-OSH 
                'Add Check SOP 2015 04092018 - OSH
                If Cb_Sesi_Bln.SelectedIndex > 0 And CInt(Cb_Sesi_Thn.SelectedItem.Text) > 2015 Then
                    Session("POLISI") = "NEO"
                    If Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI/ASASI" Then
                        Matrik_2015()
                    ElseIf Cb_Kelayakan.SelectedItem.Text = "STPM" Then 'Add 16102019 - OSH 
                        Degree_SPM_2015()
                    End If

                    'Add policy 2010 timeline 04102019 - OSH 
                ElseIf CInt(Cb_Sesi_Thn.SelectedItem.Text) > 2010 And CInt(Cb_Sesi_Thn.SelectedItem.Text) < 2016 Then
                    'Comment Original 04102019 - OSH 
                    'ElseIf CInt(Cb_Sesi_Thn.SelectedItem.Text) > 2010 Then
                    Session("POLISI") = "BARU"
                ElseIf CInt(Cb_Sesi_Thn.SelectedItem.Text) = 2010 Then
                    If Cb_Sesi_Bln.SelectedIndex > 7 Then Session("POLISI") = "BARU" Else Session("POLISI") = "LAMA"
                ElseIf CInt(Cb_Sesi_Thn.SelectedItem.Text) < 2010 Then
                    Session("POLISI") = "LAMA"
                End If
                'If Cb_Kursus.SelectedIndex = 4 Or Cb_Kursus.SelectedIndex > 5 Then Panel1.Visible = True : PanelSemak.Visible = True : Cb_Sesi_Bln.Enabled = False : Cb_Sesi_Thn.Enabled = False
            Else
                Exit Sub
            End If
        Else
            Exit Sub
        End If
    End Sub

    Protected Sub Cb_Sesi_Bln_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Sesi_Bln.SelectedIndexChanged
        Cb_Sesi_Bulan.SelectedIndex = Cb_Sesi_Bln.SelectedIndex
        Fn_Polisi()
    End Sub

    Protected Sub Cb_Sesi_Thn_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Sesi_Thn.SelectedIndexChanged
        Cb_Sesi_Tahun.SelectedIndex = Cb_Sesi_Thn.SelectedIndex
        Fn_Polisi()
    End Sub

    Protected Sub Cb_Kursus_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kursus.SelectedIndexChanged
        Cb_Kelayakan.Enabled = True : Cb_Kelayakan.Items.Clear()
        Cb_Kelayakan_Taraf.Enabled = True : Cb_Kelayakan_Taraf.Visible = False : Cb_Kelayakan_Taraf.SelectedIndex = 0
        Cb_Sesi_Bln.Enabled = True : Cb_Sesi_Bln.SelectedIndex = 0
        Cb_Sesi_Thn.Enabled = True : Cb_Sesi_Thn.SelectedIndex = 0
        Panel1.Visible = False : Panel2.Visible = False : Panel3.Visible = False : Panel4.Visible = False : Panel5.Visible = False : Panel6.Visible = False : PanelSemak.Visible = False
        Panel1.Enabled = True : Panel2.Enabled = True : Panel3.Enabled = True : Panel4.Enabled = True : Panel5.Enabled = True : Panel6.Enabled = True : PanelSemak.Enabled = True
        Bt4.Enabled = False
        Cb_KptsMuet.SelectedIndex = 0 : Cb_Luar.SelectedIndex = 0 : Cb_KptsLuar.Text = "" : Cb_KptsM.Text = "" : Cb_Aliran.SelectedIndex = 0
        Cb_Kpts1.Enabled = True : Cb_Kpts2.Enabled = True : Cb_KptsT6.SelectedIndex = 0 : Cb_KptsT7.SelectedIndex = 0

        If Cb_Kursus.SelectedIndex = 1 Then
            Cb_Kelayakan.Items.Add("SPM") : Cb_Kelayakan.Items.Add("STPM") : Isi_Subjek(2)
            Cb_Kelayakan_Taraf.Visible = True
            Panel2.Visible = True : PanelSemak.Visible = True
            Bt4.Enabled = True

        ElseIf Cb_Kursus.SelectedIndex = 2 Then
            Cb_Kelayakan.Items.Add("SPM") : Cb_Kelayakan.Items.Add("PMR") : Cb_Kelayakan.Items.Add("SRP") : Isi_Subjek(2)
            Cb_Kelayakan_Taraf.Visible = True
            Panel2.Visible = True : PanelSemak.Visible = True
            Bt4.Enabled = True

        ElseIf Cb_Kursus.SelectedIndex = 3 Then
            Cb_Kelayakan.Items.Add("SPM") : Cb_Kelayakan.Items.Add("PMR") : Cb_Kelayakan.Items.Add("SRP") : Isi_Subjek(2)
            Cb_Kelayakan_Taraf.Visible = True
            Panel2.Visible = True : PanelSemak.Visible = True
            Bt4.Enabled = True

        ElseIf Cb_Kursus.SelectedIndex = 4 Then
            Cb_Kelayakan.Enabled = False
            Panel2.Visible = True : Panel2.Enabled = False : PanelSemak.Visible = True : PanelSemak.Enabled = False

        ElseIf Cb_Kursus.SelectedIndex = 5 Then
            Cb_Kelayakan.Items.Add("STPM") : Cb_Kelayakan.Items.Add("MATRIKULASI/ASASI") : Isi_Subjek(3)
            Cb_Kelayakan_Taraf.Visible = True
            'Comment Original 11052021 - OSH
            'Panel3.Visible = True : Panel6.Visible = True : PanelSemak.Visible = True
            'Fix Matriculation Panel Enbling Issue 11052021 - OSH 
            Panel3.Visible = True : Panel4.Visible = True : Panel6.Visible = True : PanelSemak.Visible = True

        ElseIf Cb_Kursus.SelectedIndex > 5 Then
            If Cb_Kursus.SelectedIndex = 8 Then Cb_Kelayakan.Items.Add("SPM")
            Cb_Kelayakan.Enabled = False
            Panel2.Visible = True : Panel2.Enabled = False : PanelSemak.Visible = True : PanelSemak.Enabled = False
        End If

        If Cb_Kursus.SelectedIndex = 8 Then cmd_Cari.Visible = True Else cmd_Cari.Visible = False
    End Sub

    Protected Sub Cb_Kelayakan_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kelayakan.SelectedIndexChanged
        Cb_Kelayakan_Taraf.Enabled = True : Cb_Kelayakan_Taraf.Visible = False : Cb_Kelayakan_Taraf.SelectedIndex = 0
        Panel1.Visible = False : Panel2.Visible = False : Panel3.Visible = False : Panel4.Visible = False : Panel5.Visible = False : Panel6.Visible = False : PanelSemak.Visible = True
        Panel1.Enabled = True : Panel2.Enabled = True : Panel3.Enabled = True : Panel4.Enabled = True : Panel5.Enabled = True : Panel6.Enabled = True : PanelSemak.Enabled = True
        Bt4.Enabled = False
        Cb_KptsMuet.SelectedIndex = 0 : Cb_Luar.SelectedIndex = 0 : Cb_KptsLuar.Text = "" : Cb_KptsM.Text = "" : Cb_Aliran.SelectedIndex = 0
        Cb_Kpts1.Enabled = True : Cb_Kpts2.Enabled = True : Cb_KptsT6.SelectedIndex = 0 : Cb_KptsT7.SelectedIndex = 0

        If Cb_Kelayakan.SelectedItem.Text = "SRP" Then
            Panel2.Visible = True : Panel2.Enabled = False : Isi_Subjek(1)
            PanelSemak.Enabled = False
            Panel1.Visible = True
        End If

        If Cb_Kelayakan.SelectedItem.Text = "PMR" Then
            Panel2.Visible = True : Isi_Subjek(1)
        End If

        If Cb_Kelayakan.SelectedItem.Text = "SPM" Then
            Cb_Kelayakan_Taraf.Visible = True
            Panel2.Visible = True : Isi_Subjek(2)
            Bt4.Enabled = True
        End If

        If Cb_Kelayakan.SelectedItem.Text = "STPM" Then
            Cb_Kelayakan_Taraf.Visible = True
            Panel3.Visible = True : Isi_Subjek(3)
            Panel6.Visible = True
        End If

        If Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI/ASASI" Then
            Panel4.Visible = True : Isi_Subjek(0)
            Panel6.Visible = True

            '2015 POLICY 
            If Cb_Sesi_Bln.SelectedIndex > 6 And Cb_Sesi_Thn.SelectedIndex > 0 Then
                If CInt(Cb_Sesi_Thn.SelectedItem.Text) > 2014 Then
                    Matrik_2015()
                End If
                'Else
                '    Msg(Me, "Sila Pilih Tahun")
            End If
        End If
    End Sub

    Protected Sub Cb_Kelayakan_Taraf_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kelayakan_Taraf.SelectedIndexChanged
        Panel1.Visible = False : Panel2.Visible = False : Panel3.Visible = False : Panel4.Visible = False : Panel5.Visible = False : Panel6.Visible = False : PanelSemak.Visible = True
        Panel1.Enabled = True : Panel2.Enabled = True : Panel3.Enabled = True : Panel4.Enabled = True : Panel5.Enabled = True : Panel6.Enabled = True : PanelSemak.Enabled = True
        Cb_KptsMuet.SelectedIndex = 0 : Cb_Luar.SelectedIndex = 0 : Cb_KptsLuar.Text = "" : Cb_KptsM.Text = "" : Cb_Aliran.SelectedIndex = 0
        Cb_KptsT6.SelectedIndex = 0 : Cb_KptsT7.SelectedIndex = 0

        If Cb_Kelayakan_Taraf.SelectedIndex = 1 Then
            If Cb_Kelayakan.SelectedItem.Text = "SPM" Then
                Panel2.Visible = True : Isi_Subjek(2)
                Cb_Kpts1.Enabled = False ': Cb_Kpts2.Enabled = False
                Panel5.Visible = True
                Cb_Luar.Items.Clear()
                Cb_Luar.Items.Add("(PILIHAN)")
                Cb_Luar.Items.Add("IELTS")
                Cb_Luar.Items.Add("TOEFL")
                Cb_Luar.Items.Add("ENGLISH GCE O LEVEL")
            End If
            If Cb_Kelayakan.SelectedItem.Text = "STPM" Then
                Panel3.Visible = True : Isi_Subjek(3)
                Cb_Aliran.Enabled = False
                Panel5.Visible = True
                Cb_Luar.Items.Clear()
                Cb_Luar.Items.Add("(PILIHAN)")
                Cb_Luar.Items.Add("IELTS")
                Cb_Luar.Items.Add("TOEFL")
            End If

        Else
            If Cb_Kelayakan.SelectedItem.Text = "SPM" Then
                Cb_Kpts1.Enabled = True ': Cb_Kpts2.Enabled = True
                Panel2.Visible = True : Isi_Subjek(2)
            End If
            If Cb_Kelayakan.SelectedItem.Text = "STPM" Then
                Panel3.Visible = True : Isi_Subjek(3)
                Cb_Aliran.Enabled = True
                Panel6.Visible = True
            End If
        End If
    End Sub

    Protected Sub cmdHantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar.Click
        If Chk_Subjek() = True Then Exit Sub
        If Cb_Kursus.SelectedValue = 8 Then If Chk_Staff() = True Then Exit Sub

        'Medan Mandatori...
        Dim X As String = ""
        'Comment Ori 06022013
        'If Tx_Nama.Text.Trim = "" Then X += "Nama, "
        'If Tx_NoKP.Text.Trim = "" Then X += "No. KP/Tentera/Pasport, "
        'If Cb_Warga.Text.Trim = "" Then X += "Warganegara, "
        'If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi Latihan, "
        'If Cb_Sesi_Bulan.Text.Trim = "" Or Cb_Sesi_Bulan.Text.Trim = "(BULAN)" Or Cb_Sesi_Tahun.Text.Trim = "" Or Cb_Sesi_Tahun.Text.Trim = "(TAHUN)" Then X += "Sesi Pengambilan, "
        'If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "
        'If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:  " & X) : Exit Sub

        'Add sponsorship mandotory field 06022013
        If Tx_Nama.Text.Trim = "" Then X += "Nama, "
        If Tx_NoKP.Text.Trim = "" Then X += "No. KP/Tentera/Pasport, "
        If Cb_Warga.Text.Trim = "" Then X += "Warganegara, "
        If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi Latihan, "
        If Cb_Tajaan.Text.Trim = "" Then X += "Tajaan, "
        If Cb_Sesi_Bulan.Text.Trim = "" Or Cb_Sesi_Bulan.Text.Trim = "(BULAN)" Or Cb_Sesi_Tahun.Text.Trim = "" Or Cb_Sesi_Tahun.Text.Trim = "(TAHUN)" Then X += "Sesi Pengambilan, "
        If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "
        If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:  " & X) : Exit Sub
        'Check No KP length
        X = ""
        With Cb_NoKP
            If .SelectedIndex = 0 Then If Tx_NoKP.Text.Length < 12 Then X = "lengkap"
            If .SelectedIndex = 0 Then If Not IsNumeric(Tx_NoKP.Text) Then X = "tepat"
        End With
        If X.Trim = "" Then  Else Msg(Me, "Maklumat No Kad Pengenalan tidak " & X) : Tx_NoKP.Focus() : Exit Sub

        'Check format nama
        If Fn_NameCheck() = True Then  Else Msg(Me, "Format Nama Tidak Tepat!!!") : Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        'Comment Ori 04012013-OSH
        'Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text & "'"

        'Add Subject Exist Checker 12112018 - OSH
        If (Cb_Kursus.SelectedValue >= 1 And Cb_Kursus.SelectedValue < 5) Or Cb_Kursus.SelectedValue = 8 Then
            Cmd.CommandText = "select nokp from pelatih_kelayakan where nokp = '" & Tx_NoKP.Text & "'"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                Msg(Me, "Rekod Subjek Akademik Pelatih Ini Telah Ada!")
                Rdr.Close()
                Exit Sub
            End If
            Rdr.Close()
        End If


        'Improvement query by category 04012013-OSH
        Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text & "' AND J_Kursus='" & Cb_Kursus.SelectedValue & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Pelatih Ini Telah Ada!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()

        Dim SQL As String
        Try
            'Comment Original 1712019 - OSH 
            'SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, jenis_kp, warganegara, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
            'Add brith date 17122019 - OSH
            SQL = "insert pelatih (j_kursus, kelayakan, setaraf, nama, nokp, jenis_kp, warganegara, tkh_lahir, jantina, bangsa, agama, t_kahwin, tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel, emel, w_nama, w_alamat, w_poskod, w_bandar, w_negeri, w_negara, w_tel, id_kolej, tajaan, sesi_bulan, sesi_tahun, tkh_latihan_mula, ss1, ss2, ss3, ss4, ss5, ss6, ss7, ss8, log_id, log_tkh) values ("
            SQL += Cb_Kursus.SelectedItem.Value & ","
            If Cb_Kursus.SelectedValue = 4 Or Cb_Kursus.SelectedValue > 5 Then SQL += "''," Else SQL += "'" & Cb_Kelayakan.SelectedItem.Value & "',"
            SQL += "" & Cb_Kelayakan_Taraf.SelectedValue & ","
            SQL += "'" & Apo(Tx_Nama.Text.Trim.ToUpper) & "',"
            SQL += "'" & Replace(Tx_NoKP.Text.Trim.ToUpper, "-", "") & "',"
            SQL += Cb_NoKP.SelectedIndex & ","
            SQL += Cb_Warga.SelectedItem.Value & ","
            SQL += Chk_Tkh(Tx_Lahir.Text) & "," 'ADD BRITH DATE 17122019 - OSH  
            SQL += Cb_Jantina.SelectedIndex & ","
            SQL += Cb_Bangsa.SelectedIndex & ","
            SQL += Cb_Agama.SelectedIndex & ","
            SQL += Cb_Kahwin.SelectedIndex & ","
            SQL += "'" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "',"
            SQL += "'" & Tx_TP_Poskod.Text.Trim & "',"
            SQL += "'" & Tx_TP_Bandar.Text.Trim.ToUpper & "',"
            SQL += "'" & Cb_TP_Negeri.SelectedItem.Value & "',"
            SQL += "'" & Tx_Tel.Text.Trim & "',"
            SQL += "'" & Tx_Emel.Text.Trim & "',"
            SQL += "'" & Apo(Tx_W_Nama.Text.Trim.ToUpper) & "',"
            SQL += "'" & Apo(Tx_W_Alamat.Text.Trim.ToUpper) & "',"
            SQL += "'" & Tx_W_Poskod.Text.Trim & "',"
            SQL += "'" & Tx_W_Bandar.Text.Trim.ToUpper & "',"
            SQL += "'" & Cb_W_Negeri.SelectedItem.Value & "',"
            SQL += "'" & Cb_W_Negara.SelectedItem.Value & "',"
            SQL += "'" & Tx_W_Tel.Text.Trim & "',"
            SQL += "'" & Cb_Kolej.SelectedItem.Value & "',"
            SQL += "'" & Cb_Tajaan.SelectedItem.Value & "',"
            SQL += Cb_Sesi_Bulan.SelectedIndex & ","
            SQL += "'" & Cb_Sesi_Tahun.SelectedItem.Text & "',"
            SQL += Chk_Tkh(Tx_M_Latihan.Text) & ","
            SQL += SSemak(0) & ","
            SQL += SSemak(1) & ","
            SQL += SSemak(2) & ","
            SQL += SSemak(3) & ","
            SQL += SSemak(4) & ","
            SQL += SSemak(5) & ","
            SQL += SSemak(6) & ","
            SQL += SSemak(7) & ","
            SQL += "'" & Session("Id_PG") & "',"
            SQL += "getdate()"
            SQL += ")" & vbCrLf
            'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal, "Daftar Pelatih")
            'Cmd.CommandText = SQL
            'Cmd.ExecuteNonQuery()

            If Cb_Kursus.SelectedValue = 4 Or Cb_Kursus.SelectedValue > 5 Then
            Else
                'SQL = ""

                If Panel6.Visible = True Then 'muet
                    If Cb_KptsMuet.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','15','" & Cb_KptsMuet.SelectedItem.Text & "')" & vbCrLf
                End If
                If Panel5.Visible = True Then 'BI luar/setaraf
                    If Cb_Luar.SelectedIndex = 1 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-11','" & Cb_KptsLuar.Text.ToUpper & "')" & vbCrLf
                    If Cb_Luar.SelectedIndex = 2 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-12','" & Cb_KptsLuar.Text.ToUpper & "')" & vbCrLf
                    If Cb_Luar.SelectedIndex = 3 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-13','" & Cb_KptsLuar.Text.ToUpper & "')" & vbCrLf
                End If
                If Panel4.Visible = True Then 'matrik
                    If Cb_SbjM.SelectedIndex = 1 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-21','" & Cb_KptsM.Text.ToUpper & "')" & vbCrLf
                    If Cb_SbjM.SelectedIndex = 2 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-22','" & Cb_KptsM.Text.ToUpper & "')" & vbCrLf
                End If
                If Panel3.Visible = True Then 'stpm
                    If Cb_KptsT1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT1.SelectedItem.Value & "','" & Cb_KptsT1.SelectedItem.Text & "')" & vbCrLf
                    If Cb_KptsT2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT2.SelectedItem.Value & "','" & Cb_KptsT2.SelectedItem.Text & "')" & vbCrLf
                    If Cb_KptsT3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT3.SelectedItem.Value & "','" & Cb_KptsT3.SelectedItem.Text & "')" & vbCrLf
                    If Cb_KptsT4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT4.SelectedItem.Value & "','" & Cb_KptsT4.SelectedItem.Text & "')" & vbCrLf
                    If Cb_KptsT5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_SbjT5.SelectedItem.Value & "','" & Cb_KptsT5.SelectedItem.Text & "')" & vbCrLf

                    If Cb_Aliran.SelectedIndex = 2 Then
                        If Cb_KptsT6.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-3','" & Cb_KptsT6.SelectedItem.Text & "')" & vbCrLf
                        If Cb_KptsT7.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-4','" & Cb_KptsT7.SelectedItem.Text & "')" & vbCrLf
                    End If
                End If
                If Panel2.Visible = True Then 'spm
                    If Cb_Kpts1.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-1','" & Cb_Kpts1.SelectedItem.Text & "')" & vbCrLf
                    If Cb_Kpts2.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-2','" & Cb_Kpts2.SelectedItem.Text & "')" & vbCrLf
                    If Cb_Kpts3.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-3','" & Cb_Kpts3.SelectedItem.Text & "')" & vbCrLf
                    If Cb_Kpts4.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','-4','" & Cb_Kpts4.SelectedItem.Text & "')" & vbCrLf
                    If Cb_Kpts5.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj5.SelectedItem.Value & "','" & Cb_Kpts5.SelectedItem.Text & "')" & vbCrLf
                    If Cb_Kpts6.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj6.SelectedItem.Value & "','" & Cb_Kpts6.SelectedItem.Text & "')" & vbCrLf
                    If Cb_Kpts7.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj7.SelectedItem.Value & "','" & Cb_Kpts7.SelectedItem.Text & "')" & vbCrLf
                    If Cb_Kpts8.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj8.SelectedItem.Value & "','" & Cb_Kpts8.SelectedItem.Text & "')" & vbCrLf
                    If Cb_Kpts9.SelectedIndex > 0 Then SQL += "insert into pelatih_kelayakan (nokp, id_subjek, keputusan) values ('" & Tx_NoKP.Text & "','" & Cb_Sbj9.SelectedItem.Value & "','" & Cb_Kpts9.SelectedItem.Text & "')" & vbCrLf
                End If

            End If
            If SQL = "" Then
            Else
                'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal, "Daftar Pelatih")
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
            End If

            Cn.Close()
            Session("Msg_Tajuk") = "Pendaftaran Pelatih Baru"
            Session("Msg_Isi") = "Rekod Telah Dihantar..."
            Response.Redirect("p0_Mesej.aspx")
            'Msg(Me, "Rekod Telah Dihantar...")
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
    End Sub

    Function Fn_NameCheck()
        Dim checkOK, checkString, chS, chOk As String

        checkOK = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.@'/() "
        checkString = Tx_Nama.Text.Trim
        Fn_NameCheck = True
        For i As Integer = 0 To checkString.Length - 1
            chS = checkString.Substring(i, 1)
            For j As Integer = 0 To checkOK.Length - 1
                chOk = checkOK.Substring(j, 1)
                If chS = chOk Then Exit For
                If j = checkOK.Length - 1 Then Fn_NameCheck = False : Exit For
            Next
        Next

        Return Fn_NameCheck
    End Function

    Function SSemak(ByVal X As Int16)
        If chk_Semak.Items(X).Selected = True Then X = 1 Else X = 0
        Return X
    End Function

    Function Chk_Tkh(ByVal X As String)
        If IsDate(X) Then X = Format(CDate(X), "MM/dd/yyyy") : X = "'" & X & "'" Else X = "NULL"
        Return X
    End Function

    Function Chk_Subjek()
        If Cb_Kelayakan.Items.Count = 0 Then Chk_Subjek = False : Return Chk_Subjek : Exit Function
        If Cb_Kursus.SelectedIndex = 8 Then Chk_Subjek = False : Return Chk_Subjek : Exit Function
        Chk_Subjek = True
        Dim L, CL, PL As Int16, semak As String = "", Total As Double = 0, Tot As Double = 0

        If Session("POLISI") = "LAMA" Then
            If Cb_Kelayakan.SelectedItem.Text = "SPM" Then
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then L = L + 1
            ElseIf Cb_Kelayakan.SelectedItem.Text = "PMR" Then
                If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts5.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 Then L = L + 1
            ElseIf Cb_Kelayakan.SelectedItem.Text = "STPM" Then
                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1
                If Cb_KptsT6.SelectedIndex > 0 Then L = L + 1
                If Cb_KptsT7.SelectedIndex > 0 Then L = L + 1
            ElseIf Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI/ASASI" Then
                If Cb_KptsMuet.SelectedIndex > 0 Then L = L + 2
                If Cb_SbjM.SelectedIndex = 1 Then If CDbl(Cb_KptsM.Text) >= 2.0 Then L = L + 1
                If Cb_SbjM.SelectedIndex = 2 Then If CDbl(Cb_KptsM.Text) >= 2.0 Then L = L + 1
            ElseIf Cb_Kelayakan.SelectedItem.Text = "SRP" Then
                L = 3
            End If

            If Cb_Kursus.SelectedIndex = 4 Then
                L = 2
                If Cb_Sbj5.SelectedItem.Text = "MUET" Then L = L + 1
                If Cb_Sbj6.SelectedItem.Text = "MUET" Then L = L + 1
                If Cb_Sbj7.SelectedItem.Text = "MUET" Then L = L + 1
                If Cb_Sbj8.SelectedItem.Text = "MUET" Then L = L + 1
                If Cb_Sbj9.SelectedItem.Text = "MUET" Then L = L + 1
            End If

            If L > 2 Then semak = "L" Else semak = "G"
        End If

        If Session("POLISI") = "BARU" Then
            If Cb_Kelayakan.SelectedItem.Text = "SRP" Then semak = "L"
            If Cb_Kelayakan.SelectedItem.Text = "PMR" Then
                If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts5.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 Then L = L + 1
                If L > 2 Then semak = "L" Else semak = "G"
            End If
            If Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan_Taraf.SelectedIndex = 0 Then 'spm local
                If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 10 Then PL = PL + 1
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 10 Then PL = PL + 1
                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then CL = CL + 1
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Dip_Credit_2015(Cb_Sbj5) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Dip_Credit_2015(Cb_Sbj6) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Dip_Credit_2015(Cb_Sbj7) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Dip_Credit_2015(Cb_Sbj8) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Dip_Credit_2015(Cb_Sbj9) = True Then CL = CL + 1 Else L = L + 1
                If PL = 2 Then semak = "L" Else semak = "G"
                If semak = "L" Then If CL = 0 Then semak = "G" Else semak = "L"
                If semak = "L" Then Total = PL + CL + L
                'If Total > 6 Then semak = "L" Else semak = "G" 'Comment original 08062017 -OSH
                If Total >= 6 Then semak = "L" Else semak = "G"
            End If
            If Cb_Kelayakan.SelectedItem.Text = "SPM" And Cb_Kelayakan_Taraf.SelectedIndex = 1 Then 'spm staraf/luar negara              
                If Cb_Luar.SelectedIndex = 1 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 5.5 Then PL = PL + 1
                If Cb_Luar.SelectedIndex = 2 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 550 Then PL = PL + 1 ' BI staraf/luar negara
                If Cb_Luar.SelectedIndex = 3 And Cb_KptsLuar.Text <> "" Then If Cb_KptsLuar.Text.ToUpper <> "G" Then PL = PL + 1
                If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 10 Then PL = PL + 1

                If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then CL = CL + 1
                If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Dip_Credit_2015(Cb_Sbj5) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Dip_Credit_2015(Cb_Sbj6) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Dip_Credit_2015(Cb_Sbj7) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Dip_Credit_2015(Cb_Sbj8) = True Then CL = CL + 1 Else L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Dip_Credit_2015(Cb_Sbj9) = True Then CL = CL + 1 Else L = L + 1
                If PL = 1 Then semak = "L" Else semak = "G"
                If semak = "L" Then If CL = 0 Then semak = "G" Else semak = "L"
                If semak = "L" Then Total = PL + CL + L
                'Add Fixed for 5 credit 15082013-OSH
                If Total >= 5 Then semak = "L" Else semak = "G"
                'Comment Ori 15082013-OSH
                'If Total > 5 Then semak = "L" Else semak = "G"
            End If
            If Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedIndex = 0 Then 'stpm local
                If Cb_KptsMuet.SelectedIndex > 0 Then PL = PL + 1

                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                Total = Total / L

                If PL = 1 Then semak = "L" Else semak = "G"
                'If semak = "L" Then If Total >= 2.5 Then semak = "L" Else semak = "G" ' Comment Original 02062017 - OSH
                If semak = "L" Then
                    If Cb_Aliran.SelectedIndex = 2 Then
                        If Cb_KptsT6.SelectedIndex > 0 And Cb_KptsT6.SelectedIndex < 8 Then CL = CL + 1
                        If Cb_KptsT7.SelectedIndex > 0 And Cb_KptsT7.SelectedIndex < 8 Then CL = CL + 1
                        If semak = "L" Then If CL = 2 Then semak = "L" Else semak = "G"
                    End If
                End If
            End If
            If Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedIndex = 1 Then
                If Cb_Luar.SelectedIndex = 0 Then PL = PL + 1
                If Cb_Luar.SelectedIndex = 1 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 5.5 Then PL = PL + 1
                If Cb_Luar.SelectedIndex = 2 And Cb_KptsLuar.Text <> "" Then If CInt(Cb_KptsLuar.Text) >= 550 Then PL = PL + 1 'BI Setaraf/Luar Negara                

                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                Total = Total / L
                If PL = 1 Then semak = "L" Else semak = "G"
                If semak = "L" Then If Total >= 2.5 Then semak = "L" Else semak = "G"
            End If

            'Comment Original 12112018 - OSH
            'If Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI/ASASI" Then
            '    If Cb_KptsMuet.SelectedIndex > 0 Then L = L + 1
            '    If Cb_SbjM.SelectedIndex = 1 Then If CDbl(Cb_KptsM.Text) >= 2.5 Then L = L + 1
            '    If Cb_SbjM.SelectedIndex = 2 Then If CDbl(Cb_KptsM.Text) >= 3.0 Then L = L + 1
            '    If L > 1 Then semak = "L" Else semak = "G"
            'End If


            If Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI/ASASI" And Cb_Sesi_Thn.SelectedValue > 2009 Then ' Enrty Policy 2010
                If Cb_KptsMuet.SelectedIndex > 0 Then L = L + 1
                If Cb_SbjM.SelectedIndex = 1 Then If CDbl(Cb_KptsM.Text) >= 2.5 Then L = L + 1
                If Cb_SbjM.SelectedIndex = 2 Then If CDbl(Cb_KptsM.Text) >= 3.0 Then L = L + 1
                If L > 1 Then semak = "L" Else semak = "G"
            End If

            'Fix SOP 2010 Final Exit 24102019 - OSH 
            If semak = "L" Then
                Chk_Subjek = False
            End If
        End If

    
        'Add entry reqirement SOP A/A Nursing Programmes 04092018 - OSH
        If Session("POLISI") = "NEO" Then
            'Cert Level
            If (Cb_Kursus.SelectedValue = "2" Or Cb_Kursus.SelectedValue = "3") And Cb_Kelayakan.SelectedItem.Text = "SPM" Then
                If Cb_Kpts1.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts2.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts3.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts4.SelectedIndex > 0 Then L = L + 1
                If Cb_Kpts5.SelectedIndex > 0 Then If Chk_Sbj_Cert_Neo(Cb_Sbj5) = True Then L = L + 1
                If Cb_Kpts6.SelectedIndex > 0 Then If Chk_Sbj_Cert_Neo(Cb_Sbj6) = True Then L = L + 1
                If Cb_Kpts7.SelectedIndex > 0 Then If Chk_Sbj_Cert_Neo(Cb_Sbj7) = True Then L = L + 1
                If Cb_Kpts8.SelectedIndex > 0 Then If Chk_Sbj_Cert_Neo(Cb_Sbj8) = True Then L = L + 1
                If Cb_Kpts9.SelectedIndex > 0 Then If Chk_Sbj_Cert_Neo(Cb_Sbj9) = True Then L = L + 1
                If L > 3 Then semak = "L" Else semak = "G"
            End If

            'SPM Level
            If Cb_Kursus.SelectedValue = "1" And Cb_Kelayakan.SelectedItem.Text = "SPM" Then
                SPM() 'SPM RULES 03102019
                If Totalx > 4 And Cb_Kpts3.SelectedIndex < 8 Then semak = "L" Else semak = "G"
            End If

            'DEGREE 2015
            If Cb_Kursus.SelectedValue = "5" And Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI/ASASI" Then
                'Improve matriculation and fundation rules 12112018 - OSH
                'If Cb_Kelayakan.SelectedItem.Text = "MATRIKULASI/ASASI" And Cb_Sesi_Thn.SelectedValue > 2015 Then ' Enrty Policy 2015
                'If Cb_KptsMuet.SelectedIndex > 0 Then L = L + 1
                If Cb_KptsMuet.SelectedValue > 3 Then L = L + 1
                If Cb_SbjM.SelectedIndex = 1 Then If CDbl(Cb_KptsM.Text) >= 2.5 Then L = L + 1
                If Cb_SbjM.SelectedIndex = 2 Then If CDbl(Cb_KptsM.Text) >= 2.5 Then L = L + 1
                SPM() 'SPM RULES 03102019
                If L > 1 Then semak = "L" Else semak = "G"
                If Totalx > 4 Then semak = "L" Else semak = "G"
            End If

            'DEGREE 2015 - STPM - 16102019 - OSH
            If Cb_Kursus.SelectedValue = "5" And Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedIndex = 0 Then
                If Cb_KptsMuet.SelectedIndex > 3 Then PL = PL + 1

                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                Total = Total / L
                If PL = 1 Then semak = "L" Else semak = "G"
                If semak = "L" Then If Total >= 2.5 Then semak = "L" Else semak = "G"
                If Cb_Aliran.SelectedIndex = 2 Then
                    If Cb_KptsT6.SelectedIndex > 0 And Cb_KptsT6.SelectedIndex < 8 Then CL = CL + 1
                    If Cb_KptsT7.SelectedIndex > 0 And Cb_KptsT7.SelectedIndex < 8 Then CL = CL + 1
                    If semak = "L" Then If CL = 2 Then semak = "L" Else semak = "G"
                End If
                SPM() 'SPM RULES 03102019
                If Totalx > 4 Then semak = "L" Else semak = "G"
            End If

            'DEGREE 2015 - LOCAL - A-LEVEL CERTIFICATE 21102019 - OSH   
            If Cb_Kursus.SelectedValue = "5" And Cb_Kelayakan.SelectedItem.Text = "STPM" And Cb_Kelayakan_Taraf.SelectedIndex = 1 Then
                'Comment Original 06082021 - OSH 
                'If Cb_KptsMuet.SelectedIndex > 3 Then PL = PL + 1

                'Fix Intenational English 06082021 - OSH 
                If Cb_Luar.SelectedIndex = 1 Then
                    If Cb_KptsLuar.Text > "5.4" Then PL = PL + 1 'IETS
                ElseIf Cb_Luar.SelectedIndex = 2 Then
                    If Cb_KptsLuar.Text > "515" Then PL = PL + 1 'TOFEL
                End If


                If Cb_KptsT1.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT1.SelectedValue)
                If Cb_KptsT2.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT2.SelectedValue)
                If Cb_KptsT3.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT3.SelectedValue)
                If Cb_KptsT4.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT4.SelectedValue)
                If Cb_KptsT5.SelectedIndex > 0 Then L = L + 1 : Total = Total + CDbl(Cb_KptsT5.SelectedValue)
                Total = Total / L
                If PL = 1 Then semak = "L" Else semak = "G"
                If semak = "L" Then If Total >= 2.5 Then semak = "L" Else semak = "G"
                SPM() 'SPM RULES 03102019
                If Totalx > 4 Then semak = "L" Else semak = "G"
            End If

                If semak = "L" Then
                    Chk_Subjek = False
                End If
            End If
    End Function
    Public Sub SPM()
        If Cb_Kpts1.SelectedIndex > 0 And Cb_Kpts1.SelectedIndex < 8 Then CLx = CLx + 1 'malay 
        'Improve check english rule policy 2015 03102019 - OSH 
        If Cb_Kpts2.SelectedIndex > 7 And Cb_Kpts2.SelectedIndex < 10 Then PLx = PLx + 0 'english pass
        If Cb_Kpts2.SelectedIndex > 0 And Cb_Kpts2.SelectedIndex < 8 Then PLx = PLx + 1 'english credit
        If Cb_Kpts3.SelectedIndex > 0 And Cb_Kpts3.SelectedIndex < 8 Then CLx = CLx + 1 ' math  
        If Cb_Kpts4.SelectedIndex > 0 And Cb_Kpts4.SelectedIndex < 8 Then CLP = CLP + 1 ' science 

        'Check Biology Credit or Pass 02102019 - OSH
        If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then 'credit
            If Chk_Sbj_Dip_Pass_2015(Cb_Sbj5) = True Then Policy_15(1)
            If Chk_Sbj_Math_2018(Cb_Sbj5) = True Then CLx = CLx + 1
            If Chk_Sbj_Credit_2018(Cb_Sbj5) = False Then LP = LP + 1
        ElseIf Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 9 Then 'pass
            If Chk_Sbj_Dip_Pass_2015(Cb_Sbj5) = True Then Policy_15(2)
            If Chk_Sbj_Math_2018(Cb_Sbj5) = True Then CLx = CLx + 1
            If Chk_Sbj_Credit_2018(Cb_Sbj5) = False Then LP = LP + 1
        End If
        If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then 'credit
            If Chk_Sbj_Dip_Pass_2015(Cb_Sbj6) = True Then Policy_15(1)
            If Chk_Sbj_Math_2018(Cb_Sbj6) = True Then CLx = CLx + 1
            If Chk_Sbj_Credit_2018(Cb_Sbj6) = False Then LP = LP + 1
        ElseIf Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 9 Then 'pass
            If Chk_Sbj_Dip_Pass_2015(Cb_Sbj6) = True Then Policy_15(2)
            If Chk_Sbj_Math_2018(Cb_Sbj6) = True Then CLx = CLx + 1
            If Chk_Sbj_Credit_2018(Cb_Sbj6) = False Then LP = LP + 1
        End If
        If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then 'credit
            If Chk_Sbj_Dip_Pass_2015(Cb_Sbj7) = True Then Policy_15(1)
            If Chk_Sbj_Math_2018(Cb_Sbj7) = True Then CLx = CLx + 1
            If Chk_Sbj_Credit_2018(Cb_Sbj7) = False Then LP = LP + 1
        ElseIf Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 9 Then 'pass
            If Chk_Sbj_Dip_Pass_2015(Cb_Sbj7) = True Then Policy_15(2)
            If Chk_Sbj_Math_2018(Cb_Sbj7) = True Then CLx = CLx + 1
            If Chk_Sbj_Credit_2018(Cb_Sbj7) = False Then LP = LP + 1
        End If
        If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then 'credit
            If Chk_Sbj_Dip_Pass_2015(Cb_Sbj8) = True Then Policy_15(1)
            If Chk_Sbj_Math_2018(Cb_Sbj8) = True Then CLx = CLx + 1
            If Chk_Sbj_Credit_2018(Cb_Sbj8) = False Then LP = LP + 1
        ElseIf Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 9 Then 'pass
            If Chk_Sbj_Dip_Pass_2015(Cb_Sbj8) = True Then Policy_15(2)
            If Chk_Sbj_Math_2018(Cb_Sbj8) = True Then CLx = CLx + 1
            If Chk_Sbj_Credit_2018(Cb_Sbj8) = False Then LP = LP + 1
        End If
        If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then 'credit
            If Chk_Sbj_Dip_Pass_2015(Cb_Sbj9) = True Then Policy_15(1)
            If Chk_Sbj_Math_2018(Cb_Sbj9) = True Then CLx = CLx + 1
            If Chk_Sbj_Credit_2018(Cb_Sbj9) = False Then LP = LP + 1
        ElseIf Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 9 Then 'pass
            If Chk_Sbj_Dip_Pass_2015(Cb_Sbj9) = True Then Policy_15(2)
            If Chk_Sbj_Math_2018(Cb_Sbj9) = True Then CLx = CLx + 1
            If Chk_Sbj_Credit_2018(Cb_Sbj9) = False Then LP = LP + 1
        End If


        If (CLx + PLx) > 0 Then
            If PLx = 0 Then 'PASS
                Totalx = CLx + CLP + LP
            ElseIf PLx = 1 Then 'CREDIT
                Totalx = CLx + PLx + CLP + LP
            End If
        End If

    End Sub

    Function Chk_Sbj_Dip_Pass_2015(ByVal cb As DropDownList)
        Chk_Sbj_Dip_Pass_2015 = False

        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Sbj_Dip_Pass_2015 = True : Exit Function

        Return Chk_Sbj_Dip_Pass_2015
    End Function



    Public Sub Policy_15(ByVal I As Integer)
        Dim q As Integer

        If I = 1 Then 'Credit in Biology
            If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then
                If Chk_Dip_Credit_2015(Cb_Sbj5) = False Then q = q + 1
                If Chk_Pure_Dip_Credit_2015(Cb_Sbj5) = True Then CLP = CLP + 1
            End If

            If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then
                If Chk_Dip_Credit_2015(Cb_Sbj6) = False Then q = q + 1
                If Chk_Pure_Dip_Credit_2015(Cb_Sbj6) = True Then CLP = CLP + 1
            End If

            If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then
                If Chk_Dip_Credit_2015(Cb_Sbj7) = False Then q = q + 1
                If Chk_Pure_Dip_Credit_2015(Cb_Sbj7) = True Then CLP = CLP + 1
            End If

            If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then
                If Chk_Dip_Credit_2015(Cb_Sbj8) = False Then q = q + 1
                If Chk_Pure_Dip_Credit_2015(Cb_Sbj8) = True Then CLP = CLP + 1
            End If

            If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then
                If Chk_Dip_Credit_2015(Cb_Sbj9) = False Then q = q + 1
                If Chk_Pure_Dip_Credit_2015(Cb_Sbj9) = True Then CLP = CLP + 1
            End If

            If CLP = 0 And q > 0 Then
                CLP = CLP + 1
            End If

        ElseIf I = 2 Then 'Pass in Biology

            If Cb_Kpts5.SelectedIndex > 0 And Cb_Kpts5.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj5) = True Then CLP = CLP + 1
            If Cb_Kpts6.SelectedIndex > 0 And Cb_Kpts6.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj6) = True Then CLP = CLP + 1
            If Cb_Kpts7.SelectedIndex > 0 And Cb_Kpts7.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj7) = True Then CLP = CLP + 1
            If Cb_Kpts8.SelectedIndex > 0 And Cb_Kpts8.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj8) = True Then CLP = CLP + 1
            If Cb_Kpts9.SelectedIndex > 0 And Cb_Kpts9.SelectedIndex < 8 Then If Chk_Pure_Dip_Credit_2015(Cb_Sbj9) = True Then CLP = CLP + 1

        End If
    End Sub

    Function Chk_Sbj_Math_2018(ByVal cb As DropDownList)
        Chk_Sbj_Math_2018 = False

        If cb.SelectedItem.Text = "MATEMATIK TAMBAHAN" Then Chk_Sbj_Math_2018 = True : Exit Function
        Return Chk_Sbj_Math_2018
    End Function

    Function Chk_Sbj_Credit_2018(ByVal cb As DropDownList)
        Chk_Sbj_Credit_2018 = False

        If cb.SelectedItem.Text = "MATEMATIK TAMBAHAN" Then Chk_Sbj_Credit_2018 = True : Exit Function
        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Sbj_Credit_2018 = True : Exit Function
        If cb.SelectedItem.Text = "KIMIA" Then Chk_Sbj_Credit_2018 = True : Exit Function
        If cb.SelectedItem.Text = "FIZIK" Then Chk_Sbj_Credit_2018 = True : Exit Function
        Return Chk_Sbj_Credit_2018
    End Function


    Function Chk_Dip_Credit_2015(ByVal cb As DropDownList)
        Chk_Dip_Credit_2015 = False

        If cb.SelectedItem.Text = "BIOLOGI" Then Chk_Dip_Credit_2015 = True : Exit Function
        If cb.SelectedItem.Text = "KIMIA" Then Chk_Dip_Credit_2015 = True : Exit Function
        If cb.SelectedItem.Text = "FIZIK" Then Chk_Dip_Credit_2015 = True : Exit Function

        Return Chk_Dip_Credit_2015
    End Function

    Function Chk_Pure_Dip_Credit_2015(ByVal cb As DropDownList)
        Chk_Pure_Dip_Credit_2015 = False

        If cb.SelectedItem.Text = "KIMIA" Then Chk_Pure_Dip_Credit_2015 = True : Exit Function
        If cb.SelectedItem.Text = "FIZIK" Then Chk_Pure_Dip_Credit_2015 = True : Exit Function

        Return Chk_Pure_Dip_Credit_2015
    End Function
    Function Chk_Sbj_Cert_Neo(ByVal CB As DropDownList)
        Chk_Sbj_Cert_Neo = False

        If CB.SelectedItem.Text = "MATEMATIK TAMBAHAN" Then Chk_Sbj_Cert_Neo = True : Exit Function
        If CB.SelectedItem.Text = "BIOLOGI" Then Chk_Sbj_Cert_Neo = True : Exit Function

        Return Chk_Sbj_Cert_Neo
    End Function
    Function Chk_Staff()
        Dim A, T As Integer

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'PENDAFTARAN PENUH
        'Cmd.CommandText = "SELECT nokp, apc_tahun FROM jt_penuh_apc where ret=0 and apc_tahun <= year(getdate()) and nokp='" & Tx_NoKP.Text & "' ORDER BY apc_tahun"
        Cmd.CommandText = "SELECT nokp, apc_tahun FROM jt_penuh_apc where apc_tahun <= year(getdate()) and nokp='" & Tx_NoKP.Text & "' ORDER BY apc_tahun"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            A += 1
            T = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        If A > 4 And T = CInt(Year(Now)) Then
            Chk_Staff = False
        Else
            Msg(Me, "Pengalaman bekerja tidak melepasi syarat minimum untuk pendaftaran! Sila semak maklumat kelayakan.")
            Chk_Staff = True
        End If
        Return Chk_Staff

    End Function

    Protected Sub cmd_Semak_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Semak.Click
        Dim X As String = ""
        If Cb_Kursus.SelectedValue = 4 Then Panel1.Visible = True : Exit Sub
        If Cb_Sesi_Bln.SelectedIndex < 1 Then X += "Sesi Pengambilan (Bulan), "
        If Cb_Sesi_Thn.SelectedIndex < 1 Then X += "Sesi Pengambilan (Tahun), "
        'If Panel3.Visible = True And Cb_Aliran.Enabled = True Then If Cb_Aliran.SelectedIndex = 0 Then X += "Aliran, "
        'If Panel3.Visible = True And Cb_Aliran.Enabled = True Then If Cb_Aliran.SelectedIndex = 0 Then X += "Aliran, "
        If Panel6.Visible = True Then If Cb_KptsMuet.SelectedIndex = 0 Then X += "Keputusan MUET, "
        If Cb_Luar.SelectedIndex > 0 And Cb_KptsLuar.Text = "" Then X += "Keputusan Bahasa Inggeris(Calon Setaraf/Luar Negara), "
        If Cb_SbjM.SelectedIndex > 0 And Cb_KptsM.Text = "" Then X += "Matrikulasi/Asasi, "
        If Cb_Aliran.SelectedIndex = 2 Then If Cb_KptsT6.SelectedIndex = 0 Then X += "SPM (Matematik), "
        If Cb_Aliran.SelectedIndex = 2 Then If Cb_KptsT7.SelectedIndex = 0 Then X += "SPM (Sains), "
        If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:  " & X) : Exit Sub

        If Chk_Subjek() = True Then Exit Sub

        Panel1.Visible = True : PanelSemak.Visible = True

        Cb_Muet.Visible = False : Cb_Muetx.Visible = True : Cb_Muetx.Text = Cb_Muet.Text 'muet
        Cb_Luar.Visible = False : Cb_Luarx.Visible = True : Cb_Luarx.Text = Cb_Luar.SelectedItem.Text 'bi luar negara/setaraf
        Cb_SbjM.Visible = False : Cb_SbjMx.Visible = True : Cb_SbjMx.Text = Cb_SbjM.SelectedItem.Text 'matrik/asasi

        Cb_SbjT1.Visible = False : Cb_SbjT1x.Visible = True : Cb_SbjT1x.Text = Cb_SbjT1.SelectedItem.Text
        Cb_SbjT2.Visible = False : Cb_SbjT2x.Visible = True : Cb_SbjT2x.Text = Cb_SbjT2.SelectedItem.Text
        Cb_SbjT3.Visible = False : Cb_SbjT3x.Visible = True : Cb_SbjT3x.Text = Cb_SbjT3.SelectedItem.Text
        Cb_SbjT4.Visible = False : Cb_SbjT4x.Visible = True : Cb_SbjT4x.Text = Cb_SbjT4.SelectedItem.Text
        Cb_SbjT5.Visible = False : Cb_SbjT5x.Visible = True : Cb_SbjT5x.Text = Cb_SbjT5.SelectedItem.Text 'stpm

        Cb_Sbj5.Visible = False : Cb_Sbj5x.Visible = True : Cb_Sbj5x.Text = Cb_Sbj5.SelectedItem.Text
        Cb_Sbj6.Visible = False : Cb_Sbj6x.Visible = True : Cb_Sbj6x.Text = Cb_Sbj6.SelectedItem.Text
        Cb_Sbj7.Visible = False : Cb_Sbj7x.Visible = True : Cb_Sbj7x.Text = Cb_Sbj7.SelectedItem.Text
        Cb_Sbj8.Visible = False : Cb_Sbj8x.Visible = True : Cb_Sbj8x.Text = Cb_Sbj8.SelectedItem.Text
        Cb_Sbj9.Visible = False : Cb_Sbj9x.Visible = True : Cb_Sbj9x.Text = Cb_Sbj9.SelectedItem.Text 'spm

        'Disble course option 04102019 - OSH 
        Cb_Kursus.Enabled = False

        Cb_Kelayakan.Enabled = False : Cb_Kelayakan_Taraf.Enabled = False
        Cb_Sesi_Bln.Enabled = False : Cb_Sesi_Thn.Enabled = False
        Panel2.Enabled = False : Panel3.Enabled = False : Panel4.Enabled = False
        Panel5.Enabled = False : Panel6.Enabled = False : PanelSemak.Enabled = False
    End Sub

    Protected Sub Cb_NoKP_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_NoKP.SelectedIndexChanged
        Tx_NoKP.Text = ""
        With Cb_NoKP
            'Comment Original 20122019 - OSH 
            'If .SelectedIndex = 0 Then Tx_NoKP.MaxLength = 12 : Fn_Negara(1)
            'If .SelectedIndex = 1 Then Tx_NoKP.MaxLength = 8 : Fn_Negara(1)
            'If .SelectedIndex = 2 Then Tx_NoKP.MaxLength = 15 : Fn_Negara(0)

            'Switch states to country 2012019 - OSH 
            If .SelectedIndex = 0 Then Tx_NoKP.MaxLength = 12 : Fn_Negara(1) : Fn_Negeri(1)
            If .SelectedIndex = 1 Then Tx_NoKP.MaxLength = 8 : Fn_Negara(1) : Fn_Negeri(1)
            If .SelectedIndex = 2 Then Tx_NoKP.MaxLength = 15 : Fn_Negara(0) : Fn_Negeri(0)
            If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        End With
    End Sub

    Public Sub Fn_Negara(ByVal X As Int16)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'WARGANEGARA
        If X = 1 Then
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara = 'MALAYSIA'"
        Else
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
        End If
        Rdr = Cmd.ExecuteReader()
        Cb_Warga.Items.Clear()
        Cb_Warga.Items.Add("")
        Cb_W_Negara.Items.Clear()
        Cb_W_Negara.Items.Add("")
        While Rdr.Read
            Cb_Warga.Items.Add(Rdr(0))
            Cb_Warga.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
            Cb_W_Negara.Items.Add(Rdr(0))
            Cb_W_Negara.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
    End Sub

    Protected Sub RadioButtonList1_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles RadioButtonList1.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ WHERE JENIS=" & RadioButtonList1.SelectedValue & " ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select nokp from pelatih where nokp = '" & Tx_NoKP.Text & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Pelatih Ini Telah Ada!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()

        Try
            Cmd.CommandText = "select * from jt_penuh where nokp = '" & Tx_NoKP.Text & "'"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then
                If Not IsDBNull(Rdr("nama")) Then Tx_Nama.Text = Rdr("nama")
                If Not IsDBNull(Rdr("warganegara")) Then If Rdr("warganegara") > 0 Then Cb_Warga.SelectedValue = Rdr("warganegara")
                If Not IsDBNull(Rdr("jantina")) Then If Rdr("jantina") > 0 Then Cb_Jantina.SelectedValue = Rdr("jantina")
                If Not IsDBNull(Rdr("bangsa")) Then If Rdr("bangsa") > 0 Then Cb_Bangsa.SelectedValue = Rdr("bangsa")
                If Not IsDBNull(Rdr("agama")) Then If Rdr("agama") > 0 Then Cb_Agama.SelectedValue = Rdr("agama")
                If Not IsDBNull(Rdr("t_kahwin")) Then If Rdr("t_kahwin") > 0 Then Cb_Kahwin.SelectedValue = Rdr("t_kahwin")
                If Not IsDBNull(Rdr("tp_alamat")) Then Tx_TP_Alamat.Text = Rdr("tp_alamat")
                If Not IsDBNull(Rdr("tp_poskod")) Then Tx_TP_Poskod.Text = Rdr("tp_poskod")
                If Not IsDBNull(Rdr("tp_bandar")) Then Tx_TP_Bandar.Text = Rdr("tp_bandar")
                If Not IsDBNull(Rdr("tp_negeri")) Then If Rdr("tp_negeri") > 0 Then Cb_TP_Negeri.SelectedValue = Rdr("tp_negeri")
                If Not IsDBNull(Rdr("tel_hp")) Then Tx_Tel.Text = Rdr("tel_hp")
                If Not IsDBNull(Rdr("emel")) Then Tx_Emel.Text = Rdr("emel")
            Else
                Msg(Me, "Rekod Tidak Wujud")
            End If
            Rdr.Close()
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
        Cn.Close()
    End Sub

    Protected Sub Cb_Aliran_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Aliran.SelectedIndexChanged
        Cb_KptsT6.SelectedIndex = 0 : Cb_KptsT7.SelectedIndex = 0

        If Cb_Aliran.SelectedIndex = 2 Then
            Textbox13.Visible = True : Textbox22.Visible = True : TextBox23.Visible = True
            Cb_SbjT6.Visible = True : Cb_KptsT6.Visible = True
            Cb_SbjT7.Visible = True : Cb_KptsT7.Visible = True
        Else
            Textbox13.Visible = False : Textbox22.Visible = False : TextBox23.Visible = False
            Cb_SbjT6.Visible = False : Cb_KptsT6.Visible = False
            Cb_SbjT7.Visible = False : Cb_KptsT7.Visible = False
        End If
    End Sub

    Protected Sub Cb_Luar_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Luar.SelectedIndexChanged
        Cb_KptsLuar.Text = ""
        If Cb_Luar.SelectedIndex = 0 Then Cb_Kpts2.Enabled = True Else Cb_Kpts2.Enabled = False
    End Sub

    Protected Sub Matrik_2015()
        Panel2.Visible = True : Isi_Subjek(2)
        Panel4.Visible = True : Panel6.Visible = True
        Bt4.Enabled = True
    End Sub

    Protected Sub Degree_SPM_2015()
        ' Add Degree STPM - Enable SPM panel 16102019 - OSH    
        If Cb_Kursus.SelectedIndex = 5 Then
            Panel2.Visible = True : Isi_Subjek_2(2)
            Bt4.Enabled = True
        End If
    End Sub

    'Add states switch to country for internations student 20122019 - OSH  
    Public Sub Fn_Negeri(ByVal X As Int16)
        Dim Cn5 As New OleDbConnection : Dim Cmd5 As New OleDbCommand : Dim Rdr5 As OleDbDataReader
        Cn5.ConnectionString = ServerId : Cn5.Open() : Cmd5.Connection = Cn5

        'WARGANEGARA
        If X = 1 Then
            Lb_States.Text = "NEGERI" : Lb_States.Enabled = False
            Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
            Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("")
            Cmd5.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM pn_negeri order by dc_negeri"
        Else
            Lb_States.Text = "NEGARA" : Lb_States.Enabled = False
            Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
            Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("") : Cb_W_Negeri.Enabled = False
            Cmd5.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara where dc_negara <> 'MALAYSIA' ORDER BY dc_NEGARA"
        End If
        Rdr5 = Cmd5.ExecuteReader()
        Cb_TP_Negeri.Items.Clear() : Cb_TP_Negeri.Items.Add("")
        Cb_W_Negeri.Items.Clear() : Cb_W_Negeri.Items.Add("") : Cb_W_Negeri.Enabled = False
        While Rdr5.Read
            Cb_TP_Negeri.Items.Add(Rdr5(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr5(1)
            Cb_W_Negeri.Items.Add(Rdr5(0))
            Cb_W_Negeri.Items.Item(Cb_W_Negeri.Items.Count - 1).Value = Rdr5(1)
        End While
        Rdr5.Close()
        Cn5.Close()
    End Sub
End Class