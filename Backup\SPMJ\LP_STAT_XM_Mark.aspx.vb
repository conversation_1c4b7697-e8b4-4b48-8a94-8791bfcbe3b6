﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_XM_Mark
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Laporan", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        'KATEGORI
        Cb_Kategori.Items.Clear()
        Cb_Kategori.Items.Add("(SEMUA SEKTOR)")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = ""
        Cb_Kategori.Items.Add("KERAJAAN")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "1"
        Cb_Kategori.Items.Add("IPTA")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "3"
        Cb_Kategori.Items.Add("SWASTA")
        Cb_Kategori.Items.Item(Cb_Kategori.Items.Count - 1).Value = "2"

        'KOLEJ
        Cmd.CommandText = "select dc_kolej, id_kolej from pn_kolej where (jenis < 3) order by dc_kolej"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("(SEMUA KOLEJ)")
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()

        'KURSUS
        Cb_Kursus.Items.Clear()
        Cb_Kursus.Items.Add("(SEMUA JENIS PENDAFTARAN)")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = ""
        Cb_Kursus.Items.Add("JURURAWAT BERDAFTAR")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "1"
        Cb_Kursus.Items.Add("JURURAWAT MASYARAKAT")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "2"
        Cb_Kursus.Items.Add("PENOLONG JURURAWAT")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "3"
        Cb_Kursus.Items.Add("KEBIDANAN I")
        Cb_Kursus.Items.Item(Cb_Kursus.Items.Count - 1).Value = "4"

        'KERTAS
        With Cb_Kertas
            .Items.Clear()
            .Items.Add("OBJEKTIF I")
            .Items.Item(.Items.Count - 1).Value = "1"
            .Items.Add("OBJEKTIF II")
            .Items.Item(.Items.Count - 1).Value = "2"
        End With
    End Sub

    Protected Sub Cb_Kursus_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kursus.SelectedIndexChanged
        If Cb_Kursus.SelectedIndex < 4 Then Cb_Kertas.Enabled = False Else Cb_Kertas.Enabled = True
    End Sub

    Protected Sub Cb_Kategori_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kategori.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Dim SQL As String

        'update by azidi 13/08/2012
        'If Cb_Kategori.SelectedIndex < 1 Then SQL = "< 3" Else SQL = "=" & Cb_Kategori.SelectedValue
        If Cb_Kategori.SelectedIndex < 1 Then
            SQL = "jenis< 3"
        ElseIf Cb_Kategori.SelectedIndex = 1 Then
            SQL += " ( id_KOLEJ>=0 and  id_KOLEJ<=100) " ' & Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 2 Then
            SQL += " ( id_KOLEJ>=101 and  id_KOLEJ<=199) " '& Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 3 Then
            SQL += " ( id_KOLEJ>=200  and  JENIS<3) " ' Cb_Kategori.SelectedValue
        End If

        'KOLEJ
        Cmd.CommandText = "select dc_kolej, id_kolej from pn_kolej where " & SQL & " order by dc_kolej" 'jenis
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("(SEMUA)")
        Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = ""
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Dim SQL, Id_XM, Tahun, Siri, Subjek, X As String
        Tahun = "0" : Siri = "0" : Subjek = "0" : Id_XM = "0" : X = ""

        If Cb_Kolej.SelectedIndex > 0 Then X += " where p.id_kolej=" & Cb_Kolej.SelectedValue
        If Cb_Kursus.SelectedIndex > 0 Then
            If X.Length = 0 Then X += " where " Else X += " and "
            X += " j_xm=" & Cb_Kursus.SelectedIndex
        End If
        If Tx_Tahun.Text <> "" And Tx_Siri.Text <> "" Then
            If X.Length = 0 Then
                X += " where tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text
            Else
                X += " and tahun=" & Tx_Tahun.Text & " and siri=" & Tx_Siri.Text
            End If
        End If

        If X = "" Then
        Else
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            SQL = "select top 1 px.id_xm, tahun, siri, j_xm from xm_calon xc inner join pelatih p on p.nokp=xc.nokp inner join pn_xm px on xc.id_xm=px.id_xm " & _
                  "left join pn_kolej pk on pk.id_kolej=xc.id_pusat " & X & " order by tahun desc, siri desc, j_xm desc"
            Cmd.CommandText = SQL : Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then Id_XM = Rdr(0) : Tahun = Rdr(1) : Siri = Rdr(2) : Subjek = Rdr(3)
            Rdr.Close() : Cn.Close()
        End If

        If CInt(Subjek) > 2 Then
            Subjek = CInt(Subjek) + 1
            Subjek = Subjek & Cb_Kertas.SelectedValue
        Else
            Subjek = Subjek & Cb_Kertas.SelectedValue
        End If

        SQL = ""
        'SQL = "where xc.id_xm=" & Id_XM & " and tahun=" & Tahun & " and siri=" & Siri & " and subjek=" & Kursus
        'If Cb_Kategori.SelectedIndex < 1 Then SQL += "" Else SQL += " and pn.jenis=" & Cb_Kategori.SelectedValue
        'update by azidi 13/08/2012
        'If Cb_Kategori.SelectedIndex < 1 Then SQL += "" Else SQL_pilih += " and pn.jenis=" & Cb_Kategori.SelectedIndex 'Kategori
        If Cb_Kategori.SelectedIndex < 1 Then
            SQL += ""
        ElseIf Cb_Kategori.SelectedIndex = 1 Then
            SQL += " and ( pn.id_KOLEJ>=0 and  pn.id_KOLEJ<=100) " ' & Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 2 Then
            SQL += "  and ( pn.id_KOLEJ>=101 and  pn.id_KOLEJ<=199) " '& Cb_Kategori.SelectedValue
        ElseIf Cb_Kategori.SelectedIndex = 3 Then
            SQL += "  and ( pn.id_KOLEJ>=200  and  pn.JENIS<3) " ' Cb_Kategori.SelectedValue
        End If
        If Cb_Kolej.SelectedIndex < 1 Then SQL += "" Else SQL += " and p.id_kolej=" & Cb_Kolej.SelectedValue

        Jana(SQL, Id_XM, Tahun, Siri, Subjek)
    End Sub

    Public Sub Jana(ByVal X As String, ByVal id_xm As String, ByVal tahun As String, ByVal siri As String, ByVal subjek As String)
        Dim Tajuk, Tajuk2, Tajuk3, Master As String, i, A, B, C, D, G, T, Bonus, Jum As Integer
        Dim Jawatan, Jawatan2, Jawatan3 As Integer
        If Cb_Kursus.SelectedValue = 1 Then Jawatan = 1 : Jawatan2 = 5 : Jawatan3 = 8
        If Cb_Kursus.SelectedValue = 2 Then Jawatan = 2 : Jawatan2 = 2 : Jawatan3 = 2
        If Cb_Kursus.SelectedValue = 3 Then Jawatan = 3 : Jawatan2 = 3 : Jawatan3 = 3
        If Cb_Kursus.SelectedValue = 4 Then Jawatan = 4 : Jawatan2 = 4 : Jawatan3 = 4


        Tajuk = "STATISTIK ANALISA MARKAH BAGI " & Cb_Kategori.SelectedItem.Text & ", " & Cb_Kursus.SelectedItem.Text
        Tajuk2 = Cb_Kolej.SelectedItem.Text
        Tajuk3 = "KERTAS " & Cb_Kertas.SelectedItem.Text & ", TAHUN " & tahun & " SIRI " & siri

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='16' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='16' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='16' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk3 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>NO</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>ANGKA GILIRAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>NO KAD PENGENALAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>KURSUS</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>SESI BULAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>SESI TAHUN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH SOALAN</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>BETUL</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>SALAH</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>GANDA</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>TIDAK MENJAWAB</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>BONUS</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>PERATUS(%)</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>GRED</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>STATUS</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        'Comment Ori 15092015-OSH
        'Cmd.CommandText = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'AG', p.nokp, case j_kursus when 1 then 'JB' when 2 then 'JM' " & _
        '                  "when 3 then 'PJ' when 4 then 'KB1' when 5 then 'JB(PRE)' when 8 then 'JB(KPLS)' else '' end as kursus ,sesi_bulan, sesi_tahun, " & _
        '                  "(select top 1 cast(jawapan as nvarchar(150)) from xm_markah xmm where xmm.ag=xc.ag and xmm.tahun=" & tahun & " and xmm.siri=" & siri & " and xmm.subjek=" & subjek & " " & _
        '                  "order by tahun, siri, subjek) as jawapan, (select top 1 cast(master as nvarchar(150)) from xm_markah xmm where xmm.ag=xc.ag and xmm.tahun=" & tahun & " and xmm.siri=" & siri & " and xmm.subjek=" & subjek & " " & _
        '                  "order by tahun, siri, subjek) as master, markah_jum, cast(markah_jum as varchar(8))+' %' as '%', " & _
        '                  "case when xc.markah_jum is null then '' when xc.markah_jum between 80 and 100 then 'A' " & _
        '                  "when xc.markah_jum between 75 and 79.99 then 'A-' when xc.markah_jum between 70 and 74.99 then 'B+' " & _
        '                  "when xc.markah_jum between 65 and 69.99 then 'B' when xc.markah_jum between 60 and 64.99 then 'B-' " & _
        '                  "when xc.markah_jum between 55 and 59.99 then 'C+' when xc.markah_jum between 50 and 54.99 then 'C' " & _
        '                  "when xc.markah_jum between 47 and 49.99 then 'C-' when xc.markah_jum between 44 and 46.99 then 'D+' " & _
        '                  "else 'D' end as 'GRED', case keputusan when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' else '' end as 'keputusan' " & _
        '                  "from pelatih p left outer join pn_kolej pn on p.id_kolej = pn.id_kolej inner join xm_calon xc on p.nokp = xc.nokp " & _
        '                  "where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & "  or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & " " & X & " order by cast(xc.ag as integer), p.id_kolej "

        'Fixing multiple records display per canidate 15092015 - OSH
        Cmd.CommandText = "select stuff('0000',5-len(xc.ag),len(xc.ag),xc.ag) as 'AG', p.nokp, case j_kursus when 1 then 'JB' when 2 then 'JM' " & _
                         "when 3 then 'PJ' when 4 then 'KB1' when 5 then 'JB(PRE)' when 8 then 'JB(KPLS)' else '' end as kursus ,sesi_bulan, sesi_tahun, " & _
                         "(select top 1 cast(jawapan as nvarchar(150)) from xm_markah xmm where xmm.ag=xc.ag and xmm.tahun=" & tahun & " and xmm.siri=" & siri & " and xmm.subjek=" & subjek & " " & _
                         "order by tahun, siri, subjek) as jawapan, (select top 1 cast(master as nvarchar(150)) from xm_markah xmm where xmm.ag=xc.ag and xmm.tahun=" & tahun & " and xmm.siri=" & siri & " and xmm.subjek=" & subjek & " " & _
                         "order by tahun, siri, subjek) as master, markah_jum, cast(markah_jum as varchar(8))+' %' as '%', " & _
                         "case when xc.markah_jum is null then '' when xc.markah_jum between 80 and 100 then 'A' " & _
                         "when xc.markah_jum between 75 and 79.99 then 'A-' when xc.markah_jum between 70 and 74.99 then 'B+' " & _
                         "when xc.markah_jum between 65 and 69.99 then 'B' when xc.markah_jum between 60 and 64.99 then 'B-' " & _
                         "when xc.markah_jum between 55 and 59.99 then 'C+' when xc.markah_jum between 50 and 54.99 then 'C' " & _
                         "when xc.markah_jum between 47 and 49.99 then 'C-' when xc.markah_jum between 44 and 46.99 then 'D+' " & _
                         "else 'D' end as 'GRED', case keputusan when 'L' then 'LULUS' when 'G' then 'GAGAL' when 'T' then 'TIDAK HADIR' else '' end as 'keputusan' " & _
                         "from pelatih p left outer join pn_kolej pn on p.id_kolej = pn.id_kolej inner join xm_calon xc on p.nokp = xc.nokp " & _
                         "where (p.j_kursus = " & Jawatan & " or p.j_kursus = " & Jawatan2 & "  or p.j_kursus = " & Jawatan3 & " ) and xc.id_xm=" & id_xm & "and p.status is null " & X & " order by cast(xc.ag as integer), p.id_kolej "

        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "pelatih")
        Rdr.Close()

        Header = "" : i = 0
        For Each dr As DataRow In Ds.Tables(0).Rows
            i += 1
            Header += "<tr>"
            Header += "    <td>" & i & "</td> "
            Header += "    <td>=""" + dr.Item(0) + """</td> "
            Header += "    <td>=""" + dr.Item(1) + """</td> "
            Header += "    <td>" & dr.Item(2) & "</td> "
            Header += "    <td>" & dr.Item(3) & "</td> "
            Header += "    <td>" & dr.Item(4) & "</td> "

            If Not IsDBNull(dr.Item(6)) Then Master = dr.Item(6) Else Master = ""

            Jum = 0 : A = 0 : B = 0 : C = 0 : D = 0 : G = 0 : T = 0 : Bonus = 0
            If Not IsDBNull(dr.Item(5)) Then
                For j As Integer = 1 To Master.Length
                    Jum = Master.Length
                    If Mid(Master, j, 1) = "*" Then Bonus += 1
                    If Mid(dr.Item(5), j, 1) = Mid(Master, j, 1) Then A += 1
                    If Mid(dr.Item(5), j, 1) = "*" Then G += 1
                    If Mid(dr.Item(5), j, 1) <> "A" And Mid(dr.Item(5), j, 1) <> "B" And Mid(dr.Item(5), j, 1) <> "C" And Mid(dr.Item(5), j, 1) <> "D" And Mid(dr.Item(5), j, 1) <> "*" Then T += 1
                    B = Master.Length - A - G - T
                Next
            End If
            If dr.Item(10).ToString = "TIDAK HADIR" Then
                Header += "    <td></td> "
                Header += "    <td></td> "
                Header += "    <td></td> "
                Header += "    <td></td> "
                Header += "    <td></td> "
                Header += "    <td></td> "
                Header += "    <td></td> "
                Header += "    <td></td> "
                Header += "    <td></td> "
                Header += "    <td>" & dr.Item(10) & "</td> "
            Else
                Header += "    <td>" & Jum & "</td> "
                Header += "    <td>" & A & "</td> "
                Header += "    <td>" & B - Bonus & "</td> "
                Header += "    <td>" & G & "</td> "
                Header += "    <td>" & T & "</td> "
                Header += "    <td>" & Bonus & "</td> "
                Header += "    <td>" & dr.Item(7) & "</td> "
                Header += "    <td>" & dr.Item(8) & "</td> "
                Header += "    <td>" & dr.Item(9) & "</td> "
                Header += "    <td>" & dr.Item(10) & "</td> "
            End If
            'Header += "    <td>" & Jum & "</td> "
            'Header += "    <td>" & A & "</td> "
            'Header += "    <td>" & B - Bonus & "</td> "
            'Header += "    <td>" & G & "</td> "
            'Header += "    <td>" & T & "</td> "
            'Header += "    <td>" & Bonus & "</td> "
            'Header += "    <td>" & dr.Item(7) & "</td> "
            'Header += "    <td>" & dr.Item(8) & "</td> "
            'Header += "    <td>" & dr.Item(9) & "</td> "
            'Header += "    <td>" & dr.Item(10) & "</td> "
            Header += "</tr>"
        Next

        Response.Write(Header)
        Response.Write("</table>")
        Response.End()
        Response.Flush()
    End Sub
End Class