﻿Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm24
    Inherits System.Web.UI.Page

    Public Sub Cari()
        Dim SQL As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        'SQL = "select NAMA, NOKP as 'NO. KP/PASPORT', '' as 'NO. PENDAFTARAN', case log_status when 1 then 'LM' when 2 then 'L' when 3 then 'TL' else '' end as 'STATUS', log_catatan as 'CATATAN', case isnull(keputusan_tkh,0) when 0 then '' else 'LULUS' end as 'KEPUTUSAN', j_daftar from tmp_tpc order by nama"  asal
        SQL = "select NAMA, NOKP as 'NO. KP/PASPORT', '' as 'NO. PENDAFTARAN', case log_status when 1 then 'LM' when 2 then 'L' when 3 then 'TL' else '' end as 'STATUS', log_catatan as 'CATATAN', case isnull(keputusan_tkh_tamat,0) when 0 then '' else 'LULUS' end as 'KEPUTUSAN', j_daftar from tmp_tpc order by nama"
        Dim List_Adp As New SqlDataAdapter(SQL, Cn)
        List_Adp.Fill(List_Data, "tmp_tpc")
        Gd.DataSource = List_Data.Tables("tmp_tpc")
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        Cari()
        'If Gd.Rows.Count > 0 Then cmd_Pilih.Visible = True Else cmd_Pilih.Visible = False
    End Sub


    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        'e.Row.Cells(4).Visible = False
        e.Row.Cells(8).Visible = False
        e.Row.Cells(0).Width = Unit.Pixel(30)
        e.Row.Cells(6).Width = Unit.Pixel(150)
        e.Row.Cells(0).HorizontalAlign = HorizontalAlign.Center
        If e.Row.RowIndex = -1 Then Exit Sub
        e.Row.Cells(1).Text = e.Row.RowIndex + 1 & "."
    End Sub

    'Dim i As Int16, chk As CheckBox, SQL As String
    'Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
    'Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

    'For i = 0 To Gd.Rows.Count - 1
    '    chk = Gd.Rows.Item(i).FindControl("chk_pilih")
    '    If chk.Checked And chk.Enabled Then
    '        SQL = "update tmp_tpc set nopd = (select tpc+1 from pn_nopd), tkh_daftar = getdate() where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "'; "
    '        SQL += "update pn_nopd set tpc = tpc+1; "
    '        SQL += "insert jt_tpc (J_Daftar,NoKP,Nama,NoPd,Tkh_Daftar,Warganegara,Tkh_Lahir,Tpt_Lahir,Jantina,Umur,TP_Alamat,TP_Poskod,TP_Bandar,TP_Negeri,SM_Alamat,SM_Poskod,SM_Bandar,SM_Negeri,Emel,T_Kahwin,Id_Kolej,TKh_Latihan_Tamat,LP_Nama,LP_NoPd,LP_TkhSah,ss1,ss2,ss3,ss4,ss5,ss6,ss7,ss8,ss9,ss10,ss11,ss12,ss13,ss14,Log_Status,Log_Catatan,Log_Id,Log_Tkh) select * from tmp_tpc where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and nopd is not null; "
    '        SQL += "insert jt_tpc_kelayakan select * from tmp_tpc_kelayakan where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "'; "
    '        SQL += "insert jt_tpc_pengalaman select * from tmp_tpc_pengalaman where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "'; "
    '        SQL += "insert jt_tpc_majikan (nokp,tpt_amalan,tempoh_a,tempoh_b,tkh_tamat,status) select *, 1 from tmp_tpc_majikan where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "'; "
    '        SQL += "delete from tmp_tpc where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "' and nopd is not null; "
    '        SQL += "delete from tmp_tpc_kelayakan where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "'; "

    '        Try
    '            Cmd.CommandText = SQL
    '            Cmd.ExecuteNonQuery()
    '        Catch ex As Exception
    '            Cn.Close()
    '            Msg(Me, ex.Message)
    '            Exit Sub
    '        End Try

    '        Cmd.CommandText = "select 'TPC-' + cast(nopd as varchar(6)) from jt_tpc where nokp = '" & Gd.Rows.Item(i).Cells(3).Text & "'"
    '        Rdr = Cmd.ExecuteReader()
    '        If Rdr.Read Then Gd.Rows.Item(i).Cells(4).Text = Rdr(0)
    '        Rdr.Close()
    '        chk.Enabled = False
    '        'cmd_Pilih.Visible = False
    '    Else
    '        Gd.Rows.Item(i).Cells(4).Text = ""
    '    End If
    'Next
    'Cn.Close()
    'Msg(Me, "Rekod Telah Dikemaskini..")


    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        If e.Row.Cells(5).Text = "L" And e.Row.Cells(7).Text = "LULUS" Then  Else e.Row.Cells(0).Text = ""
    End Sub

    Protected Sub Gd_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Gd.SelectedIndexChanged
        Session("NAMA") = Gd.SelectedRow.Cells(2).Text
        Session("NOKP") = Gd.SelectedRow.Cells(3).Text
        Session("NOPD") = "(BELUM BERDAFTAR)"
        Session("DAFTAR") = Gd.SelectedRow.Cells(8).Text
        Response.Redirect("P4_TPC_Proses.aspx")
    End Sub
End Class