﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class Lpr_P4_APC
    Inherits System.Web.UI.Page


    Public Sub Cari(ByVal X As String)
        Dim Tb As String
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Tb = "jt_penuh"

        Dim List_Adp As New SqlDataAdapter(X, Cn)
        List_Adp.Fill(List_Data, Tb)
        Gd.DataSource = List_Data.Tables(Tb)
        DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        'Add verify login 06122019 -OSH 
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        Tx_Tkh.Text = Format(Now, "01/01/yyyy")
        Tx_Tkh2.Text = Format(Now, "31/12/yyyy")
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim SQL As String
        SQL = "select 1,'JT' as 'JENIS APC', count(jpa.nokp) 'JUMLAH' from jt_penuh jp inner join jt_penuh_apc jpa on jp.nokp = jpa.nokp and jpa.j_daftar = 1 and apc_tkh between " & Chk_Tkh(Tx_Tkh.Text) & " and " & Chk_Tkh(Tx_Tkh2.Text) & " union "
        SQL += "select 2,'JM' as 'JENIS APC', count(jpa.nokp) 'JUMLAH' from jt_penuh jp inner join jt_penuh_apc jpa on jp.nokp = jpa.nokp and jpa.j_daftar = 2 and apc_tkh between " & Chk_Tkh(Tx_Tkh.Text) & " and " & Chk_Tkh(Tx_Tkh2.Text) & " union "
        SQL += "select 3, 'PJ' as 'JENIS APC', count(jpa.nokp) 'JUMLAH' from jt_penuh jp inner join jt_penuh_apc jpa on jp.nokp = jpa.nokp and jpa.j_daftar = 3 and apc_tkh between " & Chk_Tkh(Tx_Tkh.Text) & " and " & Chk_Tkh(Tx_Tkh2.Text)
        Cari(SQL)
    End Sub
End Class