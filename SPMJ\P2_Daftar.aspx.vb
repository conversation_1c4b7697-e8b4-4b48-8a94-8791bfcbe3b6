﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient

Partial Public Class WebForm14
    Inherits System.Web.UI.Page


    Function SSemak(ByVal X As Int16)
        If chk_Semak.Items(X).Selected = True Then X = 1 Else X = 0
        Return X
    End Function

    Public Sub Isi_Pinda()
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Dim dt1, dt2, dt3, dt4 As DateTime

        Cmd.CommandText = "select * from tmp_penuh where nokp = '" & Session("NOKP") & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            If Not IsDBNull(Rdr("j_daftar")) Then Cb_Jenis.Items.FindByValue(Rdr("j_daftar")).Selected = True
            If Not IsDBNull(Rdr("nama")) Then Tx_Nama.Text = Rdr("nama")
            If Not IsDBNull(Rdr("nokp")) Then Tx_NoKP.Text = Rdr("nokp")
            If Not IsDBNull(Rdr("tpt_lahir")) Then Tx_Tpt_Lahir.Text = Rdr("tpt_lahir")
            'Comment Original 30102018 - OSH
            'If Not IsDBNull(Rdr("tkh_lahir")) Then Tx_Tkh_Lahir.Text = Rdr("tkh_lahir")

            'Fix Date format issue 30102018 - OSH 
            If Not IsDBNull(Rdr("tkh_lahir")) Then dt1 = Rdr("tkh_lahir") : Tx_Tkh_Lahir.Text = dt1.ToString("dd'/'MM'/'yyyy")
            If Not IsDBNull(Rdr("warganegara")) Then Cb_Warga.Items.FindByValue(Rdr("warganegara")).Selected = True
            If Not IsDBNull(Rdr("jantina")) Then If Rdr("jantina") > 0 Then Cb_Jantina.Items.FindByValue(Rdr("jantina")).Selected = True
            If Not IsDBNull(Rdr("bangsa")) Then If Rdr("bangsa") > 0 Then Cb_Bangsa.Items.FindByValue(Rdr("bangsa")).Selected = True
            If Not IsDBNull(Rdr("etnik")) Then If Rdr("etnik") > 0 Then Cb_Etnik.Items.FindByValue(Rdr("etnik")).Selected = True : Cb_Etnik.Enabled = True
            If Not IsDBNull(Rdr("umur")) Then Tx_Umur.Text = Rdr("umur")
            If Not IsDBNull(Rdr("agama")) Then If Rdr("agama") > 0 Then Cb_Agama.Items.FindByValue(Rdr("agama")).Selected = True
            If Not IsDBNull(Rdr("t_kahwin")) Then If Rdr("t_kahwin") > 0 Then Cb_Kahwin.Items.FindByValue(Rdr("t_kahwin")).Selected = True
            If Not IsDBNull(Rdr("tp_alamat")) Then Tx_TP_Alamat.Text = Rdr("tp_alamat")
            If Not IsDBNull(Rdr("tp_poskod")) Then Tx_TP_Poskod.Text = Rdr("tp_poskod")
            If Not IsDBNull(Rdr("tp_bandar")) Then Tx_TP_Bandar.Text = Rdr("tp_bandar")
            If Cb_Warga.SelectedItem.Text = "MALAYSIA" Then Fn_Negara(1) Else Fn_Negara(0)
            If Not IsDBNull(Rdr("tp_negeri")) Then If Rdr("tp_negeri") > 0 Then Cb_TP_Negeri.Items.FindByValue(Rdr("tp_negeri")).Selected = True
            If Not IsDBNull(Rdr("sm_alamat")) Then Tx_SM_Alamat.Text = Rdr("sm_alamat")
            If Not IsDBNull(Rdr("sm_poskod")) Then Tx_SM_Poskod.Text = Rdr("sm_poskod")
            If Not IsDBNull(Rdr("sm_bandar")) Then Tx_SM_Bandar.Text = Rdr("sm_bandar")
            If Not IsDBNull(Rdr("sm_negeri")) Then If Rdr("sm_negeri") > 0 Then Cb_SM_Negeri.Items.FindByValue(Rdr("sm_negeri")).Selected = True
            If Not IsDBNull(Rdr("tel_r")) Then Tx_Tel_R.Text = Rdr("tel_r")
            If Not IsDBNull(Rdr("tel_hp")) Then Tx_Tel_HP.Text = Rdr("tel_hp")
            If Not IsDBNull(Rdr("emel")) Then Tx_Emel.Text = Rdr("emel")
            If Not IsDBNull(Rdr("id_kolej")) Then If Rdr("id_kolej") > 0 Then Cb_Kolej.Items.FindByValue(Rdr("id_kolej")).Selected = True
            If Not IsDBNull(Rdr("tajaan")) Then If Rdr("tajaan") > 0 Then Cb_Tajaan.Items.FindByValue(Rdr("tajaan")).Selected = True
            If Not IsDBNull(Rdr("sesi_bulan")) Then If Rdr("sesi_bulan") > 0 Then Cb_Sesi_Bulan.Items.FindByValue(Rdr("sesi_bulan")).Selected = True
            If Not IsDBNull(Rdr("sesi_tahun")) Then If Rdr("sesi_tahun") > 0 Then Cb_Sesi_Tahun.Items.FindByValue(Rdr("sesi_tahun")).Selected = True
            'Comment Original 30102018 - OSH
            'If Not IsDBNull(Rdr("tkh_latihan_mula")) Then Tx_M_Latihan.Text = Rdr("tkh_latihan_mula")
            'If Not IsDBNull(Rdr("tkh_latihan_tamat")) Then Tx_T_Latihan.Text = Rdr("tkh_latihan_tamat")
            'If Not IsDBNull(Rdr("tkh_periksa_akhir")) Then Tx_Periksa.Text = Rdr("tkh_periksa_akhir")

            'Fix Date format issue 30102018 - OSH 
            If Not IsDBNull(Rdr("tkh_latihan_mula")) Then dt2 = Rdr("tkh_latihan_mula") : Tx_Tkh_Lahir.Text = dt2.ToString("dd'/'MM'/'yyyy")
            If Not IsDBNull(Rdr("tkh_latihan_tamat")) Then dt3 = Rdr("tkh_latihan_tamat") : Tx_Tkh_Lahir.Text = dt3.ToString("dd'/'MM'/'yyyy")
            If Not IsDBNull(Rdr("tkh_periksa_akhir")) Then dt4 = Rdr("tkh_periksa_akhir") : Tx_Tkh_Lahir.Text = dt4.ToString("dd'/'MM'/'yyyy")

            If Not IsDBNull(Rdr("ss1")) Then If Rdr("ss1") = 1 Then chk_Semak.Items(0).Selected = True
            If Not IsDBNull(Rdr("ss2")) Then If Rdr("ss2") = 1 Then chk_Semak.Items(1).Selected = True
            If Not IsDBNull(Rdr("ss3")) Then If Rdr("ss3") = 1 Then chk_Semak.Items(2).Selected = True
            If Not IsDBNull(Rdr("ss4")) Then If Rdr("ss4") = 1 Then chk_Semak.Items(3).Selected = True
            If Not IsDBNull(Rdr("ss5")) Then If Rdr("ss5") = 1 Then chk_Semak.Items(4).Selected = True
            If Not IsDBNull(Rdr("ss6")) Then If Rdr("ss6") = 1 Then chk_Semak.Items(5).Selected = True

            If Not IsDBNull(Rdr("log_status")) And Rdr("log_status") > 0 Then Cb_Status.Items.FindByValue(Rdr("log_status")).Selected = True
            If Not IsDBNull(Rdr("log_catatan")) Then Tx_Catatan.Text = Rdr("log_catatan")
        End If
        Rdr.Close()

        Dim x As DropDownList, y As TextBox, i As Int16
        Cmd.CommandText = "select * from tmp_penuh_kelayakan where nokp = '" & Session("NOKP") & "' and jenis = 1"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            'On Error Resume Next
            x = Gd.Rows.Item(i).FindControl("cb_kelayakan")
            y = Gd.Rows.Item(i).FindControl("Tx_Tkh_Layak")
            If Not IsDBNull(Rdr("kelayakan")) Then x.SelectedItem.Text = Rdr("kelayakan")
            If Not IsDBNull(Rdr("tkh_kelayakan")) Then y.Text = Rdr("tkh_kelayakan")
            Gd.Rows.Item(i).Visible = True
            i = i + 1
        End While
        Rdr.Close()

        i = 0
        Cmd.CommandText = "select * from tmp_penuh_kelayakan where nokp = '" & Session("NOKP") & "' and jenis = 2"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            'On Error Resume Next
            x = GdA.Rows.Item(i).FindControl("cb_akademik")
            y = GdA.Rows.Item(i).FindControl("Tx_Tkh_Layak")
            If Not IsDBNull(Rdr("kelayakan")) Then x.SelectedItem.Text = Rdr("kelayakan")
            If Not IsDBNull(Rdr("tkh_kelayakan")) Then y.Text = Rdr("tkh_kelayakan")
            GdA.Rows.Item(i).Visible = True
            i = i + 1
        End While
        Rdr.Close()
        Cn.Close()
        cmd_Padam.Visible = True
        If Len(Tx_NoKP.Text) = 12 And IsNumeric(Tx_NoKP.Text) Then
            Cb_NoKP.SelectedIndex = 0
            lb_NoKP.Text = "NO. KAD PENGENALAN"
            tx_Negeri.Text = "NEGERI"
        ElseIf Left(Tx_NoKP.Text, 1) = "T" Then
            Cb_NoKP.SelectedIndex = 1
            lb_NoKP.Text = "NO. TENTERA"
            tx_Negeri.Text = "NEGERI"
        Else
            Cb_NoKP.SelectedIndex = 2
            lb_NoKP.Text = "NO. PASPORT"
            tx_Negeri.Text = "NEGARA"
        End If
    End Sub

    Public Sub Simpan_Pinda()
        Dim SQL As String
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        'Fix DateTime Format Issues 30102018 - OSH

        'Tarikh Lahir
        Dim A1 As DateTime
        Dim B1 As String = ""
        B1 = Tx_Tkh_Lahir.Text.Trim
        If Tx_Tkh_Lahir.Text.Trim <> String.Empty Then
            A1 = DateTime.ParseExact(B1, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            B1 = A1.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            B1 = "'" & B1 & "'"
        Else
            B1 = "NULL"
        End If


        'Tarikh Mula Latihan
        Dim A2 As DateTime
        Dim B2 As String = ""
        B2 = Tx_M_Latihan.Text.Trim
        If Tx_M_Latihan.Text.Trim <> String.Empty Then
            A2 = DateTime.ParseExact(B2, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            B2 = A2.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            B2 = "'" & B2 & "'"
        Else
            B2 = "NULL"
        End If

        'Tarikh Tamat Latihan
        Dim A3 As DateTime
        Dim B3 As String = ""
        B3 = Tx_T_Latihan.Text.Trim
        If Tx_T_Latihan.Text.Trim <> String.Empty Then
            A3 = DateTime.ParseExact(B3, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            B3 = A3.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            B3 = "'" & B3 & "'"
        Else
            B3 = "NULL"
        End If

        'Tarikh Peperiksaan
        Dim A4 As DateTime
        Dim B4 As String = ""
        B4 = Tx_Periksa.Text.Trim
        If Tx_Periksa.Text.Trim <> String.Empty Then
            A4 = DateTime.ParseExact(B4, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            B4 = A4.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            B4 = "'" & B4 & "'"
        Else
            B4 = "NULL"
        End If

        'Fix missing id type 16102020 - OSH 
        SQL = "update tmp_penuh set " & _
                "nama = '" & Apo(Tx_Nama.Text.Trim.ToUpper) & "'," & _
                "jenis_kp = '" & IC_TYPE(Cb_NoKP.SelectedValue) & "'," & _
                "tpt_lahir = '" & Tx_Tpt_Lahir.Text.ToUpper & "'," & _
                "tkh_lahir = " & B1 & "," & _
                "warganegara = " & Cb_Warga.SelectedValue & "," & _
                "jantina = '" & Cb_Jantina.SelectedValue & "'," & _
                "bangsa = " & Cb_Bangsa.SelectedIndex & "," & _
                "etnik = " & Cb_Etnik.SelectedValue & "," & _
                "umur = " & Tx_Umur.Text & "," & _
                "agama = " & Cb_Agama.SelectedIndex & "," & _
                "t_kahwin = " & Cb_Kahwin.SelectedIndex & "," & _
                "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "'," & _
                "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "'," & _
                "tp_bandar = '" & Tx_TP_Bandar.Text.Trim.ToUpper & "'," & _
                "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "'," & _
                "tel_r = '" & Tx_Tel_R.Text.Trim & "'," & _
                "tel_hp = '" & Tx_Tel_HP.Text.Trim & "'," & _
                "emel = '" & Tx_Emel.Text.Trim & "'," & _
                "sm_alamat = '" & Apo(Tx_SM_Alamat.Text.Trim.ToUpper) & "'," & _
                "sm_poskod = '" & Tx_SM_Poskod.Text.Trim & "'," & _
                "sm_bandar = '" & Tx_SM_Bandar.Text.Trim.ToUpper & "'," & _
                "sm_negeri = '" & Cb_SM_Negeri.SelectedItem.Value & "'," & _
                "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "'," & _
                "tajaan = '" & Cb_Tajaan.SelectedItem.Value & "'," & _
                "sesi_bulan = " & Cb_Sesi_Bulan.SelectedIndex & "," & _
                "sesi_tahun = '" & Cb_Sesi_Tahun.SelectedItem.Text & "'," & _
                "tkh_latihan_mula = " & B2 & "," & _
                "tkh_latihan_tamat = " & B3 & "," & _
                "tkh_periksa_akhir = " & B4 & "," & _
                "log_status = '" & Cb_Status.SelectedItem.Value & "'," & _
                "log_catatan = '" & Apo(Tx_Catatan.Text.Trim.ToUpper) & "'," & _
                "ss1 = " & SSemak(0) & "," & _
                "ss2 = " & SSemak(1) & "," & _
                "ss3 = " & SSemak(2) & "," & _
                "ss4 = " & SSemak(3) & "," & _
                "ss5 = " & SSemak(4) & "," & _
                "ss6 = " & SSemak(5) & " " & _
                "where nokp = '" & Session("NOKP") & "'"

        'Comment Original 30102018 - OSH
        'SQL = "update tmp_penuh set " & _
        '        "nama = '" & Apo(Tx_Nama.Text.Trim.ToUpper) & "'," & _
        '        "tpt_lahir = '" & Tx_Tpt_Lahir.Text.ToUpper & "'," & _
        '        "tkh_lahir = " & Chk_Tkh(Tx_Tkh_Lahir.Text) & "," & _
        '        "warganegara = " & Cb_Warga.SelectedValue & "," & _
        '        "jantina = '" & Cb_Jantina.SelectedValue & "'," & _
        '        "bangsa = " & Cb_Bangsa.SelectedIndex & "," & _
        '        "etnik = " & Cb_Etnik.SelectedValue & "," & _
        '        "umur = " & Tx_Umur.Text & "," & _
        '        "agama = " & Cb_Agama.SelectedIndex & "," & _
        '        "t_kahwin = " & Cb_Kahwin.SelectedIndex & "," & _
        '        "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "'," & _
        '        "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "'," & _
        '        "tp_bandar = '" & Tx_TP_Bandar.Text.Trim.ToUpper & "'," & _
        '        "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "'," & _
        '        "tel_r = '" & Tx_Tel_R.Text.Trim & "'," & _
        '        "tel_hp = '" & Tx_Tel_HP.Text.Trim & "'," & _
        '        "emel = '" & Tx_Emel.Text.Trim & "'," & _
        '        "sm_alamat = '" & Apo(Tx_SM_Alamat.Text.Trim.ToUpper) & "'," & _
        '        "sm_poskod = '" & Tx_SM_Poskod.Text.Trim & "'," & _
        '        "sm_bandar = '" & Tx_SM_Bandar.Text.Trim.ToUpper & "'," & _
        '        "sm_negeri = '" & Cb_SM_Negeri.SelectedItem.Value & "'," & _
        '        "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "'," & _
        '        "tajaan = '" & Cb_Tajaan.SelectedItem.Value & "'," & _
        '        "sesi_bulan = " & Cb_Sesi_Bulan.SelectedIndex & "," & _
        '        "sesi_tahun = '" & Cb_Sesi_Tahun.SelectedItem.Text & "'," & _
        '        "tkh_latihan_mula = " & Chk_Tkh(Tx_M_Latihan.Text) & "," & _
        '        "tkh_latihan_tamat = " & Chk_Tkh(Tx_T_Latihan.Text) & "," & _
        '        "tkh_periksa_akhir = " & Chk_Tkh(Tx_Periksa.Text) & "," & _
        '        "log_status = '" & Cb_Status.SelectedItem.Value & "'," & _
        '        "log_catatan = '" & Apo(Tx_Catatan.Text.Trim.ToUpper) & "'," & _
        '        "ss1 = " & SSemak(0) & "," & _
        '        "ss2 = " & SSemak(1) & "," & _
        '        "ss3 = " & SSemak(2) & "," & _
        '        "ss4 = " & SSemak(3) & "," & _
        '        "ss5 = " & SSemak(4) & "," & _
        '        "ss6 = " & SSemak(5) & " " & _
        '        "where nokp = '" & Session("NOKP") & "'"
        SQL += "; " & vbCrLf
        SQL += "delete from tmp_penuh_kelayakan where nokp = '" & Tx_NoKP.Text & "'; " & vbCrLf

        Dim i As Int16, tk As DropDownList, tt As TextBox
        For i = 0 To Gd.Rows.Count - 1
            If Not Gd.Rows.Item(i).Visible Then Exit For
            tk = Gd.Rows.Item(i).FindControl("cb_kelayakan")
            tt = Gd.Rows.Item(i).FindControl("Tx_Tkh_Layak")

            SQL += "insert tmp_penuh_kelayakan (nokp, jenis, kelayakan, tkh_kelayakan) values (" & _
            "'" & Tx_NoKP.Text.Trim & "'," & _
            "1," & _
            "'" & tk.SelectedItem.Text.Trim & "'," & _
            "" & Chk_Tkh(tt.Text) & "" & _
            ")"
            SQL += ";" & vbCrLf
        Next

        For i = 0 To GdA.Rows.Count - 1
            If Not GdA.Rows.Item(i).Visible Then Exit For
            tk = GdA.Rows.Item(i).FindControl("cb_akademik")
            tt = GdA.Rows.Item(i).FindControl("Tx_Tkh_Layak")

            SQL += "insert tmp_penuh_kelayakan (nokp, jenis, kelayakan, tkh_kelayakan) values (" & _
            "'" & Tx_NoKP.Text.Trim & "'," & _
            "2," & _
            "'" & tk.SelectedItem.Text.Trim & "'," & _
            "" & Chk_Tkh(tt.Text) & "" & _
            ")"
            SQL += ";" & vbCrLf
        Next

        'Close repeater process - backdoor 'update' to exam restricted records, cause major data errors 16102020 - OSH 
        ''Fix Datetime Format 30102018 - OSH
        'SQL += "update pelatih set " & _
        '        "nama = '" & Apo(Tx_Nama.Text.Trim.ToUpper) & "', " & _
        '        "jenis_kp = " & IC_TYPE(Cb_NoKP.SelectedValue) & "," & _
        '        "warganegara = '" & Cb_Warga.SelectedItem.Value & "', " & _
        '        "jantina = '" & Cb_Jantina.SelectedIndex & "', " & _
        '        "bangsa = '" & Cb_Bangsa.SelectedIndex & "', " & _
        '        "agama = '" & Cb_Agama.SelectedIndex & "', " & _
        '        "t_kahwin = '" & Cb_Kahwin.SelectedIndex & "', " & _
        '        "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "', " & _
        '        "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "', " & _
        '        "tp_bandar = '" & Tx_TP_Bandar.Text.Trim.ToUpper & "', " & _
        '        "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "', " & _
        '        "tel = '" & Tx_Tel_R.Text.Trim & "', " & _
        '        "emel = '" & Tx_Emel.Text.Trim & "', " & _
        '        "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "', " & _
        '        "tajaan = '" & Cb_Tajaan.SelectedItem.Value & "'," & _
        '        "sesi_bulan = '" & Cb_Sesi_Bulan.SelectedIndex & "'," & _
        '        "sesi_tahun = '" & Cb_Sesi_Tahun.SelectedItem.Text & "'," & _
        '        "tkh_latihan_mula = " & B2 & "," & _
        '        "tkh_latihan_tamat = " & B3 & "" & _
        '        "where nokp ='" & Session("NOKP") & "'"

        'Comment Original 30102018 - OSH
        'SQL += "update pelatih set " & _
        '        "nama = '" & Apo(Tx_Nama.Text.Trim.ToUpper) & "', " & _
        '        "jenis_kp = " & Cb_NoKP.SelectedIndex & ", " & _
        '        "warganegara = '" & Cb_Warga.SelectedItem.Value & "', " & _
        '        "jantina = '" & Cb_Jantina.SelectedIndex & "', " & _
        '        "bangsa = '" & Cb_Bangsa.SelectedIndex & "', " & _
        '        "agama = '" & Cb_Agama.SelectedIndex & "', " & _
        '        "t_kahwin = '" & Cb_Kahwin.SelectedIndex & "', " & _
        '        "tp_alamat = '" & Apo(Tx_TP_Alamat.Text.Trim.ToUpper) & "', " & _
        '        "tp_poskod = '" & Tx_TP_Poskod.Text.Trim & "', " & _
        '        "tp_bandar = '" & Tx_TP_Bandar.Text.Trim.ToUpper & "', " & _
        '        "tp_negeri = '" & Cb_TP_Negeri.SelectedItem.Value & "', " & _
        '        "tel = '" & Tx_Tel_R.Text.Trim & "', " & _
        '        "emel = '" & Tx_Emel.Text.Trim & "', " & _
        '        "id_kolej = '" & Cb_Kolej.SelectedItem.Value & "', " & _
        '        "tajaan = '" & Cb_Tajaan.SelectedItem.Value & "'," & _
        '        "sesi_bulan = '" & Cb_Sesi_Bulan.SelectedIndex & "'," & _
        '        "sesi_tahun = '" & Cb_Sesi_Tahun.SelectedItem.Text & "'," & _
        '        "tkh_latihan_mula = " & Chk_Tkh(Tx_M_Latihan.Text) & "," & _
        '        "tkh_latihan_tamat = " & Chk_Tkh(Tx_T_Latihan.Text) & "" & _
        '        "where nokp ='" & Session("NOKP") & "'"
        Try
            Cmd.CommandText = SQL
            Cmd.ExecuteNonQuery()
            Msg(Me, "Rekod Telah Dikemaskini...")
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
    End Sub

    Public Sub Grid_Row(ByVal X As Int16, ByVal Y As Int16)
        Dim List_Data As New DataSet
        Dim Cn As New SqlConnection(ServerId_SQL)
        Dim List_Adp As New SqlDataAdapter("select top " & X & " * from tmp_grid", Cn)

        List_Adp.Fill(List_Data, "tmp_grid")
        If Y = 1 Then Gd.DataSource = List_Data.Tables("tmp_grid") : Gd.DataBind()
        If Y = 2 Then GdA.DataSource = List_Data.Tables("tmp_grid") : GdA.DataBind()
    End Sub

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If IsPostBack Then Exit Sub

        UICulture = "en-GB"
        Culture = "en-GB"

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        If Cb_NoKP.SelectedIndex = 0 Then Tx_NoKP.MaxLength = 12

        'WARGANEGARA
        Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara ORDER BY dc_NEGARA"
        Rdr = Cmd.ExecuteReader()
        Cb_Warga.Items.Clear()
        Cb_Warga.Items.Add("")
        While Rdr.Read
            Cb_Warga.Items.Add(Rdr(0))
            Cb_Warga.Items.Item(Cb_Warga.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        'If Session("PINDA") = True Then  Else Cb_Warga.Items.FindByText("MALAYSIA").Selected = True

        'ETNIK
        Cmd.CommandText = "SELECT Dc_ETNIK, Id_ETNIK FROM PN_ETNIK ORDER BY DC_ETNIK"
        Rdr = Cmd.ExecuteReader()
        Cb_Etnik.Items.Clear()
        Cb_Etnik.Items.Add("")
        Cb_Etnik.Items.Item(0).Value = 0
        While Rdr.Read
            Cb_Etnik.Items.Add(Rdr(0))
            Cb_Etnik.Items.Item(Cb_Etnik.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'IKHTISAS
        Cmd.CommandText = "select dc_ikhtisas, id_ikhtisas from pn_ikhtisas order by id_ikhtisas"
        Rdr = Cmd.ExecuteReader()
        Cb_I.Items.Clear()
        Cb_I.Items.Add("")
        While Rdr.Read
            Cb_I.Items.Add(Rdr(0))
            Cb_I.Items.Item(Cb_I.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'AKADEMIK
        Cmd.CommandText = "select dc_akademik, id_akademik from pn_akademik order by id_akademik"
        Rdr = Cmd.ExecuteReader()
        Cb_K.Items.Clear()
        Cb_K.Items.Add("")
        While Rdr.Read
            Cb_K.Items.Add(Rdr(0))
            Cb_K.Items.Item(Cb_K.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        Grid_Row(10, 1)
        Grid_Row(10, 2)

        'NEGERI
        Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()
        Cb_TP_Negeri.Items.Clear()
        Cb_TP_Negeri.Items.Add("")
        Cb_SM_Negeri.Items.Clear()
        Cb_SM_Negeri.Items.Add("")
        While Rdr.Read
            Cb_TP_Negeri.Items.Add(Rdr(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr(1)
            Cb_SM_Negeri.Items.Add(Rdr(0))
            Cb_SM_Negeri.Items.Item(Cb_SM_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        'KOLEJ
        If Session("PINDA") = True Then
            Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ ORDER BY DC_KOLEJ"
            Rdr = Cmd.ExecuteReader()
            Cb_Kolej.Items.Clear()
            Cb_Kolej.Items.Add("")
            While Rdr.Read
                Cb_Kolej.Items.Add(Rdr(0))
                Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
            End While
            Rdr.Close()
        Else
            Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ WHERE JENIS = " & RadioButtonList1.SelectedValue & "ORDER BY DC_KOLEJ"
            Rdr = Cmd.ExecuteReader()
            Cb_Kolej.Items.Clear()
            Cb_Kolej.Items.Add("")
            While Rdr.Read
                Cb_Kolej.Items.Add(Rdr(0))
                Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
            End While
            Rdr.Close()
        End If
        
        'TAJAAN
        Cmd.CommandText = "SELECT DC_TAJAAN, Id_TAJAAN FROM PN_TAJAAN ORDER BY DC_TAJAAN"
        Rdr = Cmd.ExecuteReader()
        Cb_Tajaan.Items.Clear()
        Cb_Tajaan.Items.Add("")
        While Rdr.Read
            Cb_Tajaan.Items.Add(Rdr(0))
            Cb_Tajaan.Items.Item(Cb_Tajaan.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        Cn.Close()
        Fn_Month(Cb_Sesi_Bulan) : Fn_Year(Cb_Sesi_Tahun)        
        If Session("PINDA") = True Then Isi_Pinda()
    End Sub

    Protected Sub Button1_Click(ByVal sender As Object, ByVal e As EventArgs)
        Dim i As Integer, G As String ', x As Button
        i = CInt(Mid(sender.uniqueid, InStr(sender.uniqueid, "$ctl") + 4, InStr(sender.uniqueid, "$" & sender.id) - InStr(sender.uniqueid, "$ctl") - 4))
        G = Mid(sender.uniqueid, InStr(sender.uniqueid, "$Gd") + 1, InStr(sender.uniqueid, "$ctl") - InStr(sender.uniqueid, "$Gd") - 1)
        Try
            sender.visible = False
            If G = "Gd" Then Gd.Rows.Item(i - 1).Visible = True
            If G = "GdA" Then GdA.Rows.Item(i - 1).Visible = True
        Catch ex As Exception

        End Try
    End Sub

    Private Sub Gd_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowCreated
        Gd.Height = Unit.Pixel(16)
        e.Row.Cells(0).Width = Unit.Pixel(22)
        e.Row.Cells(3).Width = Unit.Pixel(22)
        If e.Row.RowIndex > -1 Then e.Row.Cells(0).Text = (e.Row.RowIndex + 1) & "."
        If e.Row.RowIndex > 0 Then e.Row.Visible = False
    End Sub

    Private Sub GdA_RowCreated(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles GdA.RowCreated
        GdA.Height = Unit.Pixel(16)
        e.Row.Cells(0).Width = Unit.Pixel(22)
        e.Row.Cells(3).Width = Unit.Pixel(22)
        If e.Row.RowIndex > -1 Then e.Row.Cells(0).Text = (e.Row.RowIndex + 1) & "."
        If e.Row.RowIndex > 0 Then e.Row.Visible = False
    End Sub

    Private Sub GdA_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles GdA.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        Dim cb As DropDownList
        cb = e.Row.FindControl("cb_akademik")

        Dim i As Int16
        For i = 0 To Cb_K.Items.Count - 1
            cb.Items.Add(Cb_K.Items(i).Text)
            cb.Items.Item(cb.Items.Count - 1).Value = Cb_K.Items(i).Value
        Next
    End Sub

    Protected Sub Cb_Bangsa_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Bangsa.SelectedIndexChanged
        If Cb_Bangsa.SelectedIndex > 4 Then Cb_Etnik.Enabled = True : Cb_Etnik.SelectedIndex = -1 Else Cb_Etnik.Enabled = False : Cb_Etnik.SelectedIndex = -1
    End Sub

    Protected Sub cmdHantar_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmdHantar.Click        
        'Mandatori field
        Dim X As String = ""

        If Cb_Jenis.Text.Trim = "" Then X += "Jenis Pendaftaran, "
        If Tx_NoKP.Text.Trim = "" Then
            If Cb_NoKP.SelectedIndex = 0 Then X += "No. Kad Pengenalan, "
            If Cb_NoKP.SelectedIndex = 1 Then X += "No. Tentera, "
            If Cb_NoKP.SelectedIndex = 2 Then X += "No. Pasport, "
        End If
        If Tx_Nama.Text.Trim = "" Then X += "Nama, "
        If Cb_Warga.Text.Trim = "" Then X += "Warganegara, "
        If Cb_Jantina.Text.Trim = "" Then X += "Jantina, "
        If Cb_Bangsa.Text.Trim = "" Then X += "Bangsa, "
        If Tx_TP_Alamat.Text.Trim = "" Then X += "Alamat Tetap, "
        If Cb_Warga.Text.Trim = "" Or Cb_Warga.SelectedItem.Text = "MALAYSIA" Then If Tx_TP_Poskod.Text.Trim = "" Then X += "Poskod, "
        If Tx_TP_Bandar.Text.Trim = "" Then X += "Bandar, "
        If Cb_Warga.Text.Trim = "" Or Cb_Warga.SelectedItem.Text = "MALAYSIA" Then
            If Cb_TP_Negeri.Text.Trim = "" Then X += "Negeri, "
        Else
            If Cb_TP_Negeri.Text.Trim = "" Then X += "Negara, "
        End If
        If Cb_Kolej.Text.Trim = "" Then X += "Kolej/Institusi, "
        If Cb_Tajaan.Text.Trim = "" Then X += "Tajaan, "
        If Cb_Sesi_Bulan.Text.Trim = "" Or Cb_Sesi_Bulan.Text.Trim = "(BULAN)" Or Cb_Sesi_Tahun.Text.Trim = "" Or Cb_Sesi_Tahun.Text.Trim = "(TAHUN)" Then X += "Sesi Pengambilan, "
        If Tx_M_Latihan.Text.Trim = "" Then X += "Tarikh Mula Latihan, "
        If Tx_T_Latihan.Text.Trim = "" Then X += "Tarikh Tamat Latihan, "
        If Tx_Periksa.Text.Trim = "" Then X += "Tarikh Peperiksaan Akhir, "
        If Cb_Status.SelectedIndex = 0 Then X += "Status, "
        'Add brith date check 15092020 - OSH
        If Tx_Tkh_Lahir.Text = "" Or Tx_Umur.Text = "0" Then X += "Tarikh Lahir,"
        If X.Trim = "" Then  Else X = Mid(X, 1, Len(X) - 2) : Msg(Me, "Sila isikan maklumat berikut:" + X) : Exit Sub

        'Check No KP length
        X = ""
        If Cb_NoKP.SelectedIndex = 0 Then If Tx_NoKP.Text.Length < 12 Then X = "Kad Pengenalan"
        If X.Trim = "" Then  Else Msg(Me, "Maklumat No " & X & " tidak lengkap") : Tx_NoKP.Focus() : Exit Sub

        If Session("PINDA") = True Then Simpan_Pinda() : Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "select nokp from tmp_penuh where nokp = '" & Tx_NoKP.Text & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Msg(Me, "Rekod Dengan No. Kad Pengenalan Ini Sudah Ada!")
            Rdr.Close()
            Exit Sub
        End If
        Rdr.Close()

        Cmd.CommandText = "select nokp from jt_penuh where nokp = '" & Tx_NoKP.Text & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Rdr.Close() : Cn.Close()
            Msg(Me, "Rekod Dengan No. Kad Pengenalan Ini Sudah Ada!")
            Exit Sub
        End If
        Rdr.Close()

        Dim SQL As String

        'Comment Original 30102018 -OSH
        'Try
        '    SQL = "insert tmp_penuh (" & _
        '        "j_daftar, nama, nokp, tkh_lahir, tpt_lahir, warganegara, jantina, bangsa, etnik, umur, agama, t_kahwin, " & _
        '        "tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel_r, tel_hp, emel, " & _
        '        "sm_alamat, sm_poskod, sm_bandar, sm_negeri, id_kolej, tajaan, " & _
        '        "sesi_bulan, sesi_tahun, tkh_latihan_mula, tkh_latihan_tamat, tkh_periksa_akhir, " & _
        '        "ss1, ss2, ss3, ss4, ss5, ss6, log_status, log_catatan, log_id, log_tkh " & _
        '        ") values (" & _
        '        "" & Cb_Jenis.SelectedItem.Value & "," & _
        '        "'" & Apo(Tx_Nama.Text.Trim) & "'," & _
        '        "'" & Replace(Tx_NoKP.Text.Trim, "-", "") & "'," & _
        '        "" & Chk_Tkh(Tx_Tkh_Lahir.Text) & "," & _
        '        "'" & Tx_Tpt_Lahir.Text.Trim & "'," & _
        '        "" & Cb_Warga.SelectedItem.Value & "," & _
        '        "'" & Cb_Jantina.SelectedIndex & "'," & _
        '        "" & Cb_Bangsa.SelectedIndex & "," & _
        '        "" & Cb_Etnik.SelectedValue & "," & _
        '        "'" & Tx_Umur.Text.Trim & "'," & _
        '        "" & Cb_Agama.SelectedIndex & "," & _
        '        "" & Cb_Kahwin.SelectedIndex & "," & _
        '        "'" & Apo(Tx_TP_Alamat.Text.Trim) & "'," & _
        '        "'" & Tx_TP_Poskod.Text.Trim & "'," & _
        '        "'" & Tx_TP_Bandar.Text.Trim & "'," & _
        '        "'" & Cb_TP_Negeri.SelectedItem.Value & "'," & _
        '        "'" & Tx_Tel_R.Text.Trim & "'," & _
        '        "'" & Tx_Tel_HP.Text.Trim & "'," & _
        '        "'" & Tx_Emel.Text.Trim & "'," & _
        '        "'" & Apo(Tx_SM_Alamat.Text.Trim) & "'," & _
        '        "'" & Tx_SM_Poskod.Text.Trim & "'," & _
        '        "'" & Tx_SM_Bandar.Text.Trim & "'," & _
        '        "'" & Cb_SM_Negeri.SelectedItem.Value & "'," & _
        '        "'" & Cb_Kolej.SelectedItem.Value & "'," & _
        '        "'" & Cb_Tajaan.SelectedItem.Value & "'," & _
        '        "" & Cb_Sesi_Bulan.SelectedIndex & "," & _
        '        "'" & Cb_Sesi_Tahun.SelectedItem.Text & "'," & _
        '        "" & Chk_Tkh(Tx_M_Latihan.Text) & "," & _
        '        "" & Chk_Tkh(Tx_T_Latihan.Text) & "," & _
        '        "" & Chk_Tkh(Tx_Periksa.Text) & "," & _
        '        "" & SSemak(0) & "," & _
        '        "" & SSemak(1) & "," & _
        '        "" & SSemak(2) & "," & _
        '        "" & SSemak(3) & "," & _
        '        "" & SSemak(4) & "," & _
        '        "" & SSemak(5) & "," & _
        '        "" & Cb_Status.SelectedIndex & "," & _
        '        "'" & Apo(Tx_Catatan.Text.Trim) & "'," & _
        '        "'" & Session("Id_PG") & "'," & _
        '        "getdate()" & _
        '        ")"

        'Fix DateTime Format Issues 30102018 - OSH

        'Tarikh Lahir
        Dim A1 As DateTime
        Dim B1 As String = ""
        B1 = Tx_Tkh_Lahir.Text.Trim
        If Tx_Tkh_Lahir.Text.Trim <> String.Empty Then
            A1 = DateTime.ParseExact(B1, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            B1 = A1.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            B1 = "'" & B1 & "'"
        Else
            B1 = "NULL"
        End If


        'Tarikh Mula Latihan
        Dim A2 As DateTime
        Dim B2 As String = ""
        B2 = Tx_M_Latihan.Text.Trim
        If Tx_M_Latihan.Text.Trim <> String.Empty Then
            A2 = DateTime.ParseExact(B2, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            B2 = A2.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            B2 = "'" & B2 & "'"
        Else
            B2 = "NULL"
        End If

        'Tarikh Tamat Latihan
        Dim A3 As DateTime
        Dim B3 As String = ""
        B3 = Tx_T_Latihan.Text.Trim
        If Tx_T_Latihan.Text.Trim <> String.Empty Then
            A3 = DateTime.ParseExact(B3, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            B3 = A3.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            B3 = "'" & B3 & "'"
        Else
            B3 = "NULL"
        End If

        'Tarikh Peperiksaan
        Dim A4 As DateTime
        Dim B4 As String = ""
        B4 = Tx_Periksa.Text.Trim
        If Tx_Periksa.Text.Trim <> String.Empty Then
            A4 = DateTime.ParseExact(B4, "dd/MM/yyyy", CultureInfo.InvariantCulture)
            B4 = A4.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture)
            B4 = "'" & B4 & "'"
        Else
            B4 = "NULL"
        End If

       


        Try
            'Comment Original 15092020 - OSH 
            'SQL = "insert tmp_penuh (" & _
            '    "j_daftar, nama, nokp, tkh_lahir, tpt_lahir, warganegara, jantina, bangsa, etnik, umur, agama, t_kahwin, " & _
            '    "tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel_r, tel_hp, emel, " & _
            '    "sm_alamat, sm_poskod, sm_bandar, sm_negeri, id_kolej, tajaan, " & _
            '    "sesi_bulan, sesi_tahun, tkh_latihan_mula, tkh_latihan_tamat, tkh_periksa_akhir, " & _
            '    "ss1, ss2, ss3, ss4, ss5, ss6, log_status, log_catatan, log_id, log_tkh " & _
            '    ") values (" & _
            '    "" & Cb_Jenis.SelectedItem.Value & "," & _
            '    "'" & Apo(Tx_Nama.Text.Trim) & "'," & _
            '    "'" & Replace(Tx_NoKP.Text.Trim, "-", "") & "'," & _
            '    "" & B1 & "," & _
            '    "'" & Tx_Tpt_Lahir.Text.Trim & "'," & _
            '    "" & Cb_Warga.SelectedItem.Value & "," & _
            '    "'" & Cb_Jantina.SelectedIndex & "'," & _
            '    "" & Cb_Bangsa.SelectedIndex & "," & _
            '    "" & Cb_Etnik.SelectedValue & "," & _
            '    "'" & Tx_Umur.Text.Trim & "'," & _
            '    "" & Cb_Agama.SelectedIndex & "," & _
            '    "" & Cb_Kahwin.SelectedIndex & "," & _
            '    "'" & Apo(Tx_TP_Alamat.Text.Trim) & "'," & _
            '    "'" & Tx_TP_Poskod.Text.Trim & "'," & _
            '    "'" & Tx_TP_Bandar.Text.Trim & "'," & _
            '    "'" & Cb_TP_Negeri.SelectedItem.Value & "'," & _
            '    "'" & Tx_Tel_R.Text.Trim & "'," & _
            '    "'" & Tx_Tel_HP.Text.Trim & "'," & _
            '    "'" & Tx_Emel.Text.Trim & "'," & _
            '    "'" & Apo(Tx_SM_Alamat.Text.Trim) & "'," & _
            '    "'" & Tx_SM_Poskod.Text.Trim & "'," & _
            '    "'" & Tx_SM_Bandar.Text.Trim & "'," & _
            '    "'" & Cb_SM_Negeri.SelectedItem.Value & "'," & _
            '    "'" & Cb_Kolej.SelectedItem.Value & "'," & _
            '    "'" & Cb_Tajaan.SelectedItem.Value & "'," & _
            '    "" & Cb_Sesi_Bulan.SelectedIndex & "," & _
            '    "'" & Cb_Sesi_Tahun.SelectedItem.Text & "'," & _
            '    "" & B2 & "," & _
            '    "" & B3 & "," & _
            '    "" & B4 & "," & _
            '    "" & SSemak(0) & "," & _
            '    "" & SSemak(1) & "," & _
            '    "" & SSemak(2) & "," & _
            '    "" & SSemak(3) & "," & _
            '    "" & SSemak(4) & "," & _
            '    "" & SSemak(5) & "," & _
            '    "" & Cb_Status.SelectedIndex & "," & _
            '    "'" & Apo(Tx_Catatan.Text.Trim) & "'," & _
            '    "'" & Session("Id_PG") & "'," & _
            '    "getdate()" & _
            '    ")"

            'Add fix identity type values 15092020 - OSH 
            SQL = "insert tmp_penuh (" & _
                "j_daftar, jenis_kp, nama, nokp, tkh_lahir, tpt_lahir, warganegara, jantina, bangsa, etnik, umur, agama, t_kahwin, " & _
                "tp_alamat, tp_poskod, tp_bandar, tp_negeri, tel_r, tel_hp, emel, " & _
                "sm_alamat, sm_poskod, sm_bandar, sm_negeri, id_kolej, tajaan, " & _
                "sesi_bulan, sesi_tahun, tkh_latihan_mula, tkh_latihan_tamat, tkh_periksa_akhir, " & _
                "ss1, ss2, ss3, ss4, ss5, ss6, log_status, log_catatan, log_id, log_tkh " & _
                ") values (" & _
                "" & Cb_Jenis.SelectedItem.Value & "," & _
                "" & IC_TYPE(Cb_NoKP.SelectedValue) & "," & _
                "'" & Apo(Tx_Nama.Text.Trim) & "'," & _
                "'" & Replace(Tx_NoKP.Text.Trim, "-", "") & "'," & _
                "" & B1 & "," & _
                "'" & Tx_Tpt_Lahir.Text.Trim & "'," & _
                "" & Cb_Warga.SelectedItem.Value & "," & _
                "'" & Cb_Jantina.SelectedIndex & "'," & _
                "" & Cb_Bangsa.SelectedIndex & "," & _
                "" & Cb_Etnik.SelectedValue & "," & _
                "'" & Tx_Umur.Text.Trim & "'," & _
                "" & Cb_Agama.SelectedIndex & "," & _
                "" & Cb_Kahwin.SelectedIndex & "," & _
                "'" & Apo(Tx_TP_Alamat.Text.Trim) & "'," & _
                "'" & Tx_TP_Poskod.Text.Trim & "'," & _
                "'" & Tx_TP_Bandar.Text.Trim & "'," & _
                "'" & Cb_TP_Negeri.SelectedItem.Value & "'," & _
                "'" & Tx_Tel_R.Text.Trim & "'," & _
                "'" & Tx_Tel_HP.Text.Trim & "'," & _
                "'" & Tx_Emel.Text.Trim & "'," & _
                "'" & Apo(Tx_SM_Alamat.Text.Trim) & "'," & _
                "'" & Tx_SM_Poskod.Text.Trim & "'," & _
                "'" & Tx_SM_Bandar.Text.Trim & "'," & _
                "'" & Cb_SM_Negeri.SelectedItem.Value & "'," & _
                "'" & Cb_Kolej.SelectedItem.Value & "'," & _
                "'" & Cb_Tajaan.SelectedItem.Value & "'," & _
                "" & Cb_Sesi_Bulan.SelectedIndex & "," & _
                "'" & Cb_Sesi_Tahun.SelectedItem.Text & "'," & _
                "" & B2 & "," & _
                "" & B3 & "," & _
                "" & B4 & "," & _
                "" & SSemak(0) & "," & _
                "" & SSemak(1) & "," & _
                "" & SSemak(2) & "," & _
                "" & SSemak(3) & "," & _
                "" & SSemak(4) & "," & _
                "" & SSemak(5) & "," & _
                "" & Cb_Status.SelectedIndex & "," & _
                "'" & Apo(Tx_Catatan.Text.Trim) & "'," & _
                "'" & Session("Id_PG") & "'," & _
                "getdate()" & _
                ")"

            SQL += ";" & vbCrLf

            Dim i As Int16, tk As DropDownList, tt As TextBox
            For i = 0 To Gd.Rows.Count - 1
                If Not Gd.Rows.Item(i).Visible Then Exit For
                tk = Gd.Rows.Item(i).FindControl("cb_kelayakan")
                tt = Gd.Rows.Item(i).FindControl("Tx_Tkh_Layak")

                SQL += "insert tmp_penuh_kelayakan (nokp, jenis, kelayakan, tkh_kelayakan) values (" & _
                "'" & Tx_NoKP.Text.Trim & "'," & _
                "1," & _
                "'" & tk.SelectedItem.Text & "'," & _
                "" & Chk_Tkh(tt.Text) & "" & _
                ")"
                SQL += ";"
            Next

            For i = 0 To GdA.Rows.Count - 1
                If Not GdA.Rows.Item(i).Visible Then Exit For
                tk = GdA.Rows.Item(i).FindControl("cb_akademik")
                tt = GdA.Rows.Item(i).FindControl("Tx_Tkh_Layak")

                SQL += "insert tmp_penuh_kelayakan (nokp, jenis, kelayakan, tkh_kelayakan) values (" & _
                "'" & Tx_NoKP.Text.Trim & "'," & _
                "2," & _
                "'" & tk.SelectedItem.Text & "'," & _
                "" & Chk_Tkh(tt.Text) & "" & _
                ")"
                SQL += ";" & vbCrLf
            Next

            '//update table PELATIH - status 
            SQL += "update pelatih set status=1 where nokp='" & Replace(Tx_NoKP.Text.Trim, "-", "") & "'"

            'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal, "Daftar Pelatih")
            If SQL = "" Then
            Else
                'Msg(Me, SQL, MsgBoxStyle.OkOnly + MsgBoxStyle.SystemModal, "Daftar Pelatih")
                Cmd.CommandText = SQL
                Cmd.ExecuteNonQuery()
            End If

            Cn.Close()
            Msg(Me, "Rekod Telah Disimpan...")
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
    End Sub

    Protected Sub cmd_Semak_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Semak.Click
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()

        Cmd.CommandText = "select nokp from tmp_penuh where nokp = '" & Tx_NoKP.Text & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Rdr.Close() : Cn.Close()
            Msg(Me, "Rekod Dengan No. Kad Pengenalan Ini Sudah Ada!")
            Exit Sub
        End If
        Rdr.Close()

        Cmd.CommandText = "select nokp from jt_penuh where nokp = '" & Tx_NoKP.Text & "'"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            Rdr.Close() : Cn.Close()
            Msg(Me, "Rekod Dengan No. Kad Pengenalan Ini Sudah Ada!")
            Exit Sub
        End If
        Rdr.Close()

        Cmd.CommandText = "select * from pelatih where nokp = '" & Tx_NoKP.Text & "' and nokp in (select nokp from xm_calon where keputusan = 'L')"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            'On Error Resume Next
            If Not IsDBNull(Rdr("j_kursus")) Then Cb_Jenis.SelectedValue = Rdr("j_kursus")
            If Not IsDBNull(Rdr("nama")) Then Tx_Nama.Text = Rdr("nama")
            'If Not IsDBNull(Rdr("nokp")) Then Tx_NoKP.Text = Rdr("nokp") 
            If Not IsDBNull(Rdr("jenis_kp")) Then Cb_NoKP.SelectedIndex = Rdr("jenis_kp")
            If Not IsDBNull(Rdr("warganegara")) Then If Rdr("warganegara") > 0 Then Cb_Warga.SelectedValue = Rdr("warganegara") 'If Not IsDBNull(Rdr("warganegara")) Then Cb_Warga.Items.FindByValue(Rdr("warganegara")
            If Not IsDBNull(Rdr("jantina")) Then If Rdr("jantina") > 0 Then Cb_Jantina.SelectedValue = Rdr("jantina") ' Cb_Jantina.Items.FindByValue(Rdr("jantina")
            If Not IsDBNull(Rdr("bangsa")) Then If Rdr("bangsa") > 0 Then Cb_Bangsa.SelectedValue = Rdr("bangsa")
            If Not IsDBNull(Rdr("agama")) Then If Rdr("agama") > 0 Then Cb_Agama.SelectedValue = Rdr("agama")
            If Not IsDBNull(Rdr("t_kahwin")) Then If Rdr("t_kahwin") > 0 Then Cb_Kahwin.SelectedValue = Rdr("t_kahwin")
            If Not IsDBNull(Rdr("tp_alamat")) Then Tx_TP_Alamat.Text = Rdr("tp_alamat")
            If Not IsDBNull(Rdr("tp_poskod")) Then Tx_TP_Poskod.Text = Rdr("tp_poskod")
            If Not IsDBNull(Rdr("tp_bandar")) Then Tx_TP_Bandar.Text = Rdr("tp_bandar")
            If Not IsDBNull(Rdr("tp_negeri")) Then If Rdr("tp_negeri") > 0 Then Cb_TP_Negeri.SelectedValue = Rdr("tp_negeri")
            If Not IsDBNull(Rdr("tel")) Then Tx_Tel_R.Text = Rdr("tel")
            If Not IsDBNull(Rdr("emel")) Then Tx_Emel.Text = Rdr("emel")
            If Not IsDBNull(Rdr("id_kolej")) Then If Rdr("id_kolej") > 0 Then Cb_Kolej.SelectedValue = Rdr("id_kolej")
            If Not IsDBNull(Rdr("tajaan")) Then If Rdr("tajaan") > 0 Then Cb_Tajaan.SelectedValue = Rdr("tajaan")
            If Not IsDBNull(Rdr("sesi_bulan")) Then If Rdr("sesi_bulan") > 0 Then Cb_Sesi_Bulan.SelectedValue = Rdr("sesi_bulan")
            If Not IsDBNull(Rdr("sesi_tahun")) Then If Rdr("sesi_tahun") > 0 Then Cb_Sesi_Tahun.SelectedValue = Rdr("sesi_tahun")
            If Not IsDBNull(Rdr("tkh_latihan_mula")) Then Tx_M_Latihan.Text = Rdr("tkh_latihan_mula")
            If Not IsDBNull(Rdr("tkh_latihan_tamat")) Then Tx_T_Latihan.Text = Rdr("tkh_latihan_tamat")
            'If Not IsDBNull(Rdr("tel_hp")) Then Tx_Tel_HP.Text = Rdr("tel_hp")
            'If Not IsDBNull(Rdr("etnik")) Then If Rdr("etnik") > 0 Then Cb_Etnik.Items.FindByValue(Rdr("etnik") : Cb_Etnik.Enabled = True
            'If Not IsDBNull(Rdr("sm_alamat")) Then Tx_SM_Alamat.Text = Rdr("sm_alamat")
            'If Not IsDBNull(Rdr("sm_poskod")) Then Tx_SM_Poskod.Text = Rdr("sm_poskod")
            'If Not IsDBNull(Rdr("sm_bandar")) Then Tx_SM_Bandar.Text = Rdr("sm_bandar")
            'If Not IsDBNull(Rdr("sm_negeri")) Then If Rdr("sm_negeri") > 0 Then Cb_SM_Negeri.Items.FindByValue(Rdr("sm_negeri")
            'If Not IsDBNull(Rdr("tpt_lahir")) Then Tx_Tkh_Lahir.Text = Rdr("tpt_lahir")
            'If Not IsDBNull(Rdr("tkh_lahir")) Then Tx_Tpt_Lahir.Text = Rdr("tkh_lahir")           
            'If Not IsDBNull(Rdr("tkh_periksa_akhir")) Then Tx_Periksa.Text = Rdr("tkh_periksa_akhir")

            'If Not IsDBNull(Rdr("ss1")) Then If Rdr("ss1") = 1 Then chk_Semak.Items(0
            'If Not IsDBNull(Rdr("ss2")) Then If Rdr("ss2") = 1 Then chk_Semak.Items(1
            'If Not IsDBNull(Rdr("ss3")) Then If Rdr("ss3") = 1 Then chk_Semak.Items(2
            'If Not IsDBNull(Rdr("ss4")) Then If Rdr("ss4") = 1 Then chk_Semak.Items(3
            'If Not IsDBNull(Rdr("ss5")) Then If Rdr("ss5") = 1 Then chk_Semak.Items(4
            'If Not IsDBNull(Rdr("ss6")) Then If Rdr("ss6") = 1 Then chk_Semak.Items(5

            'If Not IsDBNull(Rdr("log_status")) And Rdr("log_status") > 0 Then Cb_Status.Items.FindByValue(Rdr("log_status")
            'If Not IsDBNull(Rdr("log_catatan")) Then Tx_Catatan.Text = Rdr("log_catatan")
        End If
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub Tx_NoKP_TextChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Tx_NoKP.TextChanged
        Tx_Umur.Text = 0
        If Cb_NoKP.SelectedIndex = 0 Then Kira_Umur(Tx_NoKP.Text, Tx_Umur) Else Exit Sub
    End Sub

    Protected Sub Tx_Tkh_Lahir_TextChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Tx_Tkh_Lahir.TextChanged
        'Add calculate actual age 15092020 - OSH 
        Dim X As Integer
        If Tx_Tkh_Lahir.Text <> "" Then
            X = Kira_Umur_Tkh_Server(Tx_Tkh_Lahir.Text.ToString)
            If X > CInt(Tx_Umur.Text) Then
                Tx_Umur.Text = X
            End If
        End If
    End Sub

    Private Sub Gd_RowDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.GridViewRowEventArgs) Handles Gd.RowDataBound
        If e.Row.RowIndex = -1 Then Exit Sub
        Dim cb As DropDownList
        cb = e.Row.FindControl("cb_kelayakan")

        Dim i As Int16
        For i = 0 To Cb_I.Items.Count - 1
            cb.Items.Add(Cb_I.Items(i).Text)
            cb.Items.Item(cb.Items.Count - 1).Value = Cb_I.Items(i).Value
        Next
    End Sub

    Protected Sub Cb_Kolej_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Kolej.SelectedIndexChanged
        RadioButtonList1.Items(0).Selected = False : RadioButtonList1.Items(1).Selected = False : RadioButtonList1.Items(2).Selected = False

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "SELECT JENIS FROM PN_KOLEJ WHERE ID_KOLEJ =" & Cb_Kolej.Text
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            Select Case Rdr(0)
                Case 1 : RadioButtonList1.Items(0).Selected = True
                Case 2 : RadioButtonList1.Items(1).Selected = True
                Case 3 : RadioButtonList1.Items(2).Selected = True
                Case Else
            End Select
        End While
        Rdr.Close()
        Cn.Close()

        tx_Cari.Text = ""
    End Sub

    Protected Sub cmd_Padam_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Padam.Click
        Try
            Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand
            Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
            Cmd.CommandText = "delete from tmp_penuh where nokp = '" & Tx_NoKP.Text & "'"
            Cmd.ExecuteNonQuery()
            Cmd.CommandText = "delete from tmp_penuh_kelayakan where nokp = '" & Tx_NoKP.Text & "'"
            Cmd.ExecuteNonQuery()
            Session("Msg_Tajuk") = "Padam Rekod Pendaftaran Baru"
            Session("Msg_Isi") = "Rekod Telah Dipadam..."
            Response.Redirect("p0_Mesej.aspx")
        Catch ex As Exception
            Msg(Me, ex.Message)
        End Try
    End Sub

    Protected Sub Cb_NoKP_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_NoKP.SelectedIndexChanged
        Tx_NoKP.Text = ""
        With Cb_NoKP
            If .SelectedIndex = 0 Then Tx_NoKP.MaxLength = 12 : lb_NoKP.Text = "NO. KAD PENGENALAN"
            If .SelectedIndex = 1 Then Tx_NoKP.MaxLength = 8 : lb_NoKP.Text = "NO. TENTERA"
            If .SelectedIndex = 2 Then Tx_NoKP.MaxLength = 15 : lb_NoKP.Text = "NO. PASPORT"
            If Tx_NoKP.Text.Length > 0 Then Tx_NoKP.Text = Mid(Tx_NoKP.Text, 1, Tx_NoKP.MaxLength)
        End With
    End Sub

    Protected Sub Cb_Warga_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles Cb_Warga.SelectedIndexChanged
        If Cb_Warga.SelectedItem.Text = "MALAYSIA" Then tx_Negeri.Text = "NEGERI" : Fn_Negara(1) Else tx_Negeri.Text = "NEGARA" : Fn_Negara(0)
    End Sub

    Protected Sub RadioButtonList1_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs) Handles RadioButtonList1.SelectedIndexChanged
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ WHERE JENIS = " & RadioButtonList1.SelectedValue & " ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Protected Sub bt_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles bt_Cari.Click
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ WHERE DC_KOLEJ LIKE '%" & tx_Cari.Text & "%' ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Public Sub Fn_Kolej()
        Dim X As String = ""
        If RadioButtonList1.SelectedValue = 0 Then X = "" Else X = "WHERE JENIS=" & RadioButtonList1.SelectedValue & " "

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Cmd.CommandText = "SELECT DC_KOLEJ, ID_KOLEJ FROM PN_KOLEJ " & X & " ORDER BY DC_KOLEJ"
        Rdr = Cmd.ExecuteReader()
        Cb_Kolej.Items.Clear()
        Cb_Kolej.Items.Add("")
        While Rdr.Read
            Cb_Kolej.Items.Add(Rdr(0))
            Cb_Kolej.Items.Item(Cb_Kolej.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Public Sub Fn_Negara(ByVal x As Int16)
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        If x = 0 Then
            Cmd.CommandText = "SELECT Dc_NEGARA, Id_NEGARA FROM pn_negara ORDER BY dc_NEGARA"
        Else
            Cmd.CommandText = "SELECT Dc_NEGERI, Id_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        End If

        Rdr = Cmd.ExecuteReader()
        Cb_TP_Negeri.Items.Clear()
        Cb_TP_Negeri.Items.Add("")
        While Rdr.Read
            Cb_TP_Negeri.Items.Add(Rdr(0))
            Cb_TP_Negeri.Items.Item(Cb_TP_Negeri.Items.Count - 1).Value = Rdr(1)
        End While
        Rdr.Close()
        Cn.Close()
    End Sub

    Public Sub Fn_Day(ByVal X As DropDownList)
        X.Items.Add("(HARI)")
        For i = 1 To 31
            X.Items.Add(i)
        Next
    End Sub

    Public Sub Fn_Month(ByVal X As DropDownList)
        X.Items.Add("(BULAN)")
        X.Items.Add("JANUARI") : X.Items.Item(X.Items.Count - 1).Value = 1
        X.Items.Add("FEBRUARI") : X.Items.Item(X.Items.Count - 1).Value = 2
        X.Items.Add("MAC") : X.Items.Item(X.Items.Count - 1).Value = 3
        X.Items.Add("APRIL") : X.Items.Item(X.Items.Count - 1).Value = 4
        X.Items.Add("MEI") : X.Items.Item(X.Items.Count - 1).Value = 5
        X.Items.Add("JUN") : X.Items.Item(X.Items.Count - 1).Value = 6
        X.Items.Add("JULAI") : X.Items.Item(X.Items.Count - 1).Value = 7
        X.Items.Add("OGOS") : X.Items.Item(X.Items.Count - 1).Value = 8
        X.Items.Add("SEPTEMBER") : X.Items.Item(X.Items.Count - 1).Value = 9
        X.Items.Add("OKTOBER") : X.Items.Item(X.Items.Count - 1).Value = 10
        X.Items.Add("NOVEMBER") : X.Items.Item(X.Items.Count - 1).Value = 11
        X.Items.Add("DISEMBER") : X.Items.Item(X.Items.Count - 1).Value = 12
    End Sub

    Public Sub Fn_Year(ByVal X As DropDownList)
        X.Items.Add("(TAHUN)")
        For i = 0 To 60
            X.Items.Add(Year(Now) - i)
        Next
    End Sub

End Class