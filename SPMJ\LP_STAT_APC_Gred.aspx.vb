﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_APC_Gred
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'TAHUN APC
        For i = Now.Year To Now.Year - 5 Step -1
            Cb_Tahun.Items.Add(i)
        Next

        'JAWATAN
        With Cb_Jawatan
            .Items.Clear()
            .Items.Add("Jururawat Berdaftar")
            .Items.Item(.Items.Count - 1).Value = "1"
            .Items.Add("Jururawat Masyarakat")
            .Items.Item(.Items.Count - 1).Value = "2"
            .Items.Add("Penolong Jururawat")
            .Items.Item(.Items.Count - 1).Value = "3"
        End With
    End Sub

    Protected Sub cmd_Cari1_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari1.Click
        Dim tahun As Integer = Cb_Tahun.Text
        Dim j_daftar As Integer = Cb_Jawatan.SelectedValue
        Dim K, T, J, S As Long
        Dim Tajuk, Tajuk2 As String


        Tajuk = "Laporan Statistik Pengeluaran APC Bagi " & Cb_Jawatan.SelectedItem.Text & ", " & tahun
        Tajuk2 = "mengikut Negeri dan Gred Jawatan"

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds, DTs As New DataSet

        'TAJAAN
        Cmd.CommandText = "SELECT count(*)FROM PN_GRED"
        K = Cmd.ExecuteScalar : K += 3

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='" & K & "' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='" & K & "'style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>NEGERI</td>"
        Cmd.CommandText = "SELECT Id_GRED, Dc_GRED FROM PN_GRED ORDER BY Dc_GRED"
        Rdr = Cmd.ExecuteReader()
        While Rdr.Read
            Header += "    <td style='vertical-align: middle; text-align: center;'>" + Rdr(1) + "</td>"
        End While
        Rdr.Close()
        Header += "    <td style='vertical-align: middle; text-align: center;'>X</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "</tr>"
        Response.Write(Header)

        'NEGERI
        Cmd.CommandText = "SELECT Id_NEGERI, Dc_NEGERI FROM PN_NEGERI ORDER BY Dc_NEGERI"
        Rdr = Cmd.ExecuteReader()

        Ds.Load(Rdr, LoadOption.OverwriteChanges, "Negeri")
        Rdr.Close()

        Cmd.CommandText = "SELECT Id_GRED, Dc_GRED FROM PN_GRED ORDER BY Dc_GRED"
        Rdr = Cmd.ExecuteReader()
        DTs.Load(Rdr, LoadOption.OverwriteChanges, "gred")
        Rdr.Close()

        Header = ""
        For Each dr As DataRow In Ds.Tables(0).Rows
            Header += "<tr>"
            Header += "    <td>" + dr.Item(1) + "</td> "

            J = 0 : T = 0
            For Each dtr As DataRow In DTs.Tables(0).Rows
                Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gred=" & dtr.Item(0) & ")"
                Rdr = Cmd.ExecuteReader()
                If Rdr.Read Then T = Rdr(0)
                Rdr.Close()
                J += T
                Header += "    <td>" & T & "</td>"
            Next
            Cmd.CommandText = "select count(nokp) from jt_penuh_apc where apc_tahun=" & tahun & " and j_daftar=" & j_daftar & " and id_amalan in (select id_amalan from pn_tpt_amalan where negeri=" & dr.Item(0) & ") and nokp in (select nokp from jt_penuh where gred is null)"
            Rdr = Cmd.ExecuteReader()
            If Rdr.Read Then T = Rdr(0)
            Rdr.Close()
            J += T : S += J
            Header += "    <td>" & T & "</td>"
            Header += "    <td>" & J & "</td>"
            Header += "</tr>"
        Next
        Header += "<tr>"
        Header += "    <td colspan='" & K - 1 & "' style='vertical-align: middle; text-align: left;'>JUMLAH KESELURUHAN</td><td>" & S & "</td>"
        Header += "</tr>"
        Response.Write(Header)
        Response.Write("</table>")

        Response.End()
        Response.Flush()

    End Sub


End Class