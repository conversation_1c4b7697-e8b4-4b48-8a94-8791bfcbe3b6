﻿Imports System.IO
Imports System.Data.OleDb

Partial Public Class LP_STAT_TPC_Jawatan
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub

        'Add Access Check 23072018 -OSH
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")

        'TAHUN APC
        For i = Now.Year To Now.Year - 1 Step -1
            Cb_Tahun.Items.Add(i)
        Next

        'Status
        With Cb_Status
            .Items.Clear()
            .Items.Add("BERDAFTAR")
            .Items.Item(.Items.Count - 1).Value = "1"
            .Items.Add("BELUM BERDAFTAR")
            .Items.Item(.Items.Count - 1).Value = "2"
        End With
    End Sub
  
    Protected Sub cmd_Cari_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Cari.Click
        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim SQL, tpc, tkh, majikan, stat As String
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn

        tpc = "" : tkh = "" : majikan = "" : stat = ""
        If Cb_Status.SelectedValue = 1 Then tpc = "jt_tpc" : majikan = "jt_tpc_majikan" : tkh = "tkh_daftar" : stat = "and status=1" Else tpc = "tmp_tpc" : majikan = "tmp_tpc_majikan" : tkh = "mohon_tkh"

        SQL = "delete from temp_ret; "
        SQL += "insert into temp_ret "
        SQL += "select tpt_amalan as negara, null as b1, null as b2, count(t.nokp) as b3, null as b4, null as b5, null as b6, null as b7, null as b8, null as b9, null as b10, null as b11, null as b12, null as x, null as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and j_daftar=1 group by tpt_amalan "
        SQL += "union select tpt_amalan as negara, count(t.nokp) as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7, null as b8, null as b9, null as b10, null as b11, null as b12, null as x, null as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and j_daftar=1 and jantina=1 group by tpt_amalan "
        SQL += "union select tpt_amalan as negara, null as b1, count(t.nokp) as b2, null as b3, null as b4, null as b5, null as b6, null as b7, null as b8, null as b9, null as b10, null as b11, null as b12, null as x, null as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and j_daftar=1 and jantina=2 group by tpt_amalan "
        'b1-b3
        SQL += "insert into temp_ret "
        SQL += "select tpt_amalan as negara, null as b1, null as b2, null as b3, null as b4, null as b5, count(t.nokp) as b6, null as b7, null as b8, null as b9, null as b10, null as b11, null as b12, null as x, null as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and j_daftar=2 group by tpt_amalan "
        SQL += "union select tpt_amalan as negara, null as b1, null as b2, null as b3, count(t.nokp) as b4, null as b5, null as b6, null as b7, null as b8, null as b9, null as b10, null as b11, null as b12, null as x, null as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and j_daftar=2 and jantina=1 group by tpt_amalan "
        SQL += "union select tpt_amalan as negara, null as b1, null as b2, null as b3, null as b4, count(t.nokp) as b5, null as b6, null as b7, null as b8, null as b9, null as b10, null as b11, null as b12, null as x, null as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and j_daftar=2 and jantina=2 group by tpt_amalan "
        'b4-b6
        SQL += "insert into temp_ret "
        SQL += "select tpt_amalan as negara, null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7, null as b8, count(t.nokp) as b9, null as b10, null as b11, null as b12, null as x, null as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and j_daftar=3 group by tpt_amalan "
        SQL += "union select tpt_amalan as negara, null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, count(t.nokp) as b7, null as b8, null as b9, null as b10, null as b11, null as b12, null as x, null as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and j_daftar=3 and jantina=1 group by tpt_amalan "
        SQL += "union select tpt_amalan as negara, null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7, count(t.nokp) as b8, null as b9, null as b10, null as b11, null as b12, null as x, null as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and j_daftar=3 and jantina=2 group by tpt_amalan "
        'b7-b9
        SQL += "insert into temp_ret "
        SQL += "select tpt_amalan as negara, null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7, null as b8, null as b9, null as b10, null as b11, count(t.nokp) as b12, null as x, null as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and j_daftar=4 group by tpt_amalan "
        SQL += "union select tpt_amalan as negara, null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7, null as b8, null as b9, count(t.nokp) as b10, null as b11, null as b12, null as x, null as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and j_daftar=4 and jantina=1 group by tpt_amalan "
        SQL += "union select tpt_amalan as negara, null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7, null as b8, null as b9, null as b10, count(t.nokp) as b11, null as b12, null as x, null as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and j_daftar=4 and jantina=2 group by tpt_amalan "
        'b10-b12"
        SQL += "insert into temp_ret "
        SQL += "select tpt_amalan as negara, null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7, null as b8, null as b9, null as b10, null as b11, null as b12, null as x, null as nokp, count(t.nokp) as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " group by tpt_amalan "
        SQL += "union select tpt_amalan as negara, null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7, null as b8, null as b9, null as b10, null as b11, null as b12, count(t.nokp) as x, null as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and jantina=1 group by tpt_amalan "
        SQL += "union select tpt_amalan as negara, null as b1, null as b2, null as b3, null as b4, null as b5, null as b6, null as b7, null as b8, null as b9, null as b10, null as b11, null as b12, null as x, count(t.nokp) as nokp, null as total "
        SQL += "from " & tpc & " t inner join " & majikan & " tm on t.nokp=tm.nokp where year(" & tkh & ")=" & Cb_Tahun.Text & " " & stat & " and jantina=2 group by tpt_amalan "
        'x-total
        Cmd.CommandText = SQL
        Cmd.ExecuteNonQuery()
        Cn.Close()

        Jana()
    End Sub

    Public Sub Jana()
        Dim Tajuk, Tajuk2 As String
        Dim a1, a2, a3, a4, a, b1, b2, b3, b4, b, c1, c2, c3, c4, c As Integer
        Dim d1, d2, d3, d4, d, e1, e2, e3, e4, e As Integer

        Tajuk = "LAPORAN STATISTIK PENDAFTARAN TPC " & Cb_Status.SelectedItem.Text & ", TAHUN " & Cb_Tahun.Text
        Tajuk2 = "MENGIKUT MAJIKAN, JAWATAN DAN JANTINA"

        Response.Clear()
        Response.ContentType = "application/ms-excel"
        Dim strFileName As String = "GenerateDocument" + ".xls"
        HttpContext.Current.Response.AddHeader("Content-Disposition", "inline;filename=" + strFileName)

        Dim Header As String
        Header = "<table border='1' cellpadding='-1' cellspacing='-1' style='width:80%;'>"
        Header += "<tr>"
        Header += "    <td colspan='21' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td colspan='21' style='height:25px; vertical-align: middle; text-align: center;'>" & Tajuk2 & "</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td rowspan='2' style='vertical-align: middle; text-align: left;'>MAJIKAN</td>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: center;'>JURURAWAT TERLATIH</td>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: center;'>LATIHAN ELEKTIF</td>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: center;'>PENGAJAR JURURAWAT</td>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: center;'>INSTRUKTOR KLINIKAL</td>"
        Header += "    <td colspan='4' style='vertical-align: middle; text-align: center;'>JUMLAH KESELURUHAN</td>"
        Header += "</tr>"
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>L</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>P</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>X</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>L</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>P</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>X</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>L</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>P</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>X</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>L</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>P</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>X</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>L</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>P</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>X</td>"
        Header += "    <td style='vertical-align: middle; text-align: center;'>JUMLAH</td>"
        Header += "</tr>"
        Response.Write(Header)

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim Rdr As OleDbDataReader
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn
        Dim Ds As New DataSet

        Cmd.CommandText = "select dc_amalan, isnull(sum(b1),'0'), isnull(sum(b2),'0'), isnull(sum(b3),'0'), isnull(sum(b4),'0'), isnull(sum(b5),'0'), isnull(sum(b6),'0'), isnull(sum(b7),'0'), " & _
                          "isnull(sum(b8),'0'), isnull(sum(b9),'0'), isnull(sum(b10),'0'), isnull(sum(b11),'0') ,isnull(sum(b12),'0'), isnull(sum(x),'0'), isnull(sum(nokp),'0'), isnull(sum(total),'0') " & _
                          "from temp_ret ret left outer join pn_tpt_amalan pta on ret.negara=pta.id_amalan group by dc_amalan order by dc_amalan "
        Rdr = Cmd.ExecuteReader()
        Ds.Load(Rdr, LoadOption.OverwriteChanges, "temp_ret")
        Rdr.Close()

        Header = ""
        For Each dr As DataRow In Ds.Tables(0).Rows
            Header += "<tr>"
            Header += "    <td>" & dr.Item(0) & "</td> "
            Header += "    <td>" & dr.Item(1) & "</td> " : a1 += dr.Item(1)
            Header += "    <td>" & dr.Item(2) & "</td> " : a2 += dr.Item(2)
            a = CInt(dr.Item(3)) - CInt(dr.Item(2)) - CInt(dr.Item(1))
            Header += "    <td>" & a & "</td> " : a3 += a
            Header += "    <td>" & dr.Item(3) & "</td> " : a4 += dr.Item(3)

            Header += "    <td>" & dr.Item(4) & "</td> " : b1 += dr.Item(4)
            Header += "    <td>" & dr.Item(5) & "</td> " : b2 += dr.Item(5)
            b = CInt(dr.Item(6)) - CInt(dr.Item(5)) - CInt(dr.Item(4))
            Header += "    <td>" & b & "</td> " : b3 += b
            Header += "    <td>" & dr.Item(6) & "</td> " : b4 += dr.Item(6)

            Header += "    <td>" & dr.Item(7) & "</td> " : c1 += dr.Item(7)
            Header += "    <td>" & dr.Item(8) & "</td> " : c2 += dr.Item(8)
            c = CInt(dr.Item(9)) - CInt(dr.Item(8)) - CInt(dr.Item(7))
            Header += "    <td>" & c & "</td> " : c3 += c
            Header += "    <td>" & dr.Item(9) & "</td> " : c4 += dr.Item(9)

            Header += "    <td>" & dr.Item(10) & "</td> " : d1 += dr.Item(10)
            Header += "    <td>" & dr.Item(11) & "</td> " : d2 += dr.Item(11)
            d = CInt(dr.Item(12)) - CInt(dr.Item(11)) - CInt(dr.Item(10))
            Header += "    <td>" & d & "</td> " : d3 += d
            Header += "    <td>" & dr.Item(12) & "</td> " : d4 += dr.Item(12)

            Header += "    <td>" & dr.Item(13) & "</td> " : e1 += dr.Item(13)
            Header += "    <td>" & dr.Item(14) & "</td> " : e2 += dr.Item(14)
            e = CInt(dr.Item(15)) - CInt(dr.Item(14)) - CInt(dr.Item(13))
            Header += "    <td>" & e & "</td> " : e3 += e
            Header += "    <td>" & dr.Item(15) & "</td> " : e4 += dr.Item(15)
            Header += "</tr>"
        Next
        Header += "<tr>"
        Header += "    <td style='vertical-align: middle; text-align: left;'>JUMLAH KESELURUHAN</td>"
        Header += "    <td>" & a1 & "</td>"
        Header += "    <td>" & a2 & "</td>"
        Header += "    <td>" & a3 & "</td>"
        Header += "    <td>" & a4 & "</td>"
        Header += "    <td>" & b1 & "</td>"
        Header += "    <td>" & b2 & "</td>"
        Header += "    <td>" & b3 & "</td>"
        Header += "    <td>" & b4 & "</td>"
        Header += "    <td>" & c1 & "</td>"
        Header += "    <td>" & c2 & "</td>"
        Header += "    <td>" & c3 & "</td>"
        Header += "    <td>" & c4 & "</td>"
        Header += "    <td>" & d1 & "</td>"
        Header += "    <td>" & d2 & "</td>"
        Header += "    <td>" & d3 & "</td>"
        Header += "    <td>" & d4 & "</td>"
        Header += "    <td>" & e1 & "</td>"
        Header += "    <td>" & e2 & "</td>"
        Header += "    <td>" & e3 & "</td>"
        Header += "    <td>" & e4 & "</td>"
        Header += "</tr>"
        Cn.Close()
        Header += "</table>"
        Response.Write(Header)

        Response.End()
        Response.Flush()
    End Sub
End Class