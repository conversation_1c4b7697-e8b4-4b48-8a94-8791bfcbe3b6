﻿Imports System.Globalization
Imports System.Threading
Imports System.Data.OleDb
Imports System.Data.SqlClient
Imports CrystalDecisions.Shared

Partial Public Class P1_StatXM
    Inherits System.Web.UI.Page

    Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        If IsPostBack Then Exit Sub
        If Session("Id_PG") = "" Then Session.Abandon() : Response.Redirect("p0_Login.aspx")
        If Not Akses_Pg("P1", "Laporan", Session("AKSES"), Session("MODUL")) Then
            Session("Msg_Tajuk") = "PEPERIKSAAN"
            Session("Msg_Isi") = "Akses Terhad"
            Response.Redirect("p0_Mesej.aspx")
            Exit Sub
        End If
    End Sub

    Protected Sub cmd_Jana_Click(ByVal sender As Object, ByVal e As EventArgs) Handles cmd_Jana.Click
        If Cb_Jenis.Text.Trim = "" Then Cb_Jenis.Focus() : Exit Sub
        If Tx_Tkh.Text.Trim = "" Or Not IsDate(Tx_Tkh.Text) Then Tx_Tkh.Focus() : Exit Sub

        Dim Cn As New OleDbConnection : Dim Cmd As New OleDbCommand : Dim SQL, id, tkh_xm As String
        Cn.ConnectionString = ServerId : Cn.Open() : Cmd.Connection = Cn : Dim Rdr As OleDbDataReader

        Cmd.CommandText = "select top 1 id_xm, convert(char(12), t1_tkh, 103) as 'tkh' from pn_xm where j_xm= " & Cb_Jenis.SelectedIndex & " order by tahun desc, siri desc, j_xm desc"
        Rdr = Cmd.ExecuteReader()
        If Rdr.Read Then
            id = Rdr(0)
            tkh_xm = "( TARIKH PEPERIKSAAN : " & Rdr(1) & " ; TARIKH KEPUTUSAN : " & Tx_Tkh.Text & " )"
        Else
            id = ""
            tkh_xm = "( TARIKH PEPERIKSAAN : - ; TARIKH KEPUTUSAN : " & Tx_Tkh.Text & " )"
        End If

        Rdr.Close()

        SQL = "delete from z; "
        SQL += "insert into z "
        SQL += "select distinct(p.id_kolej) as kolej, count(xc.nokp) as 'ag', null as lulus, null as gagal, null as tumpang, null as m80, null as m45, null as m0 from xm_calon xc " & _
                " inner join pn_kolej pk on xc.id_pusat=pk.id_kolej " & _
                " inner join pn_xm px on xc.id_xm=px.id_xm" & _
                " inner join pelatih p on p.nokp=xc.nokp  " & _
                " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM " & _
                " where xc.id_xm=" & id & " and xc.keputusan is not null and  not(xc.keputusan='T') and ulangan is not null group by p.id_kolej "
        SQL += "union select distinct(p.id_kolej) as kolej, null as ag, count(xc.nokp) as lulus, null as gagal, null as tumpang, null as m80, null as m45, null as m0 from xm_calon xc  " & _
                " inner join pn_kolej pk on xc.id_pusat=pk.id_kolej " & _
                " inner join pn_xm px on xc.id_xm=px.id_xm" & _
                " inner join pelatih p on p.nokp=xc.nokp    " & _
                " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM " & _
                " where xc.id_xm=" & id & " and xc.keputusan is not null  and  not(xc.keputusan='T') and ulangan is not null and xc.keputusan='L' group by p.id_kolej "
        SQL += "union select distinct(p.id_kolej) as kolej, null as ag, null as lulus, count(xc.nokp) as 'gagal', null as tumpang, null as m80, null as m45, null as m0 from xm_calon xc  " & _
                " inner join pn_kolej pk on xc.id_pusat=pk.id_kolej " & _
                " inner join pn_xm px on xc.id_xm=px.id_xm" & _
                " inner join pelatih p on p.nokp=xc.nokp    " & _
                " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM " & _
                " where xc.id_xm=" & id & " and xc.keputusan is not null  and  not(xc.keputusan='T') and ulangan is not null and xc.keputusan='G' group by p.id_kolej "
        SQL += "union select distinct(p.id_kolej) as kolej, null as ag, null as lulus, null as gagal, count(xc.nokp) as 'tumpang', null as m80, null as m45, null as m0 from xm_calon xc  " & _
                " inner join pn_kolej pk on xc.id_pusat=pk.id_kolej " & _
                " inner join pn_xm px on xc.id_xm=px.id_xm" & _
                " inner join pelatih p on p.nokp=xc.nokp    " & _
                " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM " & _
                " where xc.id_xm=" & id & " and xc.keputusan is not null  and  not(xc.keputusan='T') and ulangan is not null and xc.id_pusat<>p.id_kolej group by p.id_kolej "
        SQL += "union select distinct(p.id_kolej) as kolej, null as ag, null as lulus, null as gagal, null as tumpang, count(xc.nokp) as 'm80', null as m45, null as m0 from xm_calon xc  " & _
                " inner join pn_kolej pk on xc.id_pusat=pk.id_kolej " & _
                " inner join pn_xm px on xc.id_xm=px.id_xm" & _
                " inner join pelatih p on p.nokp=xc.nokp    " & _
                " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM " & _
                " where xc.id_xm=" & id & " and xc.keputusan is not null  and  not(xc.keputusan='T') and ulangan is not null and xc.markah_jum>=80 group by p.id_kolej "
        SQL += "union select distinct(p.id_kolej) as kolej, null as ag, null as lulus, null as gagal, null as tumpang, null as m80, count(xc.nokp) as 'm45', null as m0 from xm_calon xc  " & _
                " inner join pn_kolej pk on xc.id_pusat=pk.id_kolej " & _
                " inner join pn_xm px on xc.id_xm=px.id_xm" & _
                " inner join pelatih p on p.nokp=xc.nokp    " & _
                " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM " & _
                " where xc.id_xm=" & id & " and xc.keputusan is not null  and  not(xc.keputusan='T') and ulangan is not null and xc.markah_jum between 45 and 79.99 group by p.id_kolej "
        SQL += "union select distinct(p.id_kolej) as kolej, null as ag, null as lulus, null as gagal, null as tumpang, null as 'm80', null as m45, count(xc.nokp) as m0 from xm_calon xc  " & _
                " inner join pn_kolej pk on xc.id_pusat=pk.id_kolej " & _
                " inner join pn_xm px on xc.id_xm=px.id_xm" & _
                " inner join pelatih p on p.nokp=xc.nokp    " & _
                " and (case when p.j_kursus in (1,5,8) then  1 when p.j_kursus=2 then 2 when p.j_kursus=3 then 3 when p.j_kursus=4 then  4  end ) =px.j_XM " & _
                " where xc.id_xm=" & id & " and xc.keputusan is not null  and  not(xc.keputusan='T') and ulangan is not null and xc.markah_jum<45 group by p.id_kolej "


        'SQL += "select distinct(p.id_kolej) as kolej, count(xc.nokp) as 'ag', null as lulus, null as gagal, null as tumpang, null as m80, null as m45, null as m0 from xm_calon xc inner join pelatih p on p.nokp=xc.nokp inner join " & _
        '      "pn_kolej pk on xc.id_pusat=pk.id_kolej where xc.id_xm=" & id & " and xc.keputusan is not null and  not(xc.keputusan='T') and ulangan is not null group by p.id_kolej "
        'SQL += "union select distinct(p.id_kolej) as kolej, null as ag, count(xc.nokp) as lulus, null as gagal, null as tumpang, null as m80, null as m45, null as m0 from xm_calon xc inner join pelatih p on p.nokp=xc.nokp inner join " & _
        '        "pn_kolej pk on xc.id_pusat=pk.id_kolej where xc.id_xm=" & id & " and xc.keputusan is not null  and  not(xc.keputusan='T') and ulangan is not null and xc.keputusan='L' group by p.id_kolej "
        'SQL += "union select distinct(p.id_kolej) as kolej, null as ag, null as lulus, count(xc.nokp) as 'gagal', null as tumpang, null as m80, null as m45, null as m0 from xm_calon xc inner join pelatih p on p.nokp=xc.nokp inner join " & _
        '        "pn_kolej pk on xc.id_pusat=pk.id_kolej where xc.id_xm=" & id & " and xc.keputusan is not null  and  not(xc.keputusan='T') and ulangan is not null and xc.keputusan='G' group by p.id_kolej "
        'SQL += "union select distinct(p.id_kolej) as kolej, null as ag, null as lulus, null as gagal, count(xc.nokp) as 'tumpang', null as m80, null as m45, null as m0 from xm_calon xc inner join pelatih p on p.nokp=xc.nokp inner join " & _
        '        "pn_kolej pk on xc.id_pusat=pk.id_kolej where xc.id_xm=" & id & " and xc.keputusan is not null  and  not(xc.keputusan='T') and ulangan is not null and xc.id_pusat<>p.id_kolej group by p.id_kolej "
        'SQL += "union select distinct(p.id_kolej) as kolej, null as ag, null as lulus, null as gagal, null as tumpang, count(xc.nokp) as 'm80', null as m45, null as m0 from xm_calon xc inner join pelatih p on p.nokp=xc.nokp inner join " & _
        '        "pn_kolej pk on xc.id_pusat=pk.id_kolej where xc.id_xm=" & id & " and xc.keputusan is not null  and  not(xc.keputusan='T') and ulangan is not null and xc.markah_jum>=80 group by p.id_kolej "
        'SQL += "union select distinct(p.id_kolej) as kolej, null as ag, null as lulus, null as gagal, null as tumpang, null as m80, count(xc.nokp) as 'm45', null as m0 from xm_calon xc inner join pelatih p on p.nokp=xc.nokp inner join " & _
        '        "pn_kolej pk on xc.id_pusat=pk.id_kolej where xc.id_xm=" & id & " and xc.keputusan is not null  and  not(xc.keputusan='T') and ulangan is not null and xc.markah_jum between 45 and 79.99 group by p.id_kolej "
        'SQL += "union select distinct(p.id_kolej) as kolej, null as ag, null as lulus, null as gagal, null as tumpang, null as 'm80', null as m45, count(xc.nokp) as m0 from xm_calon xc inner join pelatih p on p.nokp=xc.nokp inner join " & _
        '        "pn_kolej pk on xc.id_pusat=pk.id_kolej where xc.id_xm=" & id & " and xc.keputusan is not null  and  not(xc.keputusan='T') and ulangan is not null and xc.markah_jum<45 group by p.id_kolej "

        Cmd.CommandText = SQL
        Cmd.ExecuteNonQuery()
        Cn.Close()
        Session("Var_0") = CInt(Cb_Jenis.SelectedValue)
        Session("Var_1") = tkh_xm
        Session("Lpr_Nama") = "Stat_XM"

        Response.Write("<script language='javascript'>win=window.open('P6_Laporan.aspx',null,'width=1000,height=700,top='+ (screen.height-600)/3 +',left='+ (screen.width-800)/2 +'','true');</script>")
    End Sub
End Class